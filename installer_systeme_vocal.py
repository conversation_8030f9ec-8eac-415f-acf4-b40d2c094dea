#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎤 INSTALLATEUR SYSTÈME VOCAL JARVIS - JEAN-LUC PASSAVE
Installation et test des composants vocaux réels
"""

import subprocess
import sys
import os

def installer_dependances_vocales():
    """INSTALLER LES DÉPENDANCES VOCALES RÉELLES - JEAN-LUC PASSAVE"""
    print("🎤 INSTALLATION SYSTÈME VOCAL JARVIS...")
    print("=" * 60)
    
    # Liste des packages nécessaires
    packages = [
        "SpeechRecognition",
        "pyttsx3",
        "pyaudio",
        "google-cloud-speech",  # Optionnel pour meilleure reconnaissance
        "pygame"  # Pour contrôle audio avancé
    ]
    
    for package in packages:
        try:
            print(f"📦 Installation de {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installé avec succès")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erreur installation {package}: {e}")
            if package == "pyaudio":
                print("💡 Pour pyaudio sur macOS, essayez: brew install portaudio")
                print("💡 Puis: pip install pyaudio")

def tester_reconnaissance_vocale():
    """TESTER LA RECONNAISSANCE VOCALE RÉELLE"""
    print("\n🎤 TEST RECONNAISSANCE VOCALE...")
    print("=" * 40)
    
    try:
        import speech_recognition as sr
        
        # Initialiser le recognizer et le microphone
        r = sr.Recognizer()
        m = sr.Microphone()
        
        print("🎤 Calibrage du microphone...")
        with m as source:
            r.adjust_for_ambient_noise(source)
        
        print("✅ Microphone calibré")
        print("🎤 Dites quelque chose (5 secondes)...")
        
        with m as source:
            audio = r.listen(source, timeout=5, phrase_time_limit=5)
        
        print("🔄 Reconnaissance en cours...")
        
        # Essayer Google Speech Recognition
        try:
            text = r.recognize_google(audio, language='fr-FR')
            print(f"✅ RECONNAISSANCE RÉUSSIE: '{text}'")
            return True
        except sr.UnknownValueError:
            print("❌ Impossible de comprendre l'audio")
            return False
        except sr.RequestError as e:
            print(f"❌ Erreur service reconnaissance: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur test reconnaissance: {e}")
        return False

def tester_synthese_vocale():
    """TESTER LA SYNTHÈSE VOCALE RÉELLE"""
    print("\n🔊 TEST SYNTHÈSE VOCALE...")
    print("=" * 40)
    
    try:
        import pyttsx3
        
        # Initialiser le moteur TTS
        engine = pyttsx3.init()
        
        # Configuration voix française
        voices = engine.getProperty('voices')
        print(f"🗣️ Voix disponibles: {len(voices)}")
        
        for i, voice in enumerate(voices):
            print(f"  {i}: {voice.name} ({voice.id})")
            if 'french' in voice.name.lower() or 'fr' in voice.id.lower():
                engine.setProperty('voice', voice.id)
                print(f"✅ Voix française sélectionnée: {voice.name}")
                break
        
        # Configuration paramètres
        engine.setProperty('rate', 200)  # Vitesse
        engine.setProperty('volume', 0.9)  # Volume
        
        # Test de synthèse
        test_text = "Bonjour Jean-Luc Passave, je suis JARVIS et mon système vocal fonctionne parfaitement !"
        print(f"🔊 Synthèse du texte: '{test_text[:50]}...'")
        
        engine.say(test_text)
        engine.runAndWait()
        
        print("✅ SYNTHÈSE VOCALE RÉUSSIE")
        return True
        
    except ImportError as e:
        print(f"❌ Module manquant: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur test synthèse: {e}")
        return False

def corriger_systeme_vocal_jarvis():
    """CORRIGER LE SYSTÈME VOCAL DANS JARVIS"""
    print("\n🔧 CORRECTION SYSTÈME VOCAL JARVIS...")
    print("=" * 50)
    
    # Code de correction pour le système vocal
    correction_code = '''
# 🎤 SYSTÈME VOCAL RÉEL CORRIGÉ - JEAN-LUC PASSAVE

def activer_reconnaissance_vocale_reelle():
    """RECONNAISSANCE VOCALE RÉELLE - PLUS DE SIMULATION"""
    try:
        import speech_recognition as sr
        import threading
        
        r = sr.Recognizer()
        m = sr.Microphone()
        
        # Calibrage
        with m as source:
            r.adjust_for_ambient_noise(source)
        
        def ecoute_continue():
            while True:
                try:
                    with m as source:
                        audio = r.listen(source, timeout=1, phrase_time_limit=5)
                    
                    text = r.recognize_google(audio, language='fr-FR')
                    
                    if "jarvis" in text.lower():
                        print(f"🎤 COMMANDE DÉTECTÉE: {text}")
                        # Traiter la commande avec JARVIS
                        
                except:
                    pass
        
        # Lancer en arrière-plan
        thread = threading.Thread(target=ecoute_continue, daemon=True)
        thread.start()
        
        return "✅ RECONNAISSANCE VOCALE RÉELLE ACTIVÉE"
        
    except Exception as e:
        return f"❌ Erreur: {e}"

def activer_synthese_vocale_reelle(texte):
    """SYNTHÈSE VOCALE RÉELLE - PLUS DE SIMULATION"""
    try:
        import pyttsx3
        
        engine = pyttsx3.init()
        
        # Voix française
        voices = engine.getProperty('voices')
        for voice in voices:
            if 'french' in voice.name.lower() or 'fr' in voice.id.lower():
                engine.setProperty('voice', voice.id)
                break
        
        engine.setProperty('rate', 200)
        engine.setProperty('volume', 0.9)
        
        # PARLER VRAIMENT
        engine.say(texte)
        engine.runAndWait()
        
        return "✅ JARVIS A PARLÉ RÉELLEMENT"
        
    except Exception as e:
        return f"❌ Erreur: {e}"
'''
    
    # Sauvegarder le code de correction
    with open("correction_vocal_jarvis.py", "w", encoding="utf-8") as f:
        f.write(correction_code)
    
    print("✅ Code de correction sauvegardé: correction_vocal_jarvis.py")
    print("💡 Intégrez ce code dans jarvis_architecture_multi_fenetres.py")

def main():
    """FONCTION PRINCIPALE"""
    print("🚀 INSTALLATEUR SYSTÈME VOCAL JARVIS - JEAN-LUC PASSAVE")
    print("=" * 70)
    
    # 1. Installer les dépendances
    installer_dependances_vocales()
    
    # 2. Tester reconnaissance vocale
    reconnaissance_ok = tester_reconnaissance_vocale()
    
    # 3. Tester synthèse vocale
    synthese_ok = tester_synthese_vocale()
    
    # 4. Générer correction
    corriger_systeme_vocal_jarvis()
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ INSTALLATION SYSTÈME VOCAL")
    print("=" * 70)
    print(f"🎤 Reconnaissance vocale: {'✅ OK' if reconnaissance_ok else '❌ ERREUR'}")
    print(f"🔊 Synthèse vocale: {'✅ OK' if synthese_ok else '❌ ERREUR'}")
    print("🔧 Code de correction: ✅ GÉNÉRÉ")
    
    if reconnaissance_ok and synthese_ok:
        print("\n🎉 SYSTÈME VOCAL JARVIS PRÊT !")
        print("💡 Intégrez le code de correction dans votre application")
    else:
        print("\n⚠️ PROBLÈMES DÉTECTÉS")
        print("💡 Vérifiez les dépendances et permissions microphone")

if __name__ == "__main__":
    main()
