# FICHIER 3 - Erreurs timestamp répétitives
# PROBLÈME : Erreurs parsing timestamp qui polluent les logs

# ERREURS OBSERVÉES EN TEMPS RÉEL :
"""
⚠️ Erreur parsing timestamp 1750681476.132413: 'float' object has no attribute 'replace'
⚠️ Erreur parsing timestamp 1750679676.132413: 'float' object has no attribute 'replace'
⚠️ Erreur parsing timestamp 1750677876.132413: 'float' object has no attribute 'replace'
⚠️ Erreur parsing timestamp 1750676076.132413: 'float' object has no attribute 'replace'
⚠️ Erreur parsing timestamp 1750674276.132413: 'float' object has no attribute 'replace'
... (répété 50+ fois au démarrage)
"""

# PROBLÈME IDENTIFIÉ :
# Le code essaie d'appeler .replace() sur un float (timestamp Unix)
# Mais .replace() est une méthode de string, pas de float !

# CODE PROBLÉMATIQUE (approximatif) :
def parse_timestamp(timestamp):
    try:
        # ERREUR : timestamp est un float, pas une string !
        formatted_time = timestamp.replace('.', ',')  # ← ERREUR ICI !
        return formatted_time
    except Exception as e:
        print(f"⚠️ Erreur parsing timestamp {timestamp}: {e}")
        return None

# SOLUTION ATTENDUE DE CHATGPT :
def parse_timestamp_correct(timestamp):
    try:
        if isinstance(timestamp, float):
            # Convertir d'abord en string
            timestamp_str = str(timestamp)
            formatted_time = timestamp_str.replace('.', ',')
            return formatted_time
        elif isinstance(timestamp, str):
            return timestamp.replace('.', ',')
        else:
            return str(timestamp)
    except Exception as e:
        print(f"⚠️ Erreur parsing timestamp {timestamp}: {e}")
        return None

# IMPACT :
# - 50+ erreurs au démarrage qui polluent les logs
# - Masque les vraies erreurs importantes
# - Ralentit le démarrage de l'application

# QUESTION POUR CHATGPT :
# Comment corriger définitivement ces erreurs de parsing timestamp ?
