import time
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 INTÉGRATION CODE ANALYZER DANS JARVIS - JEAN-LUC PASSAVE
Intégration du système d'analyse de code dans l'architecture JARVIS
"""

import gradio as gr
from jarvis_code_analyzer_auto_repair import JarvisCodeAnalyzer
from datetime import datetime
import json
import os

def create_integrated_code_analyzer():
    """CRÉER L'INTERFACE INTÉGRÉE DANS JARVIS"""
    
    with gr.Blocks() as code_analyzer_tab:
        
        # En-tête intégré JARVIS
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 15px; border-radius: 10px; margin-bottom: 15px;">
            <h2 style="margin: 0; font-size: 1.8em;">🔧 JARVIS CODE ANALYZER</h2>
            <p style="margin: 5px 0 0 0;">Analyse & Auto-Réparation Intelligente</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # CONTRÔLES D'ANALYSE
                gr.HTML("<h3>🔍 Configuration</h3>")
                
                quick_analysis_btn = gr.Button("⚡ Analyse Rapide", variant="primary", size="lg")
                full_analysis_btn = gr.Button("🔍 Analyse Complète", variant="secondary")
                
                auto_fix_checkbox = gr.Checkbox(
                    label="Auto-réparation activée",
                    value=True
                )
                
                confidence_slider = gr.Slider(
                    minimum=0.5,
                    maximum=1.0,
                    value=0.8,
                    label="Seuil de confiance",
                    step=0.1
                )
                
                # STATUT EN TEMPS RÉEL
                analysis_status = gr.HTML("""
                <div style="background: #f0f8ff; padding: 10px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0; color: #3498db;">📋 Prêt</h4>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em;">Cliquez sur "Analyse Rapide" pour commencer</p>
                </div>
                """)
                
            with gr.Column(scale=2):
                # RÉSULTATS
                gr.HTML("<h3>📊 Résultats</h3>")
                
                results_display = gr.HTML("""
                <div style="background: #f9f9f9; padding: 15px; border-radius: 10px; min-height: 200px;">
                    <h4>📋 Résultats d'Analyse</h4>
                    <p>Aucune analyse effectuée</p>
                </div>
                """)
                
                # MÉTRIQUES RAPIDES
                with gr.Row():
                    files_count = gr.HTML("""
                    <div style="background: #e3f2fd; padding: 10px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #1976d2;">📁 Fichiers</h4>
                        <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">-</p>
                    </div>
                    """)
                    
                    issues_count = gr.HTML("""
                    <div style="background: #fff3e0; padding: 10px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #f57c00;">🔍 Issues</h4>
                        <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">-</p>
                    </div>
                    """)
                    
                    fixes_count = gr.HTML("""
                    <div style="background: #e8f5e8; padding: 10px; border-radius: 8px; text-align: center;">
                        <h4 style="margin: 0; color: #388e3c;">🔧 Corrections</h4>
                        <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">-</p>
                    </div>
                    """)
        
        # HISTORIQUE COMPACT
        with gr.Row():
            with gr.Column():
                gr.HTML("<h3>📚 Dernières Analyses</h3>")

                recent_analyses = gr.HTML("""
                <div style="background: #f9f9f9; padding: 10px; border-radius: 8px;">
                    <p style="margin: 0; color: #666;">Aucun historique disponible</p>
                </div>
                """)

        # 🏠 BOUTON RETOUR DASHBOARD - JEAN-LUC PASSAVE
        with gr.Row():
            retour_dashboard_btn = gr.Button("🏠 Retour Dashboard Principal", variant="secondary", size="lg")
        
        # FONCTIONS INTÉGRÉES
        def run_quick_analysis(auto_fix, confidence):
            """ANALYSE RAPIDE INTÉGRÉE"""
            try:
                # Statut de démarrage
                status_running = """
                <div style="background: #fff3cd; padding: 10px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0; color: #856404;">🔄 Analyse en cours...</h4>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em;">Scanning des fichiers JARVIS...</p>
                </div>
                """
                
                # Initialiser l'analyseur
                analyzer = JarvisCodeAnalyzer()
                
                # Analyse rapide (seulement les fichiers principaux)
                analyzer.analyze_python_files()
                analyzer.analyze_javascript_files()
                analyzer.suggest_auto_fixes()
                
                # Appliquer corrections si demandé
                fixes_applied = []
                if auto_fix:
                    fixes_applied = analyzer.apply_auto_fixes(min_confidence=confidence)
                
                # Compter les résultats
                total_issues = len(analyzer.issues)
                total_files = len(analyzer.scan_results["python_files"]) + len(analyzer.scan_results["js_files"])
                total_fixes = len(fixes_applied)
                
                # Générer le résumé
                severity_counts = {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0}
                for issue in analyzer.issues:
                    severity_counts[issue.get("severity", "LOW")] += 1
                
                # Statut final
                if total_issues == 0:
                    final_status = """
                    <div style="background: #d4edda; padding: 10px; border-radius: 8px; margin: 10px 0;">
                        <h4 style="margin: 0; color: #155724;">🎉 Analyse Terminée</h4>
                        <p style="margin: 5px 0 0 0; font-size: 0.9em;">Aucun problème détecté!</p>
                    </div>
                    """
                else:
                    final_status = f"""
                    <div style="background: #d1ecf1; padding: 10px; border-radius: 8px; margin: 10px 0;">
                        <h4 style="margin: 0; color: #0c5460;">✅ Analyse Terminée</h4>
                        <p style="margin: 5px 0 0 0; font-size: 0.9em;">{total_issues} issues, {total_fixes} corrections</p>
                    </div>
                    """
                
                # Résultats détaillés
                results_html = f"""
                <div style="background: #f9f9f9; padding: 15px; border-radius: 10px;">
                    <h4>📊 Résultats - {datetime.now().strftime('%H:%M:%S')}</h4>
                    
                    <div style="margin: 10px 0;">
                        <strong>📁 Fichiers analysés:</strong> {total_files}<br>
                        <strong>🔍 Issues détectées:</strong> {total_issues}<br>
                        <strong>🔧 Corrections appliquées:</strong> {total_fixes}
                    </div>
                    
                    <div style="margin: 10px 0;">
                        <strong>🚨 Répartition par sévérité:</strong><br>
                        🔴 Critiques: {severity_counts['CRITICAL']}<br>
                        🟠 Hautes: {severity_counts['HIGH']}<br>
                        🟡 Moyennes: {severity_counts['MEDIUM']}<br>
                        🟢 Basses: {severity_counts['LOW']}
                    </div>
                    
                    <div style="margin: 10px 0; padding: 10px; background: #e3f2fd; border-radius: 5px;">
                        <strong>💡 Recommandation:</strong><br>
                        {"Code en bon état général" if total_issues < 10 else "Plusieurs améliorations possibles"}
                    </div>
                </div>
                """
                
                # Métriques pour les widgets
                files_widget = f"""
                <div style="background: #e3f2fd; padding: 10px; border-radius: 8px; text-align: center;">
                    <h4 style="margin: 0; color: #1976d2;">📁 Fichiers</h4>
                    <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">{total_files}</p>
                </div>
                """
                
                issues_widget = f"""
                <div style="background: #fff3e0; padding: 10px; border-radius: 8px; text-align: center;">
                    <h4 style="margin: 0; color: #f57c00;">🔍 Issues</h4>
                    <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">{total_issues}</p>
                </div>
                """
                
                fixes_widget = f"""
                <div style="background: #e8f5e8; padding: 10px; border-radius: 8px; text-align: center;">
                    <h4 style="margin: 0; color: #388e3c;">🔧 Corrections</h4>
                    <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">{total_fixes}</p>
                </div>
                """
                
                return final_status, results_html, files_widget, issues_widget, fixes_widget
                
            except Exception as e:
                error_status = f"""
                <div style="background: #f8d7da; padding: 10px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0; color: #721c24;">❌ Erreur</h4>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em;">Erreur: {str(e)}</p>
                </div>
                """
                
                error_results = f"""
                <div style="background: #f8d7da; padding: 15px; border-radius: 10px;">
                    <h4>❌ Erreur d'Analyse</h4>
                    <p>Une erreur s'est produite lors de l'analyse:</p>
                    <code style="background: #fff; padding: 5px; border-radius: 3px;">{str(e)}</code>
                </div>
                """
                
                return error_status, error_results, "", "", ""
        
        def run_full_analysis(auto_fix, confidence):
            """ANALYSE COMPLÈTE INTÉGRÉE"""
            try:
                # Utiliser l'analyseur complet
                analyzer = JarvisCodeAnalyzer()
                report = analyzer.analyze_all()
                
                if auto_fix:
                    analyzer.apply_auto_fixes(min_confidence=confidence)
                
                # Générer un résumé complet
                summary = report["summary"]
                
                status_complete = f"""
                <div style="background: #d4edda; padding: 10px; border-radius: 8px; margin: 10px 0;">
                    <h4 style="margin: 0; color: #155724;">🎉 Analyse Complète Terminée</h4>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em;">Temps: {report['analysis_time']}s</p>
                </div>
                """
                
                results_complete = f"""
                <div style="background: #f9f9f9; padding: 15px; border-radius: 10px;">
                    <h4>📊 Analyse Complète - {datetime.now().strftime('%H:%M:%S')}</h4>
                    
                    <div style="margin: 10px 0;">
                        <strong>📁 Fichiers analysés:</strong> {summary['total_files_analyzed']}<br>
                        <strong>🐍 Python:</strong> {summary['python_files']}<br>
                        <strong>📜 JavaScript:</strong> {summary['js_files']}<br>
                        <strong>🔍 Issues totales:</strong> {summary['total_issues']}<br>
                        <strong>🔧 Corrections:</strong> {summary['fixes_applied']}
                    </div>
                    
                    <div style="margin: 10px 0;">
                        <strong>🚨 Détail par sévérité:</strong><br>
                        🔴 Critiques: {summary['severity_breakdown']['CRITICAL']}<br>
                        🟠 Hautes: {summary['severity_breakdown']['HIGH']}<br>
                        🟡 Moyennes: {summary['severity_breakdown']['MEDIUM']}<br>
                        🟢 Basses: {summary['severity_breakdown']['LOW']}
                    </div>
                    
                    <div style="margin: 10px 0; padding: 10px; background: #e8f5e8; border-radius: 5px;">
                        <strong>💡 Recommandations:</strong><br>
                        {"<br>".join(report['recommendations'])}
                    </div>
                    
                    <div style="margin: 10px 0; padding: 10px; background: #fff3e0; border-radius: 5px;">
                        <strong>⏱️ Performance:</strong><br>
                        Analyse terminée en {report['analysis_time']} secondes
                    </div>
                </div>
                """
                
                # Widgets mis à jour
                files_widget = f"""
                <div style="background: #e3f2fd; padding: 10px; border-radius: 8px; text-align: center;">
                    <h4 style="margin: 0; color: #1976d2;">📁 Fichiers</h4>
                    <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">{summary['total_files_analyzed']}</p>
                </div>
                """
                
                issues_widget = f"""
                <div style="background: #fff3e0; padding: 10px; border-radius: 8px; text-align: center;">
                    <h4 style="margin: 0; color: #f57c00;">🔍 Issues</h4>
                    <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">{summary['total_issues']}</p>
                </div>
                """
                
                fixes_widget = f"""
                <div style="background: #e8f5e8; padding: 10px; border-radius: 8px; text-align: center;">
                    <h4 style="margin: 0; color: #388e3c;">🔧 Corrections</h4>
                    <p style="margin: 5px 0 0 0; font-size: 1.2em; font-weight: bold;">{summary['fixes_applied']}</p>
                </div>
                """
                
                return status_complete, results_complete, files_widget, issues_widget, fixes_widget
                
            except Exception as e:
                return run_quick_analysis(auto_fix, confidence)  # Fallback vers analyse rapide
        
        def get_recent_history():
            """OBTENIR L'HISTORIQUE RÉCENT"""
            try:
                project_root = "/Volumes/seagate/Louna_Electron_Latest"
                report_files = []
                
                for file in os.listdir(project_root):
                    if file.startswith("jarvis_analysis_report_") and file.endswith(".json"):
                        report_files.append(file)
                
                if not report_files:
                    return """
                    <div style="background: #f9f9f9; padding: 10px; border-radius: 8px;">
                        <p style="margin: 0; color: #666;">Aucun historique disponible</p>
                    </div>
                    """
                
                # Prendre les 3 plus récents
                report_files.sort(reverse=True)
                recent_files = report_files[:3]
                
                history_html = """
                <div style="background: #f9f9f9; padding: 10px; border-radius: 8px;">
                """
                
                for i, report_file in enumerate(recent_files):
                    date_str = report_file.replace("jarvis_analysis_report_", "").replace(".json", "")
                    try:
                        date_obj = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                        formatted_date = date_obj.strftime("%d/%m %H:%M")
                    except:
                        formatted_date = date_str[:8]
                    
                    history_html += f"""
                    <div style="margin: 5px 0; padding: 5px; background: #e9ecef; border-radius: 3px; font-size: 0.9em;">
                        📄 {formatted_date}
                    </div>
                    """
                
                history_html += "</div>"
                return history_html
                
            except Exception:
                return """
                <div style="background: #f9f9f9; padding: 10px; border-radius: 8px;">
                    <p style="margin: 0; color: #666;">Erreur chargement historique</p>
                </div>
                """
        
        # CONNEXIONS
        quick_analysis_btn.click(
            fn=run_quick_analysis,
            inputs=[auto_fix_checkbox, confidence_slider],
            outputs=[analysis_status, results_display, files_count, issues_count, fixes_count]
        )
        
        full_analysis_btn.click(
            fn=run_full_analysis,
            inputs=[auto_fix_checkbox, confidence_slider],
            outputs=[analysis_status, results_display, files_count, issues_count, fixes_count]
        )
        
        # 🏠 FONCTION RETOUR DASHBOARD - JEAN-LUC PASSAVE
        def retour_dashboard():
            """RETOURNER AU DASHBOARD PRINCIPAL"""
            import webbrowser
            webbrowser.open("http://localhost:7867")
            return "🏠 Redirection vers le Dashboard Principal..."

        # CONNEXION BOUTON RETOUR
        retour_dashboard_btn.click(
            fn=retour_dashboard,
            outputs=[]
        )

        # Charger l'historique au démarrage
        code_analyzer_tab.load(
            fn=get_recent_history,
            outputs=[recent_analyses]
        )
    
    return code_analyzer_tab

if __name__ == "__main__":
    interface = create_integrated_code_analyzer()
    interface.launch(server_port=7898, share=False)
