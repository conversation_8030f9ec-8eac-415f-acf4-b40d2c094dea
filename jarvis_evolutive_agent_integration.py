#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 JARVIS EVOLUTIVE AGENT INTEGRATION
====================================
Intégration du code EvolutiveAgent de Jean-Luc dans JARVIS
Combinaison de la simplicité de EvolutiveAgent avec la puissance de JARVIS
"""

import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import des modules JARVIS existants
try:
    from jarvis_thermal_memory import ajouter_memoire, rechercher_memoire
    THERMAL_MEMORY_AVAILABLE = True
except ImportError:
    THERMAL_MEMORY_AVAILABLE = False
    def ajouter_memoire(titre, contenu):
        pass
    def rechercher_memoire(query):
        return []

class JarvisEvolutiveAgent:
    """
    Agent évolutif JARVIS basé sur le code de Jean-Luc
    Combine simplicité et puissance pour un apprentissage optimal
    """
    
    def __init__(self):
        # Mémoires selon le design de Jean-Luc
        self.short_term_memory = []
        self.long_term_memory = {}
        self.context = {}
        
        # Extensions JARVIS
        self.jarvis_integration = True
        self.thermal_memory_enabled = THERMAL_MEMORY_AVAILABLE
        self.evolution_history = []
        self.learning_patterns = {}
        self.adaptation_metrics = {
            "interactions_count": 0,
            "learning_efficiency": 0.0,
            "memory_optimization": 0.0,
            "evolution_score": 0.0
        }
        
        # Configuration évolutive
        self.evolution_config = {
            "auto_evolution": True,
            "learning_threshold": 3,  # Évolution après 3 interactions similaires
            "memory_cleanup_interval": 100,  # Nettoyage toutes les 100 interactions
            "adaptation_rate": 0.1
        }
        
        print("🧠 JARVIS EvolutiveAgent initialisé")
        print(f"🔗 Intégration mémoire thermique: {self.thermal_memory_enabled}")
        
    def perceive(self, input_data: str) -> None:
        """
        Perception améliorée avec intégration JARVIS
        """
        # Ajouter à la mémoire courte (design Jean-Luc)
        self.short_term_memory.append({
            "data": input_data,
            "timestamp": datetime.now(),
            "processed": False
        })
        
        # Intégration JARVIS : ajouter à la mémoire thermique
        if self.thermal_memory_enabled:
            ajouter_memoire(
                f"Perception Agent {datetime.now().strftime('%H:%M:%S')}",
                input_data
            )
        
        # Mise à jour des métriques
        self.adaptation_metrics["interactions_count"] += 1
        
        print(f"🔍 Perception reçue : {input_data[:50]}...")
        
    def process(self) -> Optional[Dict[str, Any]]:
        """
        Traitement amélioré avec évolution automatique
        """
        if not self.short_term_memory:
            return None
        
        # Récupérer la dernière perception non traitée
        for item in self.short_term_memory:
            if not item["processed"]:
                info = item["data"]
                item["processed"] = True
                
                # Traitement selon le design de Jean-Luc
                if ":" in info:
                    key, value = info.split(":", 1)
                    self.learn(key.strip(), value.strip())
                    
                    return {
                        "type": "learning",
                        "key": key.strip(),
                        "value": value.strip(),
                        "timestamp": item["timestamp"]
                    }
                else:
                    # Traitement contextuel pour données non structurées
                    self.learn("context", info)
                    
                    return {
                        "type": "context",
                        "content": info,
                        "timestamp": item["timestamp"]
                    }
        
        return None
    
    def learn(self, key: str, value: str) -> None:
        """
        Apprentissage amélioré avec patterns et optimisation
        """
        # Apprentissage de base (design Jean-Luc)
        if key in self.long_term_memory:
            self.long_term_memory[key].append(value)
        else:
            self.long_term_memory[key] = [value]
        
        # Extensions JARVIS : analyse des patterns
        self._analyze_learning_patterns(key, value)
        
        # Intégration mémoire thermique
        if self.thermal_memory_enabled:
            ajouter_memoire(
                f"Apprentissage: {key}",
                f"Nouvelle connaissance: {value}"
            )
        
        # Mise à jour efficacité d'apprentissage
        self._update_learning_efficiency(key)
        
        print(f"📚 Appris : {key} -> {value}")
    
    def recall(self, key: str) -> Any:
        """
        Rappel amélioré avec recherche intelligente
        """
        # Rappel direct (design Jean-Luc)
        direct_result = self.long_term_memory.get(key, None)
        
        if direct_result:
            return direct_result
        
        # Recherche étendue avec mémoire thermique
        if self.thermal_memory_enabled:
            thermal_results = rechercher_memoire(key)
            if thermal_results:
                return f"Mémoire thermique: {thermal_results[0]}"
        
        # Recherche par similarité dans les clés existantes
        similar_keys = [k for k in self.long_term_memory.keys() if key.lower() in k.lower()]
        if similar_keys:
            return {
                "similar_keys": similar_keys,
                "suggestions": [self.long_term_memory[k] for k in similar_keys[:3]]
            }
        
        return "Information inconnue - Voulez-vous que j'apprenne quelque chose sur ce sujet ?"
    
    def evolve(self) -> Dict[str, Any]:
        """
        Évolution améliorée avec métriques et optimisation intelligente
        """
        print("🔄 Évolution en cours...")
        
        evolution_report = {
            "timestamp": datetime.now(),
            "before_state": {
                "memory_keys": len(self.long_term_memory),
                "total_values": sum(len(v) for v in self.long_term_memory.values()),
                "interactions": self.adaptation_metrics["interactions_count"]
            }
        }
        
        # Évolution de base (design Jean-Luc) - garder les clés avec plus de 1 occurrence
        original_size = len(self.long_term_memory)
        self.long_term_memory = {
            k: v for k, v in self.long_term_memory.items() 
            if len(v) > 1 or self._is_important_key(k)
        }
        
        # Évolution avancée JARVIS
        self._optimize_memory_structure()
        self._consolidate_learning_patterns()
        self._update_evolution_metrics()
        
        evolution_report["after_state"] = {
            "memory_keys": len(self.long_term_memory),
            "total_values": sum(len(v) for v in self.long_term_memory.values()),
            "optimization_ratio": len(self.long_term_memory) / max(original_size, 1)
        }
        
        # Enregistrer l'évolution
        self.evolution_history.append(evolution_report)
        
        print(f"✅ Évolution terminée: {original_size} -> {len(self.long_term_memory)} clés")
        
        return evolution_report
    
    def interact(self, input_data: str) -> Dict[str, Any]:
        """
        Interaction complète avec évolution automatique
        """
        # Interaction de base (design Jean-Luc)
        self.perceive(input_data)
        processing_result = self.process()
        
        # Évolution automatique selon la configuration
        if (self.evolution_config["auto_evolution"] and 
            self.adaptation_metrics["interactions_count"] % self.evolution_config["memory_cleanup_interval"] == 0):
            evolution_result = self.evolve()
        else:
            evolution_result = None
        
        # Retour enrichi
        return {
            "processing": processing_result,
            "evolution": evolution_result,
            "metrics": self.adaptation_metrics.copy(),
            "memory_state": {
                "short_term": len(self.short_term_memory),
                "long_term_keys": len(self.long_term_memory),
                "total_knowledge": sum(len(v) for v in self.long_term_memory.values())
            }
        }
    
    def _analyze_learning_patterns(self, key: str, value: str) -> None:
        """Analyse les patterns d'apprentissage"""
        if key not in self.learning_patterns:
            self.learning_patterns[key] = {
                "frequency": 0,
                "last_update": datetime.now(),
                "value_diversity": set()
            }
        
        pattern = self.learning_patterns[key]
        pattern["frequency"] += 1
        pattern["last_update"] = datetime.now()
        pattern["value_diversity"].add(value[:50])  # Limiter pour éviter la surcharge mémoire
    
    def _update_learning_efficiency(self, key: str) -> None:
        """Met à jour l'efficacité d'apprentissage"""
        if key in self.learning_patterns:
            frequency = self.learning_patterns[key]["frequency"]
            diversity = len(self.learning_patterns[key]["value_diversity"])
            
            # Efficacité = diversité / fréquence (éviter la répétition)
            efficiency = diversity / max(frequency, 1)
            self.adaptation_metrics["learning_efficiency"] = (
                self.adaptation_metrics["learning_efficiency"] * 0.9 + efficiency * 0.1
            )
    
    def _is_important_key(self, key: str) -> bool:
        """Détermine si une clé est importante à conserver"""
        important_keywords = ["jarvis", "système", "apprentissage", "évolution", "mémoire"]
        return any(keyword in key.lower() for keyword in important_keywords)
    
    def _optimize_memory_structure(self) -> None:
        """Optimise la structure de la mémoire"""
        # Fusionner les clés similaires
        keys_to_merge = {}
        for key in list(self.long_term_memory.keys()):
            base_key = key.lower().strip()
            if base_key not in keys_to_merge:
                keys_to_merge[base_key] = []
            keys_to_merge[base_key].append(key)
        
        # Appliquer les fusions
        for base_key, similar_keys in keys_to_merge.items():
            if len(similar_keys) > 1:
                merged_values = []
                for key in similar_keys:
                    merged_values.extend(self.long_term_memory[key])
                    del self.long_term_memory[key]
                
                # Garder la clé la plus récente
                self.long_term_memory[similar_keys[0]] = list(set(merged_values))
    
    def _consolidate_learning_patterns(self) -> None:
        """Consolide les patterns d'apprentissage"""
        # Nettoyer les patterns anciens
        cutoff_time = datetime.now().timestamp() - 3600  # 1 heure
        
        self.learning_patterns = {
            k: v for k, v in self.learning_patterns.items()
            if v["last_update"].timestamp() > cutoff_time or v["frequency"] > 5
        }
    
    def _update_evolution_metrics(self) -> None:
        """Met à jour les métriques d'évolution"""
        # Score d'évolution basé sur l'efficacité et l'optimisation
        memory_ratio = len(self.long_term_memory) / max(self.adaptation_metrics["interactions_count"], 1)
        self.adaptation_metrics["memory_optimization"] = 1.0 - memory_ratio
        
        # Score global d'évolution
        self.adaptation_metrics["evolution_score"] = (
            self.adaptation_metrics["learning_efficiency"] * 0.4 +
            self.adaptation_metrics["memory_optimization"] * 0.3 +
            min(len(self.learning_patterns) / 10, 1.0) * 0.3
        )
    
    def get_status(self) -> Dict[str, Any]:
        """Retourne le statut complet de l'agent"""
        return {
            "agent_type": "JARVIS EvolutiveAgent",
            "integration_status": {
                "jarvis_integration": self.jarvis_integration,
                "thermal_memory": self.thermal_memory_enabled
            },
            "memory_state": {
                "short_term_items": len(self.short_term_memory),
                "long_term_keys": len(self.long_term_memory),
                "total_knowledge": sum(len(v) for v in self.long_term_memory.values()),
                "learning_patterns": len(self.learning_patterns)
            },
            "metrics": self.adaptation_metrics,
            "evolution_history": len(self.evolution_history),
            "configuration": self.evolution_config
        }

# Instance globale pour intégration JARVIS
JARVIS_EVOLUTIVE_AGENT = None

def get_jarvis_evolutive_agent() -> JarvisEvolutiveAgent:
    """Retourne l'instance de l'agent évolutif JARVIS"""
    global JARVIS_EVOLUTIVE_AGENT
    if JARVIS_EVOLUTIVE_AGENT is None:
        JARVIS_EVOLUTIVE_AGENT = JarvisEvolutiveAgent()
    return JARVIS_EVOLUTIVE_AGENT

def test_evolutive_agent():
    """Test de l'agent évolutif"""
    print("🧪 TEST JARVIS EVOLUTIVE AGENT")
    print("=" * 50)
    
    agent = get_jarvis_evolutive_agent()
    
    # Tests selon l'exemple de Jean-Luc
    test_data = [
        "fruit: pomme",
        "fruit: banane", 
        "fruit: pomme",
        "animal: chat",
        "système: JARVIS",
        "apprentissage: évolution continue"
    ]
    
    for data in test_data:
        result = agent.interact(data)
        print(f"📝 Interaction: {data}")
        print(f"📊 Résultat: {result['processing']}")
        time.sleep(0.5)
    
    # Test de rappel
    print(f"\n🔍 Rappel 'fruit': {agent.recall('fruit')}")
    print(f"🔍 Rappel 'animal': {agent.recall('animal')}")
    print(f"🔍 Rappel 'système': {agent.recall('système')}")
    
    # Statut final
    status = agent.get_status()
    print(f"\n📊 Statut final: {status}")
    
    return agent

if __name__ == "__main__":
    test_evolutive_agent()
