#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DÉMARRAGE DEEPSEEK SIMPLE - JEAN-LUC PASSAVE
Script simple pour démarrer DeepSeek R1 8B rapidement
"""

import subprocess
import time
import os

def start_deepseek_simple():
    """Démarre DeepSeek R1 8B de manière simple"""
    print("🚀 DÉMARRAGE DEEPSEEK R1 8B SIMPLE - JEAN-LUC PASSAVE")
    print("=" * 60)
    
    # Chemin vers le modèle
    model_path = "/Volumes/seagate/Louna_Electron_Latest/Jan-nano-gguf/jan-nano-4b-Q4_K_M.gguf"
    
    if not os.path.exists(model_path):
        print(f"❌ Modèle non trouvé: {model_path}")
        return False
    
    print(f"📁 Modèle trouvé: {model_path}")
    
    # Commande VLLM simplifiée
    cmd = [
        "python", "-m", "vllm.entrypoints.openai.api_server",
        "--model", model_path,
        "--host", "0.0.0.0",
        "--port", "8000",
        "--served-model-name", "jan-nano"
    ]
    
    print("🔄 Démarrage du serveur VLLM...")
    print("⏳ Cela peut prendre 1-2 minutes...")
    
    try:
        # Démarrer le processus
        process = subprocess.Popen(
            cmd,
            cwd="/Volumes/seagate/Louna_Electron_Latest",
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print(f"🔄 Processus démarré PID: {process.pid}")
        print("📊 Logs du serveur:")
        print("-" * 40)
        
        # Afficher les logs en temps réel
        for line in process.stdout:
            print(line.strip())
            
            # Détecter quand le serveur est prêt
            if "Uvicorn running on" in line or "Application startup complete" in line:
                print("✅ Serveur DeepSeek R1 8B opérationnel !")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur démarrage: {e}")
        return False

if __name__ == "__main__":
    start_deepseek_simple()
