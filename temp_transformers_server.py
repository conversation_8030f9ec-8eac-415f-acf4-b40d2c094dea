
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from flask import Flask, request, jsonify
import threading
import time

app = Flask(__name__)

# Charger un modèle simple
model_name = "microsoft/DialoGPT-medium"
print("📥 Chargement modèle DialoGPT...")
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)
print("✅ Modèle chargé")

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    try:
        data = request.json
        messages = data.get('messages', [])
        user_message = messages[-1].get('content', '') if messages else ''
        
        # Encoder le message
        inputs = tokenizer.encode(user_message + tokenizer.eos_token, return_tensors='pt')
        
        # Générer la réponse
        with torch.no_grad():
            outputs = model.generate(
                inputs, 
                max_length=inputs.shape[1] + 100,
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Décoder la réponse
        response_text = tokenizer.decode(outputs[0][inputs.shape[1]:], skip_special_tokens=True)
        
        return jsonify({
            "id": "chatcmpl-transformers",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "DialoGPT",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response_text or "Je suis un assistant IA basé sur DialoGPT. Comment puis-je vous aider ?"
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(inputs[0]),
                "completion_tokens": 50,
                "total_tokens": len(inputs[0]) + 50
            }
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    print("🌐 Serveur démarré sur http://localhost:8000")
    app.run(host='localhost', port=8000, debug=False)
