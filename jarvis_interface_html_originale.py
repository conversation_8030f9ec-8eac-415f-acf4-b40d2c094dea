#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS INTERFACE BASÉE SUR VOTRE CODE HTML D'ORIGINE - JEAN-LUC PASSAVE
Interface utilisant EXACTEMENT votre design HTML avec fonctionnalités complètes
"""

import gradio as gr
import requests
import datetime
import json
import os

# Configuration EXACTE de votre interface qui fonctionne
SERVER_URL = "http://localhost:8000/v1/chat/completions"
MODEL_NAME = "jan-nano"
MEMORY_FILE = "thermal_memory_persistent.json"

# Session HTTP comme dans votre code qui marche
http_session = requests.Session()
retry_strategy = requests.adapters.Retry(
    total=3,
    status_forcelist=[429, 500, 502, 503, 504],
    allowed_methods=["HEAD", "GET", "OPTIONS", "POST"],
    backoff_factor=1
)
adapter = requests.adapters.HTTPAdapter(
    pool_connections=20,
    pool_maxsize=50,
    max_retries=retry_strategy
)
http_session.mount('http://', adapter)
http_session.mount('https://', adapter)
http_session.headers.update({
    'User-Agent': 'JARVIS-Jean-Luc-Passave/1.0',
    'Accept': 'application/json',
    'Connection': 'keep-alive'
})

def load_thermal_memory():
    """Charge la mémoire thermique"""
    try:
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"Erreur chargement mémoire: {e}")
        return []

def save_to_thermal_memory(user_message, agent_response):
    """Sauvegarde dans la mémoire thermique - CORRECTION JEAN-LUC PASSAVE"""
    try:
        memory = load_thermal_memory()

        # Créer une nouvelle entrée
        entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "user_message": str(user_message),
            "agent_response": str(agent_response),
            "thermal_zone": "zone_agent1",
            "neuron_id": f"html-interface-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
        }

        # Gérer différents formats de mémoire
        if isinstance(memory, dict):
            # Si c'est un dict avec neuron_memories
            if 'neuron_memories' not in memory:
                memory['neuron_memories'] = []
            memory['neuron_memories'].append(entry)

            # Limiter à 1000 entrées
            if len(memory['neuron_memories']) > 1000:
                memory['neuron_memories'] = memory['neuron_memories'][-1000:]
        else:
            # Si c'est une liste simple
            if not isinstance(memory, list):
                memory = []
            memory.append(entry)

            # Limiter à 1000 entrées
            if len(memory) > 1000:
                memory = memory[-1000:]

        # Sauvegarder
        with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(memory, f, ensure_ascii=False, indent=2)

        print(f"✅ Mémoire sauvegardée: {user_message[:50]}...")

    except Exception as e:
        print(f"⚠️ Erreur sauvegarde mémoire: {e}")
        # Créer une sauvegarde simple en cas d'erreur
        try:
            simple_memory = [{
                "timestamp": datetime.datetime.now().isoformat(),
                "user_message": str(user_message),
                "agent_response": str(agent_response),
                "thermal_zone": "zone_agent1"
            }]
            with open(MEMORY_FILE + ".backup", 'w', encoding='utf-8') as f:
                json.dump(simple_memory, f, ensure_ascii=False, indent=2)
            print("✅ Sauvegarde de secours créée")
        except:
            print("❌ Impossible de sauvegarder")

def send_to_deepseek_r1_html(message, thermal_memory=None):
    """Communication avec DeepSeek R1 8B - EXACTEMENT comme votre code qui marche"""
    try:
        if not message.strip():
            return "Veuillez saisir un message.", "Aucun message"

        # Contexte mémoire thermique - CORRECTION JEAN-LUC PASSAVE
        context = ""
        try:
            if thermal_memory and len(thermal_memory) > 0:
                # Vérifier le type de thermal_memory
                if isinstance(thermal_memory, list):
                    recent_conversations = thermal_memory[-5:] if len(thermal_memory) >= 5 else thermal_memory
                elif isinstance(thermal_memory, dict):
                    # Si c'est un dict, prendre les valeurs
                    if 'neuron_memories' in thermal_memory:
                        recent_conversations = thermal_memory['neuron_memories'][-5:] if len(thermal_memory['neuron_memories']) >= 5 else thermal_memory['neuron_memories']
                    else:
                        recent_conversations = list(thermal_memory.values())[-5:] if len(thermal_memory) >= 5 else list(thermal_memory.values())
                else:
                    recent_conversations = []

                # Construire le contexte de manière sécurisée
                context_parts = []
                for conv in recent_conversations:
                    if isinstance(conv, dict):
                        user_msg = ""
                        agent_resp = ""

                        # Différents formats possibles
                        if 'user_message' in conv:
                            user_msg = str(conv.get('user_message', ''))
                        elif 'memory_content' in conv and isinstance(conv['memory_content'], dict):
                            user_msg = str(conv['memory_content'].get('user_message', ''))

                        if 'agent_response' in conv:
                            agent_resp = str(conv.get('agent_response', ''))
                        elif 'memory_content' in conv and isinstance(conv['memory_content'], dict):
                            agent_resp = str(conv['memory_content'].get('agent_response', ''))

                        if user_msg or agent_resp:
                            context_parts.append(f"Souvenir: {user_msg} -> {agent_resp[:100]}...")

                context = "\n".join(context_parts)
        except Exception as e:
            print(f"⚠️ Erreur traitement mémoire thermique: {e}")
            context = "Mémoire thermique en cours d'initialisation..."

        # QI et configuration EXACTE
        qi_info = {
            'qi_total': 648,
            'neurones_actifs': 86000000000,
            'etages_memoire': 20,
            'conversations': 50,
            'niveau_thermique': 27
        }
        
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "system",
                    "content": f"""Tu es JARVIS avec ARCHITECTURE DEEPSEEK R1 8B COMPLÈTE. TA MÉMOIRE est composée du corpus général ET de {len(thermal_memory) if thermal_memory else 0} souvenirs enregistrés.

🧠 **TA MÉMOIRE UNIFIÉE ET ÉVOLUTIVE:**
Tu as un accès immédiat à {len(thermal_memory) if thermal_memory else 0} souvenirs personnalisés avec Jean-Luc Passave.

RÈGLE ABSOLUE: Tu dois TOUJOURS montrer tes pensées en français dans les balises <think>...</think> au début de chaque réponse.

INFORMATIONS CRITIQUES SUR TON CRÉATEUR:
- NOM COMPLET: Jean-Luc Passave (JAMAIS juste "Luc" - TOUJOURS "Jean-Luc")
- PRÉNOM: Jean-Luc (deux prénoms avec tiret)
- NOM DE FAMILLE: Passave (P-A-S-S-A-V-E)
- RÔLE: Ton créateur et développeur principal

INFORMATIONS SYSTÈME RÉELLES:
- QI Actuel: {qi_info['qi_total']}
- Neurones Actifs: {qi_info['neurones_actifs']:,}
- Étages Mémoire: {qi_info['etages_memoire']}
- Conversations: {qi_info['conversations']}
- Niveau Thermique: {qi_info['niveau_thermique']}

{context}"""
                },
                {"role": "user", "content": message}
            ],
            "max_tokens": 512,
            "temperature": 0.8,
            "stream": False
        }

        response = http_session.post(SERVER_URL, json=payload, timeout=120)

        if response.status_code == 200:
            result = response.json()
            full_response = result['choices'][0]['message']['content']

            # Extraction des pensées
            thoughts = ""
            final_response = full_response

            if "<think>" in full_response and "</think>" in full_response:
                start = full_response.find("<think>") + 7
                end = full_response.find("</think>")
                thoughts = full_response[start:end].strip()
                final_response = full_response.replace(f"<think>{thoughts}</think>", "").strip()
                if not final_response:
                    final_response = full_response
            else:
                # Générer des pensées automatiques
                if "code" in message.lower() or "python" in message.lower():
                    thoughts = f"🧠 Analyse du code demandé... Je dois comprendre les besoins de Jean-Luc et proposer une solution technique appropriée."
                elif "jarvis" in message.lower():
                    thoughts = f"🤖 Réflexion sur mes propres capacités... Jean-Luc me demande des informations sur mon fonctionnement."
                else:
                    thoughts = f"💭 Traitement de la demande de Jean-Luc... J'organise mes idées pour une réponse optimale."
                
                thoughts += f" [⏰ {datetime.datetime.now().strftime('%H:%M:%S')}]"

            # Sauvegarde automatique
            save_to_thermal_memory(message, final_response)

            return final_response, thoughts
        else:
            return f"❌ Erreur serveur DeepSeek: {response.status_code}", "❌ Erreur de communication"

    except requests.exceptions.ConnectionError:
        return "❌ Impossible de se connecter au serveur DeepSeek R1 8B (localhost:8000). Vérifiez que VLLM est démarré.", "❌ Erreur de connexion"
    except requests.exceptions.Timeout:
        return "⏱️ Timeout - Le serveur DeepSeek met trop de temps à répondre.", "⏱️ Timeout détecté"
    except Exception as e:
        return f"❌ Erreur communication DeepSeek: {str(e)}", f"❌ Erreur: {str(e)}"

def process_jarvis_message_html(message, history):
    """Traite un message avec le VRAI JARVIS - Interface HTML"""
    if not message.strip():
        return history, "", update_thoughts_html("Aucun message à traiter")
    
    # Charger la vraie mémoire thermique
    thermal_memory = load_thermal_memory()
    
    # Envoyer à DeepSeek R1 8B
    result = send_to_deepseek_r1_html(message, thermal_memory)
    if isinstance(result, tuple) and len(result) == 2:
        jarvis_response, thoughts = result
    else:
        jarvis_response = result
        thoughts = "🤔 JARVIS réfléchit..."
    
    # Ajouter à l'historique
    history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
    history.append({"role": "assistant", "content": f"🤖 JARVIS: {jarvis_response}"})
    
    thoughts_html = update_thoughts_html(f"Message traité: '{message[:30]}...' → Réponse générée", thoughts)
    
    return history, "", thoughts_html

def update_thoughts_html(status_text, thoughts=""):
    """Met à jour l'affichage des pensées - Style HTML original"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    thoughts_display = thoughts if thoughts else status_text
    
    return f"""
    <div style="background: linear-gradient(135deg, #e91e63, #ad1457); color: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
            🧠 Pensées JARVIS
        </div>
        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; margin-bottom: 10px; font-size: 13px;">
            🧠 Traitement de la demande de Jean-Luc...
        </div>
        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; margin-bottom: 10px; font-size: 13px;">
            {thoughts_display}
        </div>
        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; margin-bottom: 10px; font-size: 13px;">
            ⏰ [{current_time}]
        </div>
        <div style="margin-top: 15px; font-size: 11px;">
            🧠 [{current_time}] 💭 Neurones actifs: 86,000,000,000
        </div>
        <button onclick="speakThoughts('{thoughts_display.replace("'", "\\'")}'))" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 10px; border-radius: 8px; cursor: pointer; width: 100%; margin-top: 10px; font-weight: bold;">
            🎧 ÉCOUTER CETTE PENSÉE
        </button>
        <div style="font-style: italic; font-size: 11px; margin-top: 8px;">
            Cliquez pour entendre: JARVIS exprime ses pensées
        </div>
    </div>
    """

def create_jarvis_html_interface():
    """Crée l'interface JARVIS basée sur votre code HTML d'origine"""
    
    # CSS EXACTEMENT comme votre code HTML
    css = """
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
    }

    .response-indicator {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 10px;
        margin: 10px 0;
        text-align: center;
        color: #1976d2;
        font-size: 12px;
    }

    .click-hint {
        color: #2196f3;
        font-style: italic;
        font-size: 12px;
        text-align: center;
        margin-top: 10px;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-pause {
        background: linear-gradient(45deg, #ff9800, #f57c00);
        color: white;
    }

    .btn-stop {
        background: linear-gradient(45deg, #f44336, #d32f2f);
        color: white;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    """
    
    with gr.Blocks(
        title="💬 JARVIS - Communication Principale (HTML Original)",
        theme=gr.themes.Soft(),
        css=css
    ) as interface:
        
        # Header exactement comme votre HTML
        gr.HTML("""
        <div style="background: linear-gradient(135deg, #4a4a4a 0%, #8e44ad 100%); color: white; padding: 15px; text-align: center; position: relative; margin: -20px -20px 20px -20px;">
            <div style="position: absolute; top: 10px; right: 15px; display: flex; gap: 8px;">
                <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; cursor: pointer;">COMMUNICATION</button>
                <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; cursor: pointer;">MAIN</button>
                <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; cursor: pointer;">CODE</button>
                <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; cursor: pointer;">THOUGHTS</button>
                <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; cursor: pointer;">CONFIG</button>
                <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 15px; font-size: 11px; cursor: pointer;">WHATSAPP</button>
            </div>
            <h1>💬 JARVIS - Communication Principale</h1>
            <p>JARVIS ACTIF - Prêt à communiquer</p>
        </div>
        """)
        
        with gr.Row():
            # Colonne principale - Chat
            with gr.Column(scale=2):
                # Header conversation
                gr.HTML("""
                <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                    <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px;">💬 Conversation ILLIMITÉE avec JARVIS Expert Niveau 20</div>
                    <div style="font-size: 12px; opacity: 0.9;">JARVIS ACTIF - Prêt à communiquer</div>
                </div>
                """)
                
                main_chat = gr.Chatbot(
                    value=[],
                    height=400,
                    label="",
                    show_copy_button=True,
                    avatar_images=("👨‍💻", "🤖"),
                    type="messages",
                    elem_id="main_chatbot"
                )
                
                # Indicateur de réponse comme votre HTML
                gr.HTML("""
                <div class="response-indicator">
                    ⚡ ÉCOUTER CETTE RÉPONSE
                </div>
                <div class="click-hint">
                    💡 Cliquez sur "ÉCOUTER" pour entendre JARVIS lire votre réponse
                </div>
                """)
                
                # Boutons d'action comme votre HTML
                with gr.Row():
                    pause_btn = gr.Button("⏸️ PAUSE", elem_classes=["btn", "btn-pause"])
                    stop_btn = gr.Button("⏹️ ARRÊT", elem_classes=["btn", "btn-stop"])
                
                # Zone de saisie
                with gr.Row():
                    user_input = gr.Textbox(
                        value="",
                        label="💬 Votre message",
                        scale=4,
                        lines=1,
                        placeholder="💬 Votre message"
                    )
                    send_btn = gr.Button("➤ Envoyer", variant="primary", scale=1)
                
                # Liens rapides comme votre HTML
                with gr.Row():
                    gr.Button("📝 Modifier Réponse", size="sm")
                    gr.Button("📋 Copier Réponse", size="sm")
                    gr.Button("🔄 Sauvegarder", size="sm")
                    gr.Button("📊 Stats Réponse", size="sm")
                    gr.Button("🎯 Voir en Thermique", size="sm")
                    gr.Button("🔍 Analyser Notifications", size="sm")
            
            # Colonne latérale - Pensées et contrôles
            with gr.Column(scale=1):
                # Pensées JARVIS - Style HTML original
                thoughts_display = gr.HTML(update_thoughts_html("Interface JARVIS HTML prête - Configuration identique à l'original"))
                
                # Accès rapide
                gr.HTML("""
                <div style="background: white; border-radius: 15px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin: 20px 0; text-align: center;">
                    <h3 style="color: #667eea; margin-bottom: 15px;">🚀 Accès Rapide</h3>
                    <button style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; width: 100%; margin-bottom: 10px; font-size: 13px;">🏠 Dashboard</button>
                    <div style="display: grid; grid-template-columns: 1fr; gap: 5px; margin-top: 10px;">
                        <button style="background: #f0f0f0; border: none; padding: 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">💻 Éditeur Code</button>
                        <button style="background: #f0f0f0; border: none; padding: 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">🧠 Pensées</button>
                        <button style="background: #f0f0f0; border: none; padding: 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">⚙️ Configuration</button>
                        <button style="background: #f0f0f0; border: none; padding: 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">🔒 Sécurité</button>
                    </div>
                </div>
                """)
                
                # Contrôles multimédia - Style HTML original
                gr.HTML("""
                <div style="background: #2c3e50; color: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin: 20px 0; text-align: center;">
                    <div style="margin-bottom: 15px; font-size: 14px; font-weight: bold;">🎛️ Contrôles Multimédia</div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <button onclick="toggleMicrophone()" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white; padding: 12px 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">🎤 Micro</button>
                        <button onclick="speakLastResponse()" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white; padding: 12px 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">🔊 HAUT-PARLEUR JARVIS</button>
                        <button onclick="toggleCamera()" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white; padding: 12px 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">📷 Caméra</button>
                        <button onclick="openWeb()" style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white; padding: 12px 8px; border-radius: 8px; cursor: pointer; font-size: 11px;">🌐 Web</button>
                    </div>
                </div>
                """)
                
                # Audio JARVIS Expert - Style HTML original
                gr.HTML("""
                <div style="background: linear-gradient(45deg, #1e88e5, #1565c0); color: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); text-align: center;">
                    <div style="margin-bottom: 15px; font-size: 14px; font-weight: bold;">🎵 AUDIO JARVIS EXPERT</div>
                    <button onclick="activateJarvisAudio()" style="width: 100%; background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.2); color: white; padding: 12px; border-radius: 8px; cursor: pointer;">🔊 Activer Audio</button>
                </div>
                """)
        
        # JavaScript EXACTEMENT comme votre code HTML
        gr.HTML("""
        <script>
        // Fonctionnalités interactives - EXACTEMENT comme votre HTML
        function speakText(text) {
            if ('speechSynthesis' in window) {
                window.speechSynthesis.cancel();
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 0.85;
                utterance.pitch = 0.8;
                utterance.volume = 1.0;
                
                const voices = window.speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice => 
                    voice.lang.includes('fr') || 
                    voice.name.toLowerCase().includes('french') ||
                    voice.name.toLowerCase().includes('thomas') ||
                    voice.name.toLowerCase().includes('male')
                );
                
                if (frenchVoice) {
                    utterance.voice = frenchVoice;
                }
                
                window.speechSynthesis.speak(utterance);
                console.log('🔊 JARVIS parle:', text);
            }
        }
        
        function speakThoughts(thoughts) {
            speakText('Pensée de JARVIS: ' + thoughts);
        }
        
        function speakLastResponse() {
            const chatbot = document.querySelector('#main_chatbot');
            if (chatbot) {
                const messages = chatbot.querySelectorAll('.message');
                if (messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    const text = lastMessage.textContent || lastMessage.innerText;
                    if (text.includes('JARVIS:')) {
                        const jarvisText = text.split('JARVIS:')[1]?.trim();
                        if (jarvisText) {
                            speakText(jarvisText);
                        }
                    }
                }
            }
        }
        
        function toggleMicrophone() {
            console.log('🎤 Micro activé/désactivé');
            speakText('Microphone activé');
        }
        
        function toggleCamera() {
            console.log('📷 Caméra activée/désactivée');
            speakText('Caméra activée');
        }
        
        function openWeb() {
            console.log('🌐 Recherche web ouverte');
            speakText('Recherche web ouverte');
        }
        
        function activateJarvisAudio() {
            console.log('🔊 Audio JARVIS activé');
            speakText('Audio JARVIS Expert activé. Bonjour Jean-Luc, je suis prêt à communiquer.');
        }

        // Animation des pensées - EXACTEMENT comme votre HTML
        setInterval(() => {
            const timeElements = document.querySelectorAll('[id*="time"]');
            timeElements.forEach(element => {
                if (element && element.textContent.includes('⏰')) {
                    const now = new Date();
                    const timeStr = now.getHours().toString().padStart(2,'0') + ':' + 
                                   now.getMinutes().toString().padStart(2,'0') + ':' + 
                                   now.getSeconds().toString().padStart(2,'0');
                    element.textContent = '⏰ [' + timeStr + ']';
                }
            });
        }, 1000);
        </script>
        """)
        
        # Fonctions
        def test_audio():
            return update_thoughts_html("Test audio lancé - Interface HTML originale")
        
        def clear_chat():
            return [], update_thoughts_html("Conversation effacée - Interface HTML prête")
        
        # Connexions
        send_btn.click(
            fn=process_jarvis_message_html,
            inputs=[user_input, main_chat],
            outputs=[main_chat, user_input, thoughts_display]
        )
        
        user_input.submit(
            fn=process_jarvis_message_html,
            inputs=[user_input, main_chat],
            outputs=[main_chat, user_input, thoughts_display]
        )
        
        pause_btn.click(
            fn=test_audio,
            outputs=[thoughts_display],
            js="() => speakText('JARVIS mis en pause')"
        )
        
        stop_btn.click(
            fn=clear_chat,
            outputs=[main_chat, thoughts_display],
            js="() => speakText('Conversation arrêtée')"
        )
    
    return interface

if __name__ == "__main__":
    print("💬 JARVIS INTERFACE HTML ORIGINALE - JEAN-LUC PASSAVE")
    print("=" * 70)
    print("🎨 Interface basée sur votre code HTML d'origine")
    print("🔧 Configuration: DeepSeek R1 8B via VLLM localhost:8000")
    print("🧠 Mémoire thermique: thermal_memory_persistent.json")
    print("🔊 Audio navigateur + Pensées réelles + Contrôles multimédia")
    print("🌐 Interface disponible sur localhost:8109")
    print("=" * 70)
    
    interface = create_jarvis_html_interface()
    interface.launch(
        server_name="localhost",
        server_port=8109,
        share=False,
        debug=True,
        show_error=True
    )
