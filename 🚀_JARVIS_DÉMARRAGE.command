#!/bin/bash
# 🚀 JARVIS DÉMARRAGE RAPIDE - JEAN-LUC PASSAVE
# APPLICATION VALIDÉE 22 JUIN 2025 À 9H50
# Double-clic pour lancer JARVIS Electron

clear
echo "🚀 =================================="
echo "🤖 JARVIS ELECTRON - JEAN-LUC PASSAVE"
echo "🚀 =================================="
echo "📅 Version validée : 22 juin 2025 9h50"
echo "🖥️ Application : jarvis_electron_nouveau.js"
echo "🔌 Port JARVIS : 8101 (corrigé)"
echo "🚀 =================================="
echo ""

# Aller dans le répertoire de l'application
cd "/Volumes/seagate/Louna_Electron_Latest"

# Vérifier les fichiers nécessaires
echo "🔍 VÉRIFICATION DES FICHIERS..."
if [ ! -f "jarvis_electron_nouveau.js" ]; then
    echo "❌ ERREUR: Application Electron manquante"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ ! -f "jarvis_architecture_multi_fenetres.py" ]; then
    echo "❌ ERREUR: Interface JARVIS manquante"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo "✅ Tous les fichiers présents"
echo ""

# Nettoyer les anciens processus
echo "🧹 NETTOYAGE DES ANCIENS PROCESSUS..."
pkill -f "jarvis" 2>/dev/null || true
pkill -f "electron" 2>/dev/null || true
sleep 2
echo "✅ Nettoyage terminé"
echo ""

# Démarrer JARVIS principal
echo "🧠 DÉMARRAGE JARVIS PRINCIPAL (PORT 8101)..."
python3 jarvis_architecture_multi_fenetres.py &
JARVIS_PID=$!
echo "✅ JARVIS démarré (PID: $JARVIS_PID)"

# Attendre que JARVIS soit prêt
echo "⏳ Attente du démarrage complet..."
sleep 10

# Vérifier que JARVIS est accessible
echo "🔍 Vérification de la connexion..."
if curl -s http://localhost:8101 > /dev/null; then
    echo "✅ JARVIS accessible sur port 8101"
else
    echo "⚠️ JARVIS encore en démarrage..."
    sleep 5
fi

echo ""
echo "🖥️ LANCEMENT APPLICATION ELECTRON..."
echo "🎯 Interface validée Jean-Luc Passave"
echo "🧠 QI 648 + 100M neurones actifs"
echo "🚀 Toutes fonctionnalités activées"
echo ""

# Lancer l'application Electron validée
npm start

# Nettoyage à la fermeture
echo ""
echo "🛑 FERMETURE DE JARVIS..."
kill $JARVIS_PID 2>/dev/null || true
echo "✅ JARVIS arrêté proprement"
echo ""
read -p "Appuyez sur Entrée pour fermer cette fenêtre..."
