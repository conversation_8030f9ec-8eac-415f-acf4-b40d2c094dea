#!/usr/bin/env python3
"""
🔧 JARVIS CODE ASSEMBLER - JEAN-LUC PASSAVE
Système de génération de code par segments pour contourner les limitations
"""

import os
import json
import time
from datetime import datetime

class JarvisCodeAssembler:
    def __init__(self, project_name="nouveau_projet"):
        self.project_name = project_name
        self.segments = []
        self.project_dir = f"PROJETS_JARVIS/{project_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.segments_file = f"{self.project_dir}/segments.json"
        
        # Créer le répertoire projet
        os.makedirs(self.project_dir, exist_ok=True)
        
        print(f"🚀 JARVIS Code Assembler initialisé")
        print(f"📁 Projet: {self.project_dir}")
    
    def ajouter_segment(self, nom_fichier, code_segment, description=""):
        """Ajoute un segment de code au projet"""
        segment = {
            "nom_fichier": nom_fichier,
            "code": code_segment,
            "description": description,
            "timestamp": datetime.now().isoformat(),
            "ordre": len(self.segments) + 1
        }
        
        self.segments.append(segment)
        
        # Sauvegarder immédiatement
        self.sauvegarder_segments()
        
        # Créer le fichier individuel
        fichier_path = f"{self.project_dir}/{nom_fichier}"
        with open(fichier_path, 'w', encoding='utf-8') as f:
            f.write(code_segment)
        
        print(f"✅ Segment ajouté: {nom_fichier} ({len(code_segment)} caractères)")
        return fichier_path
    
    def sauvegarder_segments(self):
        """Sauvegarde la liste des segments"""
        with open(self.segments_file, 'w', encoding='utf-8') as f:
            json.dump(self.segments, f, indent=2, ensure_ascii=False)
    
    def assembler_projet_complet(self):
        """Assemble tous les segments en un projet complet"""
        if not self.segments:
            print("❌ Aucun segment à assembler")
            return None
        
        # Créer un fichier README avec la structure
        readme_content = f"""# 🤖 PROJET JARVIS - {self.project_name.upper()}

## 📅 Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}

## 📁 STRUCTURE DU PROJET

"""
        
        for i, segment in enumerate(self.segments, 1):
            readme_content += f"{i}. **{segment['nom_fichier']}** - {segment['description']}\n"
        
        readme_content += f"""

## 🚀 UTILISATION

Ce projet a été généré par JARVIS en {len(self.segments)} segments.
Chaque fichier est complet et fonctionnel.

## 📊 STATISTIQUES

- **Nombre de fichiers**: {len(self.segments)}
- **Lignes de code total**: {sum(len(s['code'].split('\n')) for s in self.segments)}
- **Caractères total**: {sum(len(s['code']) for s in self.segments):,}

---
**Généré par JARVIS - Jean-Luc Passave**
"""
        
        readme_path = f"{self.project_dir}/README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"🎉 PROJET ASSEMBLÉ COMPLET !")
        print(f"📁 Répertoire: {self.project_dir}")
        print(f"📄 {len(self.segments)} fichiers créés")
        print(f"📖 README.md généré")
        
        return self.project_dir
    
    def lister_segments(self):
        """Liste tous les segments du projet"""
        if not self.segments:
            print("📭 Aucun segment dans le projet")
            return
        
        print(f"📋 SEGMENTS DU PROJET '{self.project_name}':")
        print("=" * 50)
        
        for i, segment in enumerate(self.segments, 1):
            lignes = len(segment['code'].split('\n'))
            print(f"{i}. {segment['nom_fichier']}")
            print(f"   📝 {segment['description']}")
            print(f"   📊 {lignes} lignes, {len(segment['code'])} caractères")
            print(f"   ⏰ {segment['timestamp']}")
            print()
    
    def generer_script_lancement(self):
        """Génère un script pour lancer le projet"""
        # Détecter le type de projet
        fichiers = [s['nom_fichier'] for s in self.segments]
        
        if any(f.endswith('.py') for f in fichiers):
            # Projet Python
            script_content = f"""#!/bin/bash
# 🚀 SCRIPT DE LANCEMENT - {self.project_name.upper()}
# Généré par JARVIS - Jean-Luc Passave

echo "🚀 Lancement du projet {self.project_name}"
echo "📁 Répertoire: {self.project_dir}"

# Activer l'environnement virtuel si présent
if [ -d "venv" ]; then
    source venv/bin/activate
    echo "✅ Environnement virtuel activé"
fi

# Installer les dépendances si requirements.txt existe
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
    echo "✅ Dépendances installées"
fi

# Lancer l'application principale
if [ -f "main.py" ]; then
    python main.py
elif [ -f "app.py" ]; then
    python app.py
else
    echo "❌ Fichier principal non trouvé"
fi
"""
        
        elif any(f.endswith('.js') for f in fichiers):
            # Projet Node.js
            script_content = f"""#!/bin/bash
# 🚀 SCRIPT DE LANCEMENT - {self.project_name.upper()}
# Généré par JARVIS - Jean-Luc Passave

echo "🚀 Lancement du projet {self.project_name}"
echo "📁 Répertoire: {self.project_dir}"

# Installer les dépendances si package.json existe
if [ -f "package.json" ]; then
    npm install
    echo "✅ Dépendances Node.js installées"
fi

# Lancer l'application
if [ -f "main.js" ]; then
    node main.js
elif [ -f "app.js" ]; then
    node app.js
elif [ -f "index.js" ]; then
    node index.js
else
    echo "❌ Fichier principal non trouvé"
fi
"""
        else:
            # Script générique
            script_content = f"""#!/bin/bash
# 🚀 SCRIPT DE LANCEMENT - {self.project_name.upper()}
# Généré par JARVIS - Jean-Luc Passave

echo "🚀 Projet {self.project_name} prêt"
echo "📁 Répertoire: {self.project_dir}"
echo "📄 Fichiers disponibles:"

"""
            for segment in self.segments:
                script_content += f'echo "   - {segment["nom_fichier"]}"\n'
        
        script_path = f"{self.project_dir}/lancer.sh"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # Rendre exécutable
        os.chmod(script_path, 0o755)
        
        print(f"🎯 Script de lancement créé: {script_path}")
        return script_path

# ============================================================================
# FONCTIONS UTILITAIRES POUR JARVIS
# ============================================================================

def creer_nouveau_projet(nom_projet):
    """Crée un nouveau projet avec l'assembler"""
    assembler = JarvisCodeAssembler(nom_projet)
    return assembler

def exemple_utilisation():
    """Exemple d'utilisation de l'assembler"""
    print("🔧 EXEMPLE D'UTILISATION JARVIS CODE ASSEMBLER")
    print("=" * 50)
    
    # Créer un projet exemple
    assembler = JarvisCodeAssembler("exemple_flask")
    
    # Ajouter des segments
    assembler.ajouter_segment(
        "app.py",
        '''from flask import Flask, render_template

app = Flask(__name__)

@app.route('/')
def home():
    return render_template('index.html')

if __name__ == '__main__':
    app.run(debug=True)
''',
        "Application Flask principale"
    )
    
    assembler.ajouter_segment(
        "requirements.txt",
        '''Flask==2.3.3
Jinja2==3.1.2
''',
        "Dépendances Python"
    )
    
    # Assembler le projet
    assembler.assembler_projet_complet()
    assembler.generer_script_lancement()
    
    print("✅ Projet exemple créé avec succès !")

if __name__ == "__main__":
    exemple_utilisation()
