#!/bin/bash

# 🧪 TEST NAVIGATION ELECTRON JARVIS - JEAN-LUC PASSAVE
# Test des nouvelles fonctionnalités de navigation

echo "🧪 TEST NAVIGATION ELECTRON JARVIS"
echo "=================================="

# Aller dans le bon répertoire
cd "$(dirname "$0")"

echo "📁 Répertoire: $(pwd)"

# Nettoyer les anciens processus
echo "🧹 Nettoyage des processus..."
pkill -f "jarvis_interface_communication_principale.py" 2>/dev/null || true
pkill -f "electron.*jarvis" 2>/dev/null || true
sleep 2

echo ""
echo "🚀 DÉMARRAGE INTERFACE DASHBOARD (PORT 7864)"
echo "============================================="

# Démarrer l'interface Dashboard
source venv_gradio/bin/activate && python3 jarvis_interface_communication_principale.py &
INTERFACE_PID=$!

echo "✅ Interface démarrée (PID: $INTERFACE_PID)"
echo "⏳ Attente du démarrage (8 secondes)..."
sleep 8

# Vérifier que l'interface est accessible
if curl -s http://localhost:7864 > /dev/null; then
    echo "✅ Interface Dashboard accessible sur port 7864"
else
    echo "❌ Interface Dashboard non accessible"
    kill $INTERFACE_PID 2>/dev/null || true
    exit 1
fi

echo ""
echo "🖥️ DÉMARRAGE APPLICATION ELECTRON AVEC NAVIGATION"
echo "================================================="

# Démarrer Electron en arrière-plan pour test
npm start &
ELECTRON_PID=$!

echo "✅ Application Electron démarrée (PID: $ELECTRON_PID)"
echo "⏳ Attente du démarrage Electron (5 secondes)..."
sleep 5

echo ""
echo "🎯 FONCTIONNALITÉS DE NAVIGATION AJOUTÉES"
echo "========================================="

echo "✅ Bouton 'Accueil' dans l'en-tête"
echo "✅ Bouton 'Dashboard' dans l'en-tête"
echo "✅ Bouton 'Actualiser' dans l'en-tête"
echo "✅ Bouton 'Accueil' dans la barre latérale (vert)"
echo "✅ Navigation vers port 7864 (Dashboard validé)"

echo ""
echo "🎮 UTILISATION DES BOUTONS DE NAVIGATION"
echo "========================================"

echo "🏠 Bouton Accueil : Recharge l'interface principale"
echo "📊 Bouton Dashboard : Redirige vers http://localhost:7864"
echo "🔄 Bouton Actualiser : Actualise la page courante"
echo "🎯 Bouton Accueil (sidebar) : Retour à l'interface principale"

echo ""
echo "⌨️ RACCOURCIS CLAVIER DISPONIBLES"
echo "================================="

echo "Cmd+R (Mac) / Ctrl+R : Actualiser"
echo "Cmd+W (Mac) / Ctrl+W : Fermer"
echo "Cmd+Q (Mac) / Alt+F4 : Quitter"

echo ""
echo "🎉 TEST TERMINÉ AVEC SUCCÈS !"
echo "============================"

echo "📱 Votre application Electron est maintenant ouverte avec :"
echo "   - Navigation améliorée"
echo "   - Boutons de retour à l'accueil"
echo "   - Interface Dashboard sur port 7864"
echo ""
echo "Appuyez sur Ctrl+C pour arrêter les services"

# Attendre l'interruption
trap 'echo "🛑 Arrêt des services..."; kill $INTERFACE_PID $ELECTRON_PID 2>/dev/null; exit 0' INT

# Garder les services actifs
wait
