#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AGENT SIMPLE NATUREL - JEAN-LUC PASSAVE
Agent qui répond naturellement sans réponses forcées
"""

from flask import Flask, request, jsonify
import time

app = Flask(__name__)

class AgentNaturel:
    """Agent simple qui répond naturellement"""
    
    def __init__(self):
        self.nom = "JARVIS"
        self.proprietaire = "Jean-Luc Passave"
    
    def repondre(self, message, with_thoughts=False):
        """Répond naturellement au message"""

        # Réponses simples et directes
        msg = message.lower()

        if "bonjour" in msg or "salut" in msg:
            response = f"Bonjour {self.proprietaire}."
            thoughts = f"L'utilisateur me salue. Je réponds poliment."

        elif "qui êtes-vous" in msg or "votre nom" in msg:
            response = f"Je suis {self.nom}."
            thoughts = f"Question sur mon identité. Je me présente simplement."

        elif "comment allez-vous" in msg or "ça va" in msg:
            response = "Ça va bien."
            thoughts = f"Question sur mon état. Je réponds positivement."

        elif "merci" in msg:
            response = "De rien."
            thoughts = f"Remerciement de {self.proprietaire}. Je réponds naturellement."

        elif "aide" in msg:
            response = "Que voulez-vous faire ?"
            thoughts = f"Demande d'aide. Je demande des précisions."

        elif "test" in msg:
            response = f"Je suis {self.nom}, opérationnel."
            thoughts = f"Test de fonctionnement. Je confirme mon statut."

        elif "code" in msg or "programmation" in msg:
            response = "Quel langage ?"
            thoughts = f"Question sur la programmation. Je demande le langage."

        else:
            response = "Je vous écoute."
            thoughts = f"Message général de {self.proprietaire}. J'écoute attentivement."

        if with_thoughts:
            return f"<think>{thoughts}</think>\n\n{response}"
        else:
            return response

# Instance de l'agent
agent = AgentNaturel()

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint compatible OpenAI"""
    try:
        data = request.json
        messages = data.get('messages', [])

        if not messages:
            return jsonify({"error": "Aucun message"}), 400

        # Vérifier si on demande des pensées (message système)
        has_system_message = any(msg.get('role') == 'system' for msg in messages)
        with_thoughts = has_system_message and any('think' in msg.get('content', '').lower() for msg in messages)

        # Récupérer le message utilisateur
        user_message = messages[-1].get('content', '')

        # Réponse de l'agent
        reponse = agent.repondre(user_message, with_thoughts=with_thoughts)

        # Format OpenAI
        return jsonify({
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "jarvis-naturel",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": reponse
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(user_message.split()),
                "completion_tokens": len(reponse.split()),
                "total_tokens": len(user_message.split()) + len(reponse.split())
            }
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de santé"""
    return jsonify({
        "status": "ok",
        "agent": "JARVIS Naturel"
    })

def main():
    """Fonction principale"""
    print("🤖 AGENT NATUREL JARVIS - JEAN-LUC PASSAVE")
    print("🚫 AUCUNE RÉPONSE FORCÉE")
    print("=" * 40)
    print(f"👤 Propriétaire: {agent.proprietaire}")
    print(f"🤖 Agent: {agent.nom}")
    print("🌐 Serveur: http://localhost:8000")
    print("✅ Réponses naturelles")
    print("=" * 40)
    
    app.run(host='localhost', port=8000, debug=False)

if __name__ == "__main__":
    main()
