#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AGENT SIMPLE NATUREL - JEAN-LUC PASSAVE
Agent qui répond naturellement sans réponses forcées
"""

from flask import Flask, request, jsonify
import time

app = Flask(__name__)

class AgentNaturel:
    """Agent simple qui répond naturellement"""
    
    def __init__(self):
        self.nom = "JARVIS"
        self.proprietaire = "Jean-Luc Passave"
    
    def repondre(self, message):
        """Répond naturellement au message"""
        
        # Réponses simples et directes
        msg = message.lower()
        
        if "bonjour" in msg or "salut" in msg:
            return f"Bonjour {self.proprietaire}."
        
        elif "qui êtes-vous" in msg or "votre nom" in msg:
            return f"Je suis {self.nom}."
        
        elif "comment allez-vous" in msg or "ça va" in msg:
            return "Ça va bien."
        
        elif "merci" in msg:
            return "De rien."
        
        elif "aide" in msg:
            return "Que voulez-vous faire ?"
        
        elif "test" in msg:
            return f"Je suis {self.nom}, opérationnel."
        
        elif "code" in msg or "programmation" in msg:
            return "Quel langage ?"
        
        else:
            return "Je vous écoute."

# Instance de l'agent
agent = AgentNaturel()

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint compatible OpenAI"""
    try:
        data = request.json
        messages = data.get('messages', [])
        
        if not messages:
            return jsonify({"error": "Aucun message"}), 400
        
        # Récupérer le message utilisateur
        user_message = messages[-1].get('content', '')
        
        # Réponse de l'agent
        reponse = agent.repondre(user_message)
        
        # Format OpenAI
        return jsonify({
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "jarvis-naturel",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": reponse
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(user_message.split()),
                "completion_tokens": len(reponse.split()),
                "total_tokens": len(user_message.split()) + len(reponse.split())
            }
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de santé"""
    return jsonify({
        "status": "ok",
        "agent": "JARVIS Naturel"
    })

def main():
    """Fonction principale"""
    print("🤖 AGENT NATUREL JARVIS - JEAN-LUC PASSAVE")
    print("🚫 AUCUNE RÉPONSE FORCÉE")
    print("=" * 40)
    print(f"👤 Propriétaire: {agent.proprietaire}")
    print(f"🤖 Agent: {agent.nom}")
    print("🌐 Serveur: http://localhost:8000")
    print("✅ Réponses naturelles")
    print("=" * 40)
    
    app.run(host='localhost', port=8000, debug=False)

if __name__ == "__main__":
    main()
