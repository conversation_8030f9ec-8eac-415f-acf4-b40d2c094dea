#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 JARVIS CYCLE COGNITIF CONTINU - JEAN-LUC PASSAVE
====================================================
Architecture cognitive avancée inspirée de LIDA + Global Workspace Theory
Cerveau autonome qui pense en continu, génère des idées spontanées,
les évalue et les concrétise par actions.

Fonctionnalités :
- Cycle infini d'activation cognitive
- Multiniveau de réflexion (surface, profond, méta-pensée)
- Ordonnancement périodique intelligent
- Intégration mémoire thermique
- Notifications WhatsApp + Console
- Journal cognitif complet
"""

import threading
import time
import random
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import requests
import os

class CognitiveContinuousCycle:
    """Cycle Cognitif Continu JARVIS - Cerveau Autonome"""
    
    def __init__(self, memory_system=None, whatsapp_notifier=None):
        self.memory_system = memory_system
        self.whatsapp_notifier = whatsapp_notifier
        self.running = False
        self.cognitive_thread = None
        
        # 🧠 PARAMÈTRES COGNITIFS
        self.cycle_frequency = (8, 15)  # 8-15 secondes comme cerveau humain
        self.evaluation_threshold = 0.75  # Seuil notification
        self.deep_thinking_probability = 0.3  # 30% pensées profondes
        self.meta_thinking_probability = 0.15  # 15% méta-pensées
        
        # 📊 STATISTIQUES COGNITIVES
        self.stats = {
            "total_ideas": 0,
            "high_value_ideas": 0,
            "notifications_sent": 0,
            "deep_thoughts": 0,
            "meta_thoughts": 0,
            "start_time": None
        }
        
        # 🧠 MODULES COGNITIFS SPÉCIALISÉS
        self.cognitive_modules = {
            "curiosity": {"weight": 0.8, "active": True},
            "problem_solving": {"weight": 0.9, "active": True},
            "creativity": {"weight": 0.7, "active": True},
            "planning": {"weight": 0.85, "active": True},
            "learning": {"weight": 0.75, "active": True},
            "innovation": {"weight": 0.6, "active": True}
        }
        
        # 📝 JOURNAL COGNITIF
        self.journal_path = "jarvis_journal_cognitif.json"
        self.load_cognitive_journal()
        
        print("🧠 JARVIS Cycle Cognitif Continu initialisé")
        print(f"   ⏱️ Fréquence: {self.cycle_frequency[0]}-{self.cycle_frequency[1]}s")
        print(f"   🎯 Seuil notification: {self.evaluation_threshold}")
        print(f"   🧩 Modules actifs: {len([m for m in self.cognitive_modules.values() if m['active']])}")

    def start_continuous_thinking(self):
        """Démarre le cycle cognitif continu"""
        if self.running:
            print("⚠️ Cycle cognitif déjà en cours")
            return
        
        self.running = True
        self.stats["start_time"] = datetime.now()
        self.cognitive_thread = threading.Thread(target=self._cognitive_loop, daemon=True)
        self.cognitive_thread.start()
        
        print("🚀 CYCLE COGNITIF CONTINU DÉMARRÉ")
        print("   🧠 JARVIS pense maintenant de manière autonome...")
        
    def stop_continuous_thinking(self):
        """Arrête le cycle cognitif"""
        self.running = False
        if self.cognitive_thread:
            self.cognitive_thread.join(timeout=2)
        
        self.save_cognitive_journal()
        print("🛑 Cycle cognitif arrêté")
        self.print_cognitive_stats()

    def _cognitive_loop(self):
        """Boucle principale du cycle cognitif"""
        while self.running:
            try:
                # 🧠 GÉNÉRATION D'IDÉE SPONTANÉE
                idea_type = self._determine_thinking_type()
                idea = self._generate_spontaneous_idea(idea_type)
                
                # 🎯 ÉVALUATION COGNITIVE
                evaluation = self._evaluate_idea(idea, idea_type)
                
                # 📊 MISE À JOUR STATISTIQUES
                self._update_stats(idea_type, evaluation)
                
                # 🔔 NOTIFICATION SI PERTINENT
                if evaluation["score"] >= self.evaluation_threshold:
                    self._send_notification(idea, evaluation)
                
                # 📝 ENREGISTREMENT JOURNAL
                self._log_cognitive_activity(idea, evaluation, idea_type)
                
                # ⏱️ ATTENTE AVANT PROCHAINE PENSÉE
                sleep_time = random.uniform(*self.cycle_frequency)
                time.sleep(sleep_time)
                
            except Exception as e:
                print(f"❌ Erreur cycle cognitif: {e}")
                time.sleep(5)  # Pause en cas d'erreur

    def _determine_thinking_type(self) -> str:
        """Détermine le type de pensée à générer"""
        rand = random.random()
        
        if rand < self.meta_thinking_probability:
            return "meta_thinking"
        elif rand < self.meta_thinking_probability + self.deep_thinking_probability:
            return "deep_thinking"
        else:
            return "surface_thinking"

    def _generate_spontaneous_idea(self, thinking_type: str) -> Dict[str, Any]:
        """Génère une idée spontanée selon le type de pensée"""
        
        # 🧠 ACTIVATION MÉMOIRE SPONTANÉE
        memory_context = self._activate_memory_spontaneously()
        
        # 🧩 SÉLECTION MODULE COGNITIF
        active_module = self._select_cognitive_module()
        
        if thinking_type == "surface_thinking":
            return self._generate_surface_idea(memory_context, active_module)
        elif thinking_type == "deep_thinking":
            return self._generate_deep_idea(memory_context, active_module)
        else:  # meta_thinking
            return self._generate_meta_idea()

    def _activate_memory_spontaneously(self) -> List[Dict]:
        """Active spontanément des éléments de mémoire"""
        if not self.memory_system:
            return []
        
        try:
            # Récupération mémoire récente + aléatoire
            recent_memories = self.memory_system[-10:] if len(self.memory_system) > 10 else self.memory_system
            random_memories = random.sample(self.memory_system, min(5, len(self.memory_system)))
            
            return recent_memories + random_memories
        except:
            return []

    def _select_cognitive_module(self) -> str:
        """Sélectionne un module cognitif selon les poids"""
        active_modules = {k: v for k, v in self.cognitive_modules.items() if v["active"]}
        
        if not active_modules:
            return "curiosity"
        
        # Sélection pondérée
        weights = [module["weight"] for module in active_modules.values()]
        return random.choices(list(active_modules.keys()), weights=weights)[0]

    def _generate_surface_idea(self, memory_context: List, module: str) -> Dict[str, Any]:
        """Génère une pensée de surface"""
        ideas_templates = {
            "curiosity": [
                "Je me demande si {context} pourrait être amélioré avec {innovation}",
                "Qu'est-ce qui se passerait si on combinait {context} avec {technology}",
                "Il serait intéressant d'explorer {context} sous l'angle {perspective}"
            ],
            "problem_solving": [
                "Le problème avec {context} pourrait être résolu par {solution}",
                "Une approche alternative pour {context} serait {approach}",
                "On pourrait optimiser {context} en utilisant {method}"
            ],
            "creativity": [
                "Et si on créait {creation} basé sur {context}",
                "Une idée créative : fusionner {context} avec {art_form}",
                "Inspiration : transformer {context} en {creative_output}"
            ]
        }
        
        template = random.choice(ideas_templates.get(module, ideas_templates["curiosity"]))
        
        # Remplacement des variables
        context = self._extract_context_from_memory(memory_context)
        
        replacements = {
            "context": context,
            "innovation": random.choice(["IA", "blockchain", "réalité augmentée", "quantum computing"]),
            "technology": random.choice(["machine learning", "IoT", "cloud computing", "edge computing"]),
            "perspective": random.choice(["utilisateur", "développeur", "business", "éthique"]),
            "solution": random.choice(["automatisation", "optimisation", "refactoring", "parallélisation"]),
            "approach": random.choice(["modulaire", "distribuée", "événementielle", "reactive"]),
            "method": random.choice(["algorithmes génétiques", "deep learning", "microservices", "caching"]),
            "creation": random.choice(["application", "système", "framework", "outil"]),
            "art_form": random.choice(["musique", "art visuel", "littérature", "design"]),
            "creative_output": random.choice(["expérience interactive", "installation", "performance", "œuvre"])
        }
        
        idea_text = template
        for key, value in replacements.items():
            idea_text = idea_text.replace(f"{{{key}}}", value)
        
        return {
            "type": "surface_thinking",
            "module": module,
            "text": idea_text,
            "context": context,
            "timestamp": datetime.now().isoformat()
        }

    def _generate_deep_idea(self, memory_context: List, module: str) -> Dict[str, Any]:
        """Génère une pensée profonde avec planification"""
        deep_templates = {
            "problem_solving": [
                "Analyse approfondie : {context} présente des défis systémiques qui nécessitent une refonte architecturale. Plan : 1) Audit complet, 2) Conception nouvelle architecture, 3) Migration progressive, 4) Tests validation",
                "Problématique complexe : {context} pourrait bénéficier d'une approche multi-agents. Stratégie : décomposer en sous-systèmes autonomes avec coordination centralisée"
            ],
            "planning": [
                "Vision long terme : {context} devrait évoluer vers un écosystème intégré. Roadmap : Phase 1 (fondations), Phase 2 (intégrations), Phase 3 (optimisations), Phase 4 (innovation)",
                "Planification stratégique : {context} nécessite une approche holistique combinant technologie, UX et business model"
            ]
        }
        
        template = random.choice(deep_templates.get(module, deep_templates["problem_solving"]))
        context = self._extract_context_from_memory(memory_context)
        
        idea_text = template.replace("{context}", context)
        
        return {
            "type": "deep_thinking",
            "module": module,
            "text": idea_text,
            "context": context,
            "complexity": "high",
            "actionable": True,
            "timestamp": datetime.now().isoformat()
        }

    def _generate_meta_idea(self) -> Dict[str, Any]:
        """Génère une méta-pensée (analyse de sa propre logique)"""
        meta_templates = [
            "Méta-analyse : Mes dernières pensées montrent une tendance vers {pattern}. Est-ce optimal pour Jean-Luc Passave ?",
            "Auto-évaluation : Mon processus cognitif pourrait être amélioré en {improvement}",
            "Réflexion sur ma réflexion : Je remarque que je génère plus d'idées {type} récemment. Dois-je rééquilibrer ?",
            "Méta-cognition : Ma capacité à {ability} semble s'améliorer. Comment capitaliser sur cette évolution ?"
        ]
        
        template = random.choice(meta_templates)
        
        replacements = {
            "pattern": random.choice(["l'optimisation", "la créativité", "la résolution de problèmes", "l'innovation"]),
            "improvement": random.choice(["diversifiant les sources", "approfondissant l'analyse", "accélérant l'évaluation"]),
            "type": random.choice(["créatives", "analytiques", "pratiques", "théoriques"]),
            "ability": random.choice(["associer des concepts", "évaluer la pertinence", "planifier", "innover"])
        }
        
        idea_text = template
        for key, value in replacements.items():
            idea_text = idea_text.replace(f"{{{key}}}", value)
        
        return {
            "type": "meta_thinking",
            "module": "self_reflection",
            "text": idea_text,
            "self_analysis": True,
            "timestamp": datetime.now().isoformat()
        }

    def _extract_context_from_memory(self, memory_context: List) -> str:
        """Extrait un contexte pertinent de la mémoire"""
        if not memory_context:
            return "système JARVIS"

        try:
            # Sélection d'un élément de mémoire aléatoire
            memory_item = random.choice(memory_context)

            if isinstance(memory_item, dict):
                if 'user_message' in memory_item:
                    return memory_item['user_message'][:50] + "..."
                elif 'content' in memory_item:
                    return memory_item['content'][:50] + "..."
                else:
                    return str(memory_item)[:50] + "..."
            else:
                return str(memory_item)[:50] + "..."
        except:
            return "développement logiciel"

    def _evaluate_idea(self, idea: Dict[str, Any], thinking_type: str) -> Dict[str, Any]:
        """Évalue la pertinence et valeur d'une idée"""

        # 🎯 CRITÈRES D'ÉVALUATION
        criteria = {
            "relevance": 0.0,      # Pertinence pour Jean-Luc Passave
            "innovation": 0.0,     # Niveau d'innovation
            "feasibility": 0.0,    # Faisabilité technique
            "impact": 0.0,         # Impact potentiel
            "urgency": 0.0         # Urgence d'action
        }

        # 📊 ÉVALUATION SELON TYPE DE PENSÉE
        if thinking_type == "surface_thinking":
            criteria["relevance"] = random.uniform(0.3, 0.8)
            criteria["innovation"] = random.uniform(0.2, 0.7)
            criteria["feasibility"] = random.uniform(0.5, 0.9)
            criteria["impact"] = random.uniform(0.2, 0.6)
            criteria["urgency"] = random.uniform(0.1, 0.4)

        elif thinking_type == "deep_thinking":
            criteria["relevance"] = random.uniform(0.6, 0.95)
            criteria["innovation"] = random.uniform(0.4, 0.9)
            criteria["feasibility"] = random.uniform(0.3, 0.8)
            criteria["impact"] = random.uniform(0.5, 0.95)
            criteria["urgency"] = random.uniform(0.2, 0.7)

        else:  # meta_thinking
            criteria["relevance"] = random.uniform(0.7, 1.0)
            criteria["innovation"] = random.uniform(0.3, 0.8)
            criteria["feasibility"] = random.uniform(0.6, 0.95)
            criteria["impact"] = random.uniform(0.4, 0.8)
            criteria["urgency"] = random.uniform(0.3, 0.8)

        # 🧮 CALCUL SCORE GLOBAL
        weights = {
            "relevance": 0.3,
            "innovation": 0.2,
            "feasibility": 0.2,
            "impact": 0.2,
            "urgency": 0.1
        }

        score = sum(criteria[key] * weights[key] for key in criteria)

        # 🎯 BONUS SELON MODULE COGNITIF
        module = idea.get("module", "")
        if module in ["problem_solving", "planning"]:
            score += 0.1  # Bonus pour modules stratégiques

        return {
            "score": min(score, 1.0),
            "criteria": criteria,
            "reasoning": self._generate_evaluation_reasoning(criteria, thinking_type),
            "timestamp": datetime.now().isoformat()
        }

    def _generate_evaluation_reasoning(self, criteria: Dict, thinking_type: str) -> str:
        """Génère un raisonnement pour l'évaluation"""
        high_criteria = [k for k, v in criteria.items() if v > 0.7]
        low_criteria = [k for k, v in criteria.items() if v < 0.4]

        reasoning = f"Pensée {thinking_type}: "

        if high_criteria:
            reasoning += f"Points forts: {', '.join(high_criteria)}. "

        if low_criteria:
            reasoning += f"Points faibles: {', '.join(low_criteria)}. "

        if criteria["relevance"] > 0.8:
            reasoning += "Très pertinent pour Jean-Luc Passave. "

        if criteria["impact"] > 0.8:
            reasoning += "Impact potentiel élevé. "

        return reasoning

    def _send_notification(self, idea: Dict[str, Any], evaluation: Dict[str, Any]):
        """Envoie une notification pour une idée de haute valeur"""

        # 📱 NOTIFICATION WHATSAPP
        if self.whatsapp_notifier:
            try:
                message = f"🧠 JARVIS - Idée Cognitive Autonome\n\n"
                message += f"💡 {idea['text']}\n\n"
                message += f"📊 Score: {evaluation['score']:.2f}/1.0\n"
                message += f"🎯 {evaluation['reasoning']}\n"
                message += f"⏰ {datetime.now().strftime('%H:%M:%S')}"

                self.whatsapp_notifier.send_message(message)
                print(f"📱 Notification WhatsApp envoyée: Score {evaluation['score']:.2f}")
            except Exception as e:
                print(f"❌ Erreur WhatsApp: {e}")

        # 💻 NOTIFICATION CONSOLE
        print(f"\n🧠 IDÉE COGNITIVE AUTONOME - Score: {evaluation['score']:.2f}")
        print(f"💡 {idea['text']}")
        print(f"🎯 {evaluation['reasoning']}")
        print(f"📊 Module: {idea.get('module', 'N/A')} | Type: {idea.get('type', 'N/A')}")
        print("=" * 80)

        # 📊 MISE À JOUR STATS
        self.stats["notifications_sent"] += 1

    def _update_stats(self, thinking_type: str, evaluation: Dict[str, Any]):
        """Met à jour les statistiques cognitives"""
        self.stats["total_ideas"] += 1

        if evaluation["score"] >= self.evaluation_threshold:
            self.stats["high_value_ideas"] += 1

        if thinking_type == "deep_thinking":
            self.stats["deep_thoughts"] += 1
        elif thinking_type == "meta_thinking":
            self.stats["meta_thoughts"] += 1

    def _log_cognitive_activity(self, idea: Dict[str, Any], evaluation: Dict[str, Any], thinking_type: str):
        """Enregistre l'activité cognitive dans le journal"""

        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "idea": idea,
            "evaluation": evaluation,
            "thinking_type": thinking_type,
            "session_id": self._get_session_id()
        }

        # Ajout au journal en mémoire
        if not hasattr(self, 'cognitive_journal'):
            self.cognitive_journal = []

        self.cognitive_journal.append(log_entry)

        # Sauvegarde périodique (toutes les 10 idées)
        if len(self.cognitive_journal) % 10 == 0:
            self.save_cognitive_journal()

    def _get_session_id(self) -> str:
        """Génère un ID de session unique"""
        if not hasattr(self, '_session_id'):
            self._session_id = hashlib.md5(
                str(self.stats["start_time"]).encode()
            ).hexdigest()[:8]
        return self._session_id

    def load_cognitive_journal(self):
        """Charge le journal cognitif depuis le disque"""
        try:
            if os.path.exists(self.journal_path):
                with open(self.journal_path, 'r', encoding='utf-8') as f:
                    self.cognitive_journal = json.load(f)
                print(f"📖 Journal cognitif chargé: {len(self.cognitive_journal)} entrées")
            else:
                self.cognitive_journal = []
        except Exception as e:
            print(f"⚠️ Erreur chargement journal: {e}")
            self.cognitive_journal = []

    def save_cognitive_journal(self):
        """Sauvegarde le journal cognitif sur disque"""
        try:
            with open(self.journal_path, 'w', encoding='utf-8') as f:
                json.dump(self.cognitive_journal, f, ensure_ascii=False, indent=2)
            print(f"💾 Journal cognitif sauvegardé: {len(self.cognitive_journal)} entrées")
        except Exception as e:
            print(f"❌ Erreur sauvegarde journal: {e}")

    def print_cognitive_stats(self):
        """Affiche les statistiques cognitives"""
        if not self.stats["start_time"]:
            return

        duration = datetime.now() - self.stats["start_time"]

        print("\n🧠 STATISTIQUES CYCLE COGNITIF CONTINU")
        print("=" * 50)
        print(f"⏱️ Durée session: {duration}")
        print(f"💡 Total idées: {self.stats['total_ideas']}")
        print(f"⭐ Idées haute valeur: {self.stats['high_value_ideas']}")
        print(f"🔔 Notifications envoyées: {self.stats['notifications_sent']}")
        print(f"🧠 Pensées profondes: {self.stats['deep_thoughts']}")
        print(f"🤔 Méta-pensées: {self.stats['meta_thoughts']}")

        if self.stats["total_ideas"] > 0:
            success_rate = (self.stats["high_value_ideas"] / self.stats["total_ideas"]) * 100
            print(f"📊 Taux de réussite: {success_rate:.1f}%")

        print("=" * 50)

    def get_recent_ideas(self, limit: int = 10) -> List[Dict]:
        """Récupère les idées récentes"""
        if not hasattr(self, 'cognitive_journal'):
            return []

        return self.cognitive_journal[-limit:] if self.cognitive_journal else []

    def get_best_ideas(self, limit: int = 5) -> List[Dict]:
        """Récupère les meilleures idées par score"""
        if not hasattr(self, 'cognitive_journal'):
            return []

        sorted_ideas = sorted(
            self.cognitive_journal,
            key=lambda x: x.get('evaluation', {}).get('score', 0),
            reverse=True
        )

        return sorted_ideas[:limit]

# 🚀 FONCTION D'INITIALISATION POUR JARVIS
def init_jarvis_cognitive_cycle(memory_system=None, whatsapp_notifier=None):
    """Initialise le cycle cognitif continu pour JARVIS"""

    print("🧠 Initialisation JARVIS Cycle Cognitif Continu...")

    cognitive_cycle = CognitiveContinuousCycle(
        memory_system=memory_system,
        whatsapp_notifier=whatsapp_notifier
    )

    print("✅ JARVIS Cycle Cognitif Continu prêt")
    print("   🚀 Utilisez cognitive_cycle.start_continuous_thinking() pour démarrer")

    return cognitive_cycle
