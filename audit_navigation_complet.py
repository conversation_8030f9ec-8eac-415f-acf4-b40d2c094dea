import time
#!/usr/bin/env python3
"""
AUDIT NAVIGATION COMPLET - JEAN-LUC PASSAVE
Vérifie toutes les navigations et corrige les liens
"""

import os
import re
import json
from datetime import datetime

def audit_all_navigation():
    """Audit complet de toutes les navigations"""
    
    print("🔍 AUDIT NAVIGATION COMPLET")
    print("=" * 50)
    print("👤 Jean-Luc Passave")
    print("📅 Vérification de toutes les navigations")
    print("")
    
    # Configuration des ports corrects
    CORRECT_PORTS = {
        'main_dashboard': 7867,  # VRAIE page principale
        'communication': 7866,
        'dashboard_onglets': 7899,
        'tableau_bord': 7902,
        'cerveau_tensorflow': 7912,
        'cerveau_3d': 7910,
        'energie': 7911
    }
    
    # Fichiers à vérifier
    files_to_check = [
        'jarvis_architecture_multi_fenetres.py',
        'dashboard_avec_onglets.py',
        'jarvis_tableau_de_bord_ultime.py',
        'cerveau_3d_tensorflow_jarvis.py',
        'centre_commande_unifie_jarvis.py'
    ]
    
    problems_found = []
    corrections_needed = []
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"\n📁 ANALYSE: {filename}")
            
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Rechercher les boutons de retour
            home_buttons = re.findall(r'gr\.Button\([^)]*["\'][^"\']*(?:Retour|Home|Accueil|🏠)[^"\']*["\'][^)]*\)', content, re.IGNORECASE)
            
            # Rechercher les redirections
            redirections = re.findall(r'webbrowser\.open\(["\']([^"\']+)["\']', content)
            
            print(f"   🔘 Boutons retour trouvés: {len(home_buttons)}")
            print(f"   🔗 Redirections trouvées: {len(redirections)}")
            
            # Vérifier les ports dans les redirections
            for redirect in redirections:
                if 'localhost:' in redirect:
                    port_match = re.search(r'localhost:(\d+)', redirect)
                    if port_match:
                        port = int(port_match.group(1))
                        if port not in CORRECT_PORTS.values():
                            problems_found.append({
                                'file': filename,
                                'type': 'PORT_INCORRECT',
                                'found': port,
                                'redirect': redirect,
                                'should_be': CORRECT_PORTS['main_dashboard']
                            })
                            print(f"   ❌ Port incorrect: {port} (devrait être {CORRECT_PORTS['main_dashboard']})")
                        else:
                            print(f"   ✅ Port correct: {port}")
            
            # Vérifier si le fichier a des boutons sans redirection
            if home_buttons and not redirections:
                problems_found.append({
                    'file': filename,
                    'type': 'BOUTON_SANS_REDIRECTION',
                    'buttons': len(home_buttons)
                })
                print(f"   ⚠️ Boutons sans redirection: {len(home_buttons)}")
            
            # Vérifier si le fichier n'a pas de bouton de retour
            if not home_buttons:
                problems_found.append({
                    'file': filename,
                    'type': 'AUCUN_BOUTON_RETOUR'
                })
                print(f"   ❌ Aucun bouton de retour trouvé")
        else:
            print(f"\n❌ FICHIER NON TROUVÉ: {filename}")
    
    # Résumé des problèmes
    print(f"\n📊 RÉSUMÉ AUDIT")
    print("=" * 50)
    print(f"📁 Fichiers vérifiés: {len([f for f in files_to_check if os.path.exists(f)])}")
    print(f"❌ Problèmes trouvés: {len(problems_found)}")
    
    if problems_found:
        print(f"\n🚨 PROBLÈMES DÉTECTÉS:")
        for i, problem in enumerate(problems_found, 1):
            print(f"   {i}. {problem['file']}: {problem['type']}")
            if problem['type'] == 'PORT_INCORRECT':
                print(f"      Port trouvé: {problem['found']} → Devrait être: {problem['should_be']}")
    else:
        print(f"\n✅ AUCUN PROBLÈME DÉTECTÉ")
        print(f"🎉 Toutes les navigations sont correctes!")
    
    # Générer les corrections
    if problems_found:
        print(f"\n🔧 CORRECTIONS RECOMMANDÉES:")
        for problem in problems_found:
            if problem['type'] == 'PORT_INCORRECT':
                print(f"   📝 {problem['file']}: Remplacer localhost:{problem['found']} par localhost:{problem['should_be']}")
            elif problem['type'] == 'BOUTON_SANS_REDIRECTION':
                print(f"   📝 {problem['file']}: Ajouter redirection pour {problem['buttons']} boutons")
            elif problem['type'] == 'AUCUN_BOUTON_RETOUR':
                print(f"   📝 {problem['file']}: Ajouter bouton de retour vers localhost:{CORRECT_PORTS['main_dashboard']}")
    
    return problems_found

def generate_correction_script(problems):
    """Génère un script de correction automatique"""
    
    if not problems:
        return "# Aucune correction nécessaire"
    
    script = f"""#!/usr/bin/env python3
# SCRIPT DE CORRECTION AUTOMATIQUE - JEAN-LUC PASSAVE
# Généré le {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

import re

def apply_corrections():
    corrections_applied = 0
    
"""
    
    for problem in problems:
        if problem['type'] == 'PORT_INCORRECT':
            script += f"""
    # Correction {problem['file']} - Port incorrect
    try:
        with open('{problem['file']}', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remplacer localhost:{problem['found']} par localhost:7867
        content = content.replace('localhost:{problem['found']}', 'localhost:7867')
        
        with open('{problem['file']}', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {problem['file']}: Port corrigé {problem['found']} → 7867")
        corrections_applied += 1
    except Exception as e:
        print(f"❌ Erreur correction {problem['file']}: {{e}}")
"""
    
    script += f"""
    return corrections_applied

if __name__ == "__main__":
    print("🔧 APPLICATION DES CORRECTIONS")
    print("=" * 40)
    corrections = apply_corrections()
    print(f"\\n✅ {{corrections}} corrections appliquées")
"""
    
    return script

def main():
    """Fonction principale"""
    problems = audit_all_navigation()
    
    # Sauvegarder le rapport
    report = {
        'timestamp': datetime.now().isoformat(),
        'problems_found': len(problems),
        'problems': problems
    }
    
    with open('audit_navigation_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Rapport sauvegardé: audit_navigation_report.json")
    
    # Générer script de correction
    if problems:
        correction_script = generate_correction_script(problems)
        with open('correction_navigation_auto.py', 'w', encoding='utf-8') as f:
            f.write(correction_script)
        print(f"🔧 Script de correction généré: correction_navigation_auto.py")
    
    return problems

if __name__ == "__main__":
    main()
