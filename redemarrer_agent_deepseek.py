#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
REDÉMARRAGE AGENT DEEPSEEK - JEAN-LUC PASSAVE
Script pour redémarrer automatiquement l'agent DeepSeek R1 8B
"""

import subprocess
import requests
import time
import os
import signal

def check_deepseek_status():
    """Vérifie le statut de DeepSeek R1 8B"""
    try:
        # Test health endpoint
        response = requests.get("http://localhost:8000/health", timeout=3)
        if response.status_code == 200:
            # Test chat endpoint
            test_payload = {
                "model": "jan-nano",
                "messages": [{"role": "user", "content": "Test"}],
                "max_tokens": 5
            }
            chat_response = requests.post("http://localhost:8000/v1/chat/completions", 
                                        json=test_payload, timeout=10)
            if chat_response.status_code == 200:
                return True, "✅ DeepSeek R1 8B opérationnel"
            else:
                return False, f"❌ Chat endpoint error: {chat_response.status_code}"
        else:
            return False, f"❌ Health check failed: {response.status_code}"
    except requests.exceptions.ConnectionError:
        return False, "❌ Connexion refusée - Serveur arrêté"
    except requests.exceptions.Timeout:
        return False, "⏱️ Timeout - Serveur lent"
    except Exception as e:
        return False, f"❌ Erreur: {str(e)}"

def kill_existing_deepseek():
    """Arrête tous les processus DeepSeek existants"""
    try:
        # Chercher les processus DeepSeek/VLLM
        result = subprocess.run(['pgrep', '-f', 'vllm'], capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    os.kill(int(pid), signal.SIGTERM)
                    print(f"🔄 Arrêt processus VLLM PID: {pid}")
                except:
                    pass
        
        # Chercher les processus sur le port 8000
        result = subprocess.run(['lsof', '-ti:8000'], capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    os.kill(int(pid), signal.SIGTERM)
                    print(f"🔄 Arrêt processus port 8000 PID: {pid}")
                except:
                    pass
        
        time.sleep(3)  # Attendre l'arrêt
        return True
    except Exception as e:
        print(f"❌ Erreur arrêt processus: {e}")
        return False

def start_deepseek():
    """Démarre DeepSeek R1 8B avec VLLM"""
    try:
        # Chemin vers le modèle
        model_path = "/Volumes/seagate/Louna_Electron_Latest/Jan-nano-gguf/jan-nano-4b-Q4_K_M.gguf"
        
        if not os.path.exists(model_path):
            print(f"❌ Modèle non trouvé: {model_path}")
            return False
        
        # Commande VLLM optimisée pour M4
        cmd = [
            "python", "-m", "vllm.entrypoints.openai.api_server",
            "--model", model_path,
            "--host", "localhost",
            "--port", "8000",
            "--served-model-name", "jan-nano",
            "--max-model-len", "4096",
            "--gpu-memory-utilization", "0.8",
            "--tensor-parallel-size", "1",
            "--dtype", "float16",
            "--trust-remote-code"
        ]
        
        print("🚀 Démarrage DeepSeek R1 8B avec VLLM...")
        print(f"📁 Modèle: {model_path}")
        
        # Démarrer en arrière-plan
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="/Volumes/seagate/Louna_Electron_Latest"
        )
        
        print(f"🔄 Processus démarré PID: {process.pid}")
        
        # Attendre que le serveur soit prêt
        max_wait = 60  # 1 minute max
        wait_time = 0
        
        while wait_time < max_wait:
            time.sleep(5)
            wait_time += 5
            
            status, msg = check_deepseek_status()
            if status:
                print(f"✅ DeepSeek R1 8B opérationnel après {wait_time}s")
                return True
            else:
                print(f"⏳ Attente démarrage... {wait_time}s ({msg})")
        
        print("❌ Timeout démarrage DeepSeek")
        return False
        
    except Exception as e:
        print(f"❌ Erreur démarrage: {e}")
        return False

def restart_deepseek():
    """Redémarre complètement DeepSeek R1 8B"""
    print("🔄 REDÉMARRAGE DEEPSEEK R1 8B - JEAN-LUC PASSAVE")
    print("=" * 60)
    
    # 1. Vérifier le statut actuel
    status, msg = check_deepseek_status()
    print(f"📊 Statut actuel: {msg}")
    
    # 2. Arrêter les processus existants
    print("🛑 Arrêt des processus existants...")
    kill_existing_deepseek()
    
    # 3. Redémarrer
    print("🚀 Redémarrage en cours...")
    if start_deepseek():
        print("✅ DeepSeek R1 8B redémarré avec succès !")
        return True
    else:
        print("❌ Échec du redémarrage")
        return False

def monitor_deepseek():
    """Surveillance continue de DeepSeek"""
    print("👁️ SURVEILLANCE DEEPSEEK R1 8B")
    print("=" * 40)
    
    while True:
        status, msg = check_deepseek_status()
        current_time = time.strftime("%H:%M:%S")
        
        if status:
            print(f"[{current_time}] ✅ {msg}")
        else:
            print(f"[{current_time}] ❌ {msg}")
            print("🔄 Tentative de redémarrage automatique...")
            if restart_deepseek():
                print("✅ Redémarrage réussi")
            else:
                print("❌ Redémarrage échoué")
        
        time.sleep(30)  # Vérifier toutes les 30 secondes

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "status":
            status, msg = check_deepseek_status()
            print(f"📊 Statut DeepSeek R1 8B: {msg}")
            sys.exit(0 if status else 1)
        elif sys.argv[1] == "restart":
            success = restart_deepseek()
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "monitor":
            monitor_deepseek()
        else:
            print("Usage: python redemarrer_agent_deepseek.py [status|restart|monitor]")
    else:
        # Par défaut: redémarrage
        restart_deepseek()
