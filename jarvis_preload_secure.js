
// 🔒 SCRIPT PRELOAD SÉCURISÉ JARVIS - JEAN-LUC PASSAVE
const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// Exposer uniquement les APIs nécessaires de manière sécurisée
contextBridge.exposeInMainWorld('jarvisAPI', {
    // Communication sécurisée avec le processus principal
    sendMessage: (channel, data) => {
        const validChannels = ['jarvis-message', 'jarvis-command', 'jarvis-status'];
        if (validChannels.includes(channel)) {
            ipcRenderer.invoke(channel, data);
        }
    },
    
    // Réception sécurisée des messages
    onMessage: (channel, callback) => {
        const validChannels = ['jarvis-response', 'jarvis-update', 'jarvis-notification'];
        if (validChannels.includes(channel)) {
            ipcRenderer.on(channel, callback);
        }
    },
    
    // Informations système sécurisées
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
    
    // Navigation sécurisée
    openWindow: (url) => {
        if (url.startsWith('http://localhost:') || url.startsWith('https://')) {
            ipcRenderer.invoke('open-secure-window', url);
        }
    }
});

// Bloquer l'accès direct à Node.js
delete window.require;
delete window.exports;
delete window.module;
