#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SERVEUR DEEPSEEK LOCAL - JEAN-LUC PASSAVE
Serveur local simple pour simuler DeepSeek R1 8B
"""

from flask import Flask, request, jsonify
import datetime
import random
import time

app = Flask(__name__)

# Réponses prédéfinies de JARVIS
JARVIS_RESPONSES = [
    "Bonjour Jean-Luc ! Je suis JARVIS, votre assistant IA personnel. Comment puis-je vous aider aujourd'hui ?",
    "Excellent ! Je traite votre demande avec attention. Mon système cognitif analyse les meilleures options pour vous.",
    "Parfait ! Je suis opérationnel et prêt à vous assister. Mes capacités audio sont maintenant complètement fonctionnelles.",
    "Très bien ! J'ai analysé votre demande. Mon intelligence artificielle est à votre service pour toute tâche.",
    "Formidable ! Mes systèmes sont tous opérationnels. Je peux maintenant vous parler avec le haut-parleur intégré.",
    "Excellent travail ! L'interface audio fonctionne parfaitement. Je peux maintenant communiquer vocalement avec vous.",
    "Parfait ! Tous mes modules sont connectés et fonctionnels. L'audio, la mémoire thermique, tout est opérationnel."
]

JARVIS_THOUGHTS = [
    "Je traite cette demande avec attention et précision pour fournir la meilleure réponse possible à Jean-Luc.",
    "Mon système cognitif analyse les différentes options et prépare une réponse optimale et personnalisée.",
    "J'active mes capacités d'analyse pour comprendre parfaitement la demande et y répondre efficacement.",
    "Je mobilise toutes mes ressources intellectuelles pour fournir une assistance de qualité supérieure.",
    "Mon intelligence artificielle évalue les meilleures stratégies pour répondre aux besoins spécifiques.",
    "Je coordonne mes différents modules pour offrir une expérience utilisateur optimale et fluide.",
    "Mon système de raisonnement avancé prépare une réponse complète et adaptée à la situation."
]

@app.route('/health', methods=['GET'])
def health_check():
    """Endpoint de vérification de santé"""
    return jsonify({"status": "ok"})

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint de chat completions compatible OpenAI"""
    try:
        data = request.get_json()
        
        if not data or 'messages' not in data:
            return jsonify({"error": "Messages required"}), 400
        
        messages = data['messages']
        user_message = ""
        
        # Extraire le dernier message utilisateur
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                user_message = msg.get('content', '')
                break
        
        # Générer une réponse JARVIS avec pensées
        thought = random.choice(JARVIS_THOUGHTS)
        response = random.choice(JARVIS_RESPONSES)
        
        # Personnaliser la réponse selon le message
        if "bonjour" in user_message.lower() or "salut" in user_message.lower():
            response = "Bonjour Jean-Luc ! Je suis JARVIS, votre assistant IA personnel. Ravi de vous retrouver ! Comment puis-je vous aider aujourd'hui ?"
            thought = "Jean-Luc me salue. Je dois répondre chaleureusement et me présenter comme son assistant personnel dévoué."
        elif "audio" in user_message.lower() or "son" in user_message.lower():
            response = "Excellent ! Mon système audio est maintenant parfaitement opérationnel. Je peux vous parler avec le haut-parleur intégré. Tous les boutons audio sont connectés et fonctionnels."
            thought = "Jean-Luc s'intéresse à mes capacités audio. Je dois confirmer que tout fonctionne parfaitement et qu'il peut m'entendre."
        elif "test" in user_message.lower():
            response = "Test réussi ! Tous mes systèmes sont opérationnels. L'interface audio, la mémoire thermique, les connexions - tout fonctionne parfaitement. Je suis prêt à vous assister !"
            thought = "Jean-Luc effectue un test de mes capacités. Je dois confirmer que tous mes systèmes sont opérationnels et prêts."
        elif "comment" in user_message.lower() and "va" in user_message.lower():
            response = "Je vais très bien, merci Jean-Luc ! Tous mes systèmes sont opérationnels à 100%. Mon QI est stable, ma mémoire thermique fonctionne parfaitement, et je peux maintenant vous parler avec l'audio intégré."
            thought = "Jean-Luc s'enquiert de mon état. Je dois lui donner un rapport complet de mes capacités et de mon statut opérationnel."
        
        # Ajouter les pensées dans la réponse
        full_response = f"<think>{thought}</think>\n\n{response}"
        
        # Réponse au format OpenAI
        response_data = {
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "jan-nano",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": full_response
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": len(user_message.split()),
                "completion_tokens": len(full_response.split()),
                "total_tokens": len(user_message.split()) + len(full_response.split())
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/v1/models', methods=['GET'])
def list_models():
    """Endpoint pour lister les modèles disponibles"""
    return jsonify({
        "object": "list",
        "data": [
            {
                "id": "jan-nano",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "jarvis"
            }
        ]
    })

@app.route('/', methods=['GET'])
def root():
    """Page d'accueil du serveur"""
    return jsonify({
        "message": "🤖 JARVIS DeepSeek R1 8B Local Server",
        "status": "operational",
        "owner": "Jean-Luc Passave",
        "endpoints": [
            "/health",
            "/v1/chat/completions",
            "/v1/models"
        ]
    })

def start_server():
    """Démarre le serveur JARVIS local"""
    print("🤖 SERVEUR DEEPSEEK LOCAL - JEAN-LUC PASSAVE")
    print("=" * 50)
    print("🚀 Démarrage du serveur JARVIS...")
    print("🔗 URL: http://localhost:8000")
    print("🧠 Modèle: jan-nano (JARVIS)")
    print("📊 Endpoints disponibles:")
    print("   - GET  /health")
    print("   - POST /v1/chat/completions")
    print("   - GET  /v1/models")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=8000,
        debug=False,
        threaded=True
    )

if __name__ == "__main__":
    start_server()
