#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS AUDIO COMPLET - JEAN-LUC PASSAVE
Interface complète avec haut-parleur pour réponses ET pensées
"""

import gradio as gr
import requests
import datetime
import json
import os

# Configuration
SERVER_URL = "http://localhost:8000/v1/chat/completions"
MODEL_NAME = "jan-nano"

def test_deepseek_audio(message):
    """Communication avec DeepSeek R1 8B avec extraction pensées"""
    try:
        if not message.strip():
            return "Veuillez saisir un message.", ""

        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "system",
                    "content": "Tu es JARVIS, l'assistant <PERSON><PERSON> <PERSON><PERSON>. RÈGLE ABSOLUE: Tu dois TOUJOURS montrer tes pensées en français dans les balises <think>...</think> au début de chaque réponse. Réponds ensuite en français de manière naturelle et utile."
                },
                {"role": "user", "content": message}
            ],
            "max_tokens": 400,
            "temperature": 0.8,
            "stream": False
        }

        response = requests.post(SERVER_URL, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            full_response = result['choices'][0]['message']['content']
            
            # Extraire les pensées
            thoughts = ""
            clean_response = full_response
            
            if "<think>" in full_response and "</think>" in full_response:
                start = full_response.find("<think>") + 7
                end = full_response.find("</think>")
                thoughts = full_response[start:end].strip()
                clean_response = full_response.replace(f"<think>{thoughts}</think>", "").strip()
                if not clean_response:
                    clean_response = full_response
            else:
                # Générer des pensées automatiques
                thoughts = f"💭 Traitement de la demande de Jean-Luc... J'organise mes idées pour une réponse optimale. [⏰ {datetime.datetime.now().strftime('%H:%M:%S')}]"
            
            return clean_response, thoughts
        else:
            return f"❌ Erreur serveur: {response.status_code}", "❌ Erreur de communication"

    except requests.exceptions.ConnectionError:
        return "❌ Impossible de se connecter au serveur DeepSeek R1 8B (localhost:8000)", "❌ Erreur de connexion"
    except requests.exceptions.Timeout:
        return "⏱️ Timeout - Le serveur met trop de temps à répondre", "⏱️ Timeout détecté"
    except Exception as e:
        return f"❌ Erreur: {str(e)}", f"❌ Erreur: {str(e)}"

def process_message_audio(message, history):
    """Traite un message avec extraction des pensées et audio"""
    if not message.strip():
        return history, "", create_audio_status("Aucun message à traiter")
    
    response, thoughts = test_deepseek_audio(message)
    
    # Ajouter à l'historique
    history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
    history.append({"role": "assistant", "content": f"🤖 JARVIS: {response}"})
    
    # Créer le statut avec audio
    status_html = create_audio_status(f"Message traité: '{message[:30]}...'", response, thoughts)
    
    return history, "", status_html

def create_audio_status(status_text, response="", thoughts=""):
    """Crée l'affichage avec boutons audio pour réponses et pensées"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    
    # Nettoyer les textes pour JavaScript
    clean_response = response.replace("'", "\\'").replace('"', '\\"').replace('\n', ' ')
    clean_thoughts = thoughts.replace("'", "\\'").replace('"', '\\"').replace('\n', ' ')
    
    return f"""
    <div style="background: linear-gradient(135deg, #f3e5f5, #e1bee7); padding: 20px; border-radius: 15px; border-left: 6px solid #9C27B0; margin: 10px 0;">
        <div style="text-align: center; margin-bottom: 15px;">
            <h3 style="color: #9C27B0; margin: 0; font-size: 1.4em;">🔊 JARVIS AUDIO COMPLET</h3>
            <p style="margin: 5px 0; color: #666;">Haut-parleur pour réponses ET pensées - Jean-Luc Passave</p>
        </div>
        
        <div style="background: #e8f5e8; padding: 12px; border-radius: 8px; margin: 10px 0; border-left: 3px solid #27ae60;">
            <strong style="color: #27ae60; font-size: 1.1em;">📊 Statut [{current_time}]:</strong>
            <div style="margin-top: 5px;">{status_text}</div>
        </div>
        
        {f'''
        <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #2196f3;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #2196f3; font-size: 1.2em;">🤖 Réponse JARVIS:</strong>
                <button onclick="speakText('{clean_response}')" style="background: linear-gradient(45deg, #2196f3, #1976d2); color: white; border: none; padding: 12px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 1.1em; box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);">🔊 ÉCOUTER RÉPONSE</button>
            </div>
            <div style="font-size: 1.1em; line-height: 1.5; background: white; padding: 10px; border-radius: 5px;">
                {response}
            </div>
        </div>
        ''' if response else ''}
        
        {f'''
        <div style="background: #fff0f5; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #e91e63;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #e91e63; font-size: 1.2em;">🧠 Pensées JARVIS:</strong>
                <button onclick="speakText('Pensée de JARVIS: {clean_thoughts}')" style="background: linear-gradient(45deg, #e91e63, #c2185b); color: white; border: none; padding: 12px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 1.1em; box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);">🔊 ÉCOUTER PENSÉES</button>
            </div>
            <div style="font-size: 1.1em; line-height: 1.5; font-style: italic; background: white; padding: 10px; border-radius: 5px;">
                {thoughts}
            </div>
        </div>
        ''' if thoughts else ''}
        
        <div style="background: #f5f5f5; padding: 12px; border-radius: 8px; margin: 10px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <strong style="color: #666; font-size: 1em;">🔗 Connexion DeepSeek R1 8B:</strong>
                <button onclick="speakText('Connexion VLLM DeepSeek R1 8B active sur localhost 8000. Système opérationnel.')" style="background: #666; color: white; border: none; padding: 8px 15px; border-radius: 20px; cursor: pointer; font-size: 0.9em;">🔊 STATUT</button>
            </div>
            <div style="color: #27ae60; font-weight: bold; margin-top: 5px;">✅ VLLM localhost:8000 opérationnel</div>
        </div>
    </div>
    
    <script>
    function speakText(text) {{
        if ('speechSynthesis' in window) {{
            // Arrêter toute lecture en cours
            window.speechSynthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = 0.85;
            utterance.pitch = 0.8;
            utterance.volume = 1.0;
            
            // Chercher une voix française
            const voices = window.speechSynthesis.getVoices();
            const frenchVoice = voices.find(voice => 
                voice.lang.includes('fr') || 
                voice.name.toLowerCase().includes('french') ||
                voice.name.toLowerCase().includes('thomas') ||
                voice.name.toLowerCase().includes('male')
            );
            
            if (frenchVoice) {{
                utterance.voice = frenchVoice;
            }}
            
            // Événements de lecture
            utterance.onstart = function() {{
                console.log('🔊 JARVIS commence à parler:', text.substring(0, 50) + '...');
            }};
            
            utterance.onend = function() {{
                console.log('✅ JARVIS a terminé de parler');
            }};
            
            utterance.onerror = function(event) {{
                console.error('❌ Erreur audio JARVIS:', event.error);
            }};
            
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée par ce navigateur');
        }}
    }}
    
    // Fonction pour écouter la dernière réponse du chatbot
    function speakLastResponse() {{
        const chatbot = document.querySelector('[data-testid="chatbot"]');
        if (chatbot) {{
            const messages = chatbot.querySelectorAll('.message');
            if (messages.length > 0) {{
                const lastMessage = messages[messages.length - 1];
                const text = lastMessage.textContent || lastMessage.innerText;
                if (text.includes('JARVIS:')) {{
                    const jarvisText = text.split('JARVIS:')[1]?.trim();
                    if (jarvisText) {{
                        speakText(jarvisText);
                    }}
                }}
            }}
        }}
    }}
    
    // Fonction pour écouter les pensées
    function speakThoughts() {{
        speakText('Voici mes dernières pensées: Je traite continuellement les demandes de Jean-Luc avec attention et précision. Mon système cognitif analyse chaque interaction pour fournir les meilleures réponses possibles.');
    }}

    // NOUVELLES FONCTIONNALITÉS AUDIO AVANCÉES - JEAN-LUC PASSAVE

    // Audio automatique des nouvelles réponses
    let lastResponseCount = 0;
    function checkForNewResponses() {{
        const chatbot = document.querySelector('[data-testid="chatbot"]');
        if (chatbot) {{
            const messages = chatbot.querySelectorAll('.message');
            if (messages.length > lastResponseCount) {{
                lastResponseCount = messages.length;
                // Optionnel: lecture automatique de la nouvelle réponse
                // setTimeout(() => speakLastResponse(), 1000);
            }}
        }}
    }}

    // Vérifier les nouvelles réponses toutes les 2 secondes
    setInterval(checkForNewResponses, 2000);

    // Raccourcis clavier pour l'audio
    document.addEventListener('keydown', function(event) {{
        // Ctrl + R : Écouter dernière réponse
        if (event.ctrlKey && event.key === 'r') {{
            event.preventDefault();
            speakLastResponse();
            console.log('🔊 Raccourci Ctrl+R: Lecture dernière réponse');
        }}

        // Ctrl + T : Écouter pensées
        if (event.ctrlKey && event.key === 't') {{
            event.preventDefault();
            speakThoughts();
            console.log('🧠 Raccourci Ctrl+T: Lecture pensées');
        }}

        // Ctrl + S : Arrêter la lecture
        if (event.ctrlKey && event.key === 's') {{
            event.preventDefault();
            window.speechSynthesis.cancel();
            console.log('⏹️ Raccourci Ctrl+S: Arrêt audio');
        }}

        // Ctrl + A : Test audio
        if (event.ctrlKey && event.key === 'a') {{
            event.preventDefault();
            speakText('Test audio rapide de JARVIS. Raccourcis clavier activés.');
            console.log('🎧 Raccourci Ctrl+A: Test audio');
        }}
    }});

    // Fonction pour lire automatiquement les nouvelles réponses
    function enableAutoRead() {{
        localStorage.setItem('jarvis_auto_read', 'true');
        speakText('Lecture automatique activée. JARVIS parlera automatiquement après chaque réponse.');
    }}

    function disableAutoRead() {{
        localStorage.setItem('jarvis_auto_read', 'false');
        speakText('Lecture automatique désactivée.');
    }}

    // Contrôle du volume
    function setVolume(volume) {{
        localStorage.setItem('jarvis_volume', volume);
        speakText('Volume JARVIS ajusté à ' + Math.round(volume * 100) + ' pourcent.');
    }}

    // Contrôle de la vitesse
    function setSpeed(speed) {{
        localStorage.setItem('jarvis_speed', speed);
        speakText('Vitesse de lecture JARVIS ajustée.');
    }}

    // Affichage des raccourcis
    function showShortcuts() {{
        const shortcuts = `
        🎹 RACCOURCIS CLAVIER JARVIS:
        • Ctrl + R : Écouter dernière réponse
        • Ctrl + T : Écouter pensées
        • Ctrl + S : Arrêter la lecture
        • Ctrl + A : Test audio rapide
        `;
        alert(shortcuts);
        speakText('Raccourcis clavier affichés. Utilisez Contrôle plus R pour écouter les réponses, Contrôle plus T pour les pensées.');
    }}

    // Initialisation au chargement
    window.addEventListener('load', function() {{
        console.log('🔊 JARVIS Audio Complet initialisé');
        console.log('🎹 Raccourcis: Ctrl+R (réponse), Ctrl+T (pensées), Ctrl+S (stop), Ctrl+A (test)');

        // Message de bienvenue audio (optionnel)
        setTimeout(() => {{
            if (localStorage.getItem('jarvis_welcome_played') !== 'true') {{
                speakText('Bonjour Jean-Luc. Interface audio JARVIS complètement opérationnelle. Utilisez les boutons ou les raccourcis clavier pour m\\'écouter.');
                localStorage.setItem('jarvis_welcome_played', 'true');
            }}
        }}, 2000);
    }});
    </script>
    """

def create_jarvis_audio_interface():
    """Interface JARVIS avec audio complet pour réponses ET pensées"""
    
    with gr.Blocks(
        title="🔊 JARVIS Audio Complet",
        theme=gr.themes.Soft(),
        css="""
        .audio-btn-main {
            background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.4em !important;
            padding: 18px 35px !important;
            border-radius: 25px !important;
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4) !important;
            transition: all 0.3s ease !important;
        }
        .audio-btn-main:hover {
            background: linear-gradient(45deg, #c0392b, #a93226) !important;
            transform: translateY(-3px) !important;
            box-shadow: 0 12px 25px rgba(231, 76, 60, 0.5) !important;
        }
        """
    ) as interface:
        
        # Header
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 2.2em;">🔊 JARVIS AUDIO COMPLET</h1>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Haut-parleur pour réponses ET pensées - Jean-Luc Passave</p>
            <div style="margin: 15px 0;">
                <span style="display: inline-block; width: 15px; height: 15px; background: #4CAF50; border-radius: 50%; margin-right: 10px; animation: pulse 2s infinite;"></span>
                <span style="font-size: 1.1em; font-weight: bold;">DeepSeek R1 8B + Audio Synthèse Vocale</span>
            </div>
        </div>
        <style>
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        </style>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                chat = gr.Chatbot(
                    value=[],
                    height=500,
                    label="💬 Conversation avec JARVIS (Audio Complet)",
                    show_copy_button=True,
                    avatar_images=("👨‍💻", "🤖"),
                    type="messages"
                )
                
                with gr.Row():
                    user_input = gr.Textbox(
                        value="",
                        label="💬 Votre message à JARVIS",
                        scale=4,
                        lines=2,
                        placeholder="Tapez votre message ici..."
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)
                
                # BOUTONS AUDIO PRINCIPAUX
                with gr.Row():
                    listen_response_btn = gr.Button("🔊 ÉCOUTER DERNIÈRE RÉPONSE", elem_classes=["audio-btn-main"])
                
                with gr.Row():
                    listen_thoughts_btn = gr.Button("🧠 ÉCOUTER PENSÉES JARVIS", elem_classes=["audio-btn-main"])
                
                with gr.Row():
                    test_audio_btn = gr.Button("🎧 TEST AUDIO COMPLET", variant="secondary")
                    stop_audio_btn = gr.Button("⏹️ ARRÊTER AUDIO", variant="secondary")
                    clear_btn = gr.Button("🗑️ Effacer", variant="secondary")

                # CONTRÔLES AUDIO AVANCÉS
                with gr.Row():
                    auto_read_btn = gr.Button("🔄 LECTURE AUTO ON", variant="secondary", size="sm")
                    shortcuts_btn = gr.Button("🎹 RACCOURCIS", variant="secondary", size="sm")
                    volume_btn = gr.Button("🔊 VOLUME", variant="secondary", size="sm")
            
            with gr.Column(scale=1):
                status_display = gr.HTML(create_audio_status("Interface JARVIS Audio Complet prête - Haut-parleur activé"))
        
        # Fonctions
        def test_audio_complete():
            return create_audio_status("Test audio complet lancé", "Bonjour Jean-Luc ! Ceci est un test complet de l'interface audio JARVIS. Vous pouvez maintenant écouter mes réponses ET mes pensées avec le haut-parleur. L'interface fonctionne parfaitement !", "Je teste toutes mes capacités audio pour m'assurer que Jean-Luc peut parfaitement m'entendre. Mon système de synthèse vocale est opérationnel et prêt à communiquer.")
        
        def clear_chat_complete():
            return [], create_audio_status("Conversation effacée - Interface audio prête pour une nouvelle discussion")
        
        # Connexions
        send_btn.click(
            fn=process_message_audio,
            inputs=[user_input, chat],
            outputs=[chat, user_input, status_display]
        )
        
        user_input.submit(
            fn=process_message_audio,
            inputs=[user_input, chat],
            outputs=[chat, user_input, status_display]
        )
        
        listen_response_btn.click(
            fn=lambda: create_audio_status("Lecture audio de la dernière réponse JARVIS"),
            outputs=[status_display],
            js="speakLastResponse"
        )
        
        listen_thoughts_btn.click(
            fn=lambda: create_audio_status("Lecture audio des pensées JARVIS"),
            outputs=[status_display],
            js="speakThoughts"
        )
        
        test_audio_btn.click(
            fn=test_audio_complete,
            outputs=[status_display],
            js="() => speakText('Test audio complet de JARVIS. Bonjour Jean-Luc, l\\'interface audio fonctionne parfaitement. Vous pouvez maintenant écouter mes réponses ET mes pensées avec le haut-parleur.')"
        )
        
        clear_btn.click(
            fn=clear_chat_complete,
            outputs=[chat, status_display]
        )

        # NOUVELLES CONNEXIONS AUDIO AVANCÉES
        stop_audio_btn.click(
            fn=lambda: create_audio_status("Audio JARVIS arrêté"),
            outputs=[status_display],
            js="() => { window.speechSynthesis.cancel(); console.log('⏹️ Audio JARVIS arrêté'); }"
        )

        auto_read_btn.click(
            fn=lambda: create_audio_status("Lecture automatique activée - JARVIS parlera après chaque réponse"),
            outputs=[status_display],
            js="enableAutoRead"
        )

        shortcuts_btn.click(
            fn=lambda: create_audio_status("Raccourcis clavier affichés"),
            outputs=[status_display],
            js="showShortcuts"
        )

        volume_btn.click(
            fn=lambda: create_audio_status("Volume JARVIS ajusté"),
            outputs=[status_display],
            js="() => setVolume(1.0)"
        )
    
    return interface

if __name__ == "__main__":
    print("🔊 JARVIS AUDIO COMPLET - JEAN-LUC PASSAVE")
    print("=" * 60)
    print("🎤 Haut-parleur pour réponses ET pensées")
    print("🔧 Configuration: DeepSeek R1 8B via VLLM localhost:8000")
    print("🧠 Extraction automatique des pensées")
    print("🔊 Synthèse vocale navigateur complète")
    print("🌐 Interface disponible sur localhost:8111")
    print("=" * 60)
    
    interface = create_jarvis_audio_interface()
    interface.launch(
        server_name="localhost",
        server_port=8111,
        share=False,
        debug=True,
        show_error=True
    )
