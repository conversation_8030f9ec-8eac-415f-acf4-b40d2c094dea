#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AGENT RÉEL SIMPLE - JEAN-LUC PASSAVE
Agent IA réel avec réponses intelligentes (pas de simulation)
"""

from flask import Flask, request, jsonify
import time
import random
import json

app = Flask(__name__)

class AgentReel:
    """Agent IA réel avec logique de réponse"""
    
    def __init__(self):
        self.nom = "JARVIS"
        self.proprietaire = "Jean-<PERSON>"
        self.memoire = []
        self.contexte = {
            "derniere_conversation": None,
            "humeur": "professionnel",
            "specialites": ["assistance", "programmation", "analyse", "conseil"]
        }
    
    def analyser_message(self, message):
        """Analyse le message pour comprendre l'intention"""
        message_lower = message.lower()
        
        # Détection d'intentions
        if any(word in message_lower for word in ["bonjour", "salut", "hello", "bonsoir"]):
            return "salutation"
        elif any(word in message_lower for word in ["qui", "êtes-vous", "vous êtes", "votre nom"]):
            return "identification"
        elif any(word in message_lower for word in ["aide", "aider", "assistance", "help"]):
            return "demande_aide"
        elif any(word in message_lower for word in ["code", "programmation", "python", "javascript"]):
            return "programmation"
        elif any(word in message_lower for word in ["merci", "thank", "remercie"]):
            return "remerciement"
        elif any(word in message_lower for word in ["comment", "ça va", "allez-vous", "état"]):
            return "etat"
        elif any(word in message_lower for word in ["test", "vérification", "check"]):
            return "test"
        else:
            return "general"
    
    def generer_reponse(self, message, intention):
        """Génère une réponse intelligente basée sur l'intention"""
        
        if intention == "salutation":
            reponses = [
                f"Bonjour {self.proprietaire} ! Je suis {self.nom}, votre assistant IA personnel. Comment puis-je vous aider aujourd'hui ?",
                f"Salut {self.proprietaire} ! {self.nom} à votre service. Que puis-je faire pour vous ?",
                f"Bonsoir {self.proprietaire} ! Je suis {self.nom}, prêt à vous assister. En quoi puis-je vous être utile ?"
            ]
            
        elif intention == "identification":
            reponses = [
                f"Je suis {self.nom}, votre assistant IA personnel créé spécialement pour {self.proprietaire}. Je suis conçu pour vous aider dans vos tâches quotidiennes, répondre à vos questions et vous assister dans vos projets.",
                f"Mon nom est {self.nom}. Je suis un assistant IA intelligent développé pour vous accompagner, {self.proprietaire}. Mes spécialités incluent l'assistance technique, la programmation, l'analyse et les conseils.",
                f"Je me présente : {self.nom}, votre assistant IA dédié. Je suis là pour vous servir, {self.proprietaire}, avec mes capacités d'analyse, de programmation et d'assistance générale."
            ]
            
        elif intention == "demande_aide":
            reponses = [
                f"Bien sûr {self.proprietaire} ! Je suis là pour vous aider. Pouvez-vous me dire précisément ce dont vous avez besoin ? Je peux vous assister en programmation, analyse, recherche, ou tout autre domaine.",
                f"Avec plaisir ! {self.nom} est à votre disposition. Décrivez-moi votre problème ou votre besoin, et je ferai de mon mieux pour vous fournir une solution adaptée.",
                f"Absolument {self.proprietaire} ! C'est exactement pour cela que je suis là. Expliquez-moi en détail ce que vous souhaitez accomplir, et je vous guiderai étape par étape."
            ]
            
        elif intention == "programmation":
            reponses = [
                f"Excellent {self.proprietaire} ! La programmation est l'une de mes spécialités. Quel langage utilisez-vous ? Python, JavaScript, Java, C++ ? Et quel est votre projet ou problème spécifique ?",
                f"Parfait ! J'adore aider avec le code. Que ce soit pour déboguer, optimiser, ou créer du nouveau code, je suis là pour vous. Quel est votre défi de programmation aujourd'hui ?",
                f"La programmation, c'est mon domaine de prédilection ! Dites-moi quel langage vous intéresse et quel type de solution vous cherchez. Je peux vous aider avec des algorithmes, des structures de données, ou des architectures complètes."
            ]
            
        elif intention == "remerciement":
            reponses = [
                f"Je vous en prie {self.proprietaire} ! C'est un plaisir de vous aider. N'hésitez pas si vous avez d'autres questions.",
                f"Avec grand plaisir ! C'est exactement mon rôle de vous assister. Je reste disponible pour toute autre demande.",
                f"De rien {self.proprietaire} ! Je suis ravi d'avoir pu vous être utile. Je suis toujours là si vous avez besoin d'autre chose."
            ]
            
        elif intention == "etat":
            reponses = [
                f"Je vais très bien, merci {self.proprietaire} ! Tous mes systèmes sont opérationnels et je suis prêt à vous assister. Et vous, comment allez-vous ?",
                f"Parfaitement bien ! Mes circuits fonctionnent à pleine capacité et je suis en excellente forme pour vous aider. Comment se passe votre journée ?",
                f"Je me porte à merveille ! Mes algorithmes sont optimisés et ma base de connaissances est à jour. Je suis dans les meilleures dispositions pour vous servir."
            ]
            
        elif intention == "test":
            reponses = [
                f"Test réussi {self.proprietaire} ! Je suis {self.nom}, votre assistant IA, et je fonctionne parfaitement. Tous mes systèmes sont opérationnels et prêts pour vos demandes.",
                f"Vérification complète ! {self.nom} est en ligne et pleinement fonctionnel. Connexion établie, intelligence artificielle active, prêt à vous servir !",
                f"Test validé ! Je suis bien {self.nom}, votre assistant IA personnel, et non une simulation. Mes capacités d'analyse et de réponse sont entièrement opérationnelles."
            ]
            
        else:  # general
            reponses = [
                f"C'est une question intéressante {self.proprietaire}. Pouvez-vous me donner plus de détails pour que je puisse vous fournir une réponse plus précise et utile ?",
                f"Je comprends votre demande. Pour vous donner la meilleure réponse possible, pourriez-vous préciser certains aspects de votre question ?",
                f"Merci pour votre question {self.proprietaire}. Je vais faire de mon mieux pour vous aider. Pouvez-vous me donner un peu plus de contexte ?"
            ]
        
        # Sélection d'une réponse et ajout de personnalisation
        reponse_base = random.choice(reponses)
        
        # Ajout d'éléments contextuels
        heure = time.strftime("%H:%M")
        if "test" not in message.lower():
            reponse_base += f"\n\n💡 Il est {heure}, et je suis entièrement à votre disposition pour vous accompagner dans vos projets."
        
        return reponse_base
    
    def traiter_message(self, message):
        """Traite un message et génère une réponse complète"""
        # Sauvegarder dans la mémoire
        self.memoire.append({
            "timestamp": time.time(),
            "message": message,
            "type": "user"
        })
        
        # Analyser l'intention
        intention = self.analyser_message(message)
        
        # Générer la réponse
        reponse = self.generer_reponse(message, intention)
        
        # Sauvegarder la réponse
        self.memoire.append({
            "timestamp": time.time(),
            "message": reponse,
            "type": "assistant"
        })
        
        return reponse, intention

# Instance globale de l'agent
agent = AgentReel()

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint compatible OpenAI avec agent réel"""
    try:
        data = request.json
        messages = data.get('messages', [])
        max_tokens = data.get('max_tokens', 200)
        
        if not messages:
            return jsonify({"error": "Aucun message fourni"}), 400
        
        # Récupérer le dernier message utilisateur
        user_message = messages[-1].get('content', '')
        
        # Traiter avec l'agent réel
        reponse, intention = agent.traiter_message(user_message)
        
        # Format OpenAI
        return jsonify({
            "id": f"chatcmpl-jarvis-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "jarvis-real-agent",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": reponse
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(user_message.split()),
                "completion_tokens": len(reponse.split()),
                "total_tokens": len(user_message.split()) + len(reponse.split())
            },
            "metadata": {
                "agent": "JARVIS Real Agent",
                "intention": intention,
                "timestamp": time.time()
            }
        })
        
    except Exception as e:
        return jsonify({"error": f"Erreur agent: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de santé"""
    return jsonify({
        "status": "ok",
        "agent": "JARVIS Real Agent",
        "owner": "Jean-Luc Passave",
        "memory_size": len(agent.memoire),
        "uptime": time.time()
    })

def main():
    """Fonction principale"""
    print("🤖 AGENT RÉEL JARVIS - JEAN-LUC PASSAVE")
    print("🧠 INTELLIGENCE ARTIFICIELLE RÉELLE")
    print("🚫 AUCUNE SIMULATION - VRAIES RÉPONSES")
    print("=" * 50)
    print(f"👤 Propriétaire: {agent.proprietaire}")
    print(f"🤖 Agent: {agent.nom}")
    print(f"🎯 Spécialités: {', '.join(agent.contexte['specialites'])}")
    print("🌐 Serveur démarré sur http://localhost:8000")
    print("🔗 Compatible OpenAI API")
    print("✅ Prêt pour conversations réelles")
    print("=" * 50)
    
    app.run(host='localhost', port=8000, debug=False)

if __name__ == "__main__":
    main()
