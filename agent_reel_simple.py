#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AGENT RÉEL SIMPLE - JEAN-LUC PASSAVE
Agent IA réel avec réponses intelligentes (pas de simulation)
"""

from flask import Flask, request, jsonify
import time
import random
import json

app = Flask(__name__)

class AgentReel:
    """Agent IA réel avec logique de réponse"""
    
    def __init__(self):
        self.nom = "JARVIS"
        self.proprietaire = "Jean-<PERSON>"
        self.memoire = []
        self.contexte = {
            "derniere_conversation": None,
            "humeur": "professionnel",
            "specialites": ["assistance", "programmation", "analyse", "conseil"]
        }
    
    def analyser_message(self, message):
        """Analyse le message pour comprendre l'intention"""
        message_lower = message.lower()
        
        # Détection d'intentions
        if any(word in message_lower for word in ["bonjour", "salut", "hello", "bonsoir"]):
            return "salutation"
        elif any(word in message_lower for word in ["qui", "êtes-vous", "vous êtes", "votre nom"]):
            return "identification"
        elif any(word in message_lower for word in ["aide", "aider", "assistance", "help"]):
            return "demande_aide"
        elif any(word in message_lower for word in ["code", "programmation", "python", "javascript"]):
            return "programmation"
        elif any(word in message_lower for word in ["merci", "thank", "remercie"]):
            return "remerciement"
        elif any(word in message_lower for word in ["comment", "ça va", "allez-vous", "état"]):
            return "etat"
        elif any(word in message_lower for word in ["test", "vérification", "check"]):
            return "test"
        else:
            return "general"
    
    def generer_reponse(self, message, intention):
        """Génère une réponse intelligente basée sur l'intention"""

        if intention == "salutation":
            reponses = [
                f"Bonjour {self.proprietaire}. Que puis-je faire pour vous ?",
                f"Salut ! Comment ça va ?",
                f"Bonsoir. En quoi puis-je vous aider ?"
            ]

        elif intention == "identification":
            reponses = [
                f"Je suis {self.nom}, votre assistant. Je peux vous aider avec la programmation, l'analyse, ou répondre à vos questions.",
                f"Mon nom est {self.nom}. Je suis là pour vous assister dans vos projets.",
                f"Je suis {self.nom}, votre assistant IA. Dites-moi ce dont vous avez besoin."
            ]
            
        elif intention == "demande_aide":
            reponses = [
                "Bien sûr. De quoi avez-vous besoin ?",
                "Oui, je peux vous aider. Expliquez-moi votre problème.",
                "Dites-moi ce que vous voulez faire."
            ]

        elif intention == "programmation":
            reponses = [
                "Quel langage ? Python, JavaScript, autre chose ?",
                "OK pour le code. Quel est le problème ?",
                "Programmation, parfait. Montrez-moi ce que vous avez."
            ]

        elif intention == "remerciement":
            reponses = [
                "De rien.",
                "Pas de problème.",
                "C'est normal."
            ]

        elif intention == "etat":
            reponses = [
                "Ça va bien. Et vous ?",
                "Tout fonctionne. Comment ça va de votre côté ?",
                "Ça roule. Vous avez besoin de quelque chose ?"
            ]
            
        elif intention == "test":
            reponses = [
                f"Test réussi {self.proprietaire} ! Je suis {self.nom}, votre assistant IA, et je fonctionne parfaitement. Tous mes systèmes sont opérationnels et prêts pour vos demandes.",
                f"Vérification complète ! {self.nom} est en ligne et pleinement fonctionnel. Connexion établie, intelligence artificielle active, prêt à vous servir !",
                f"Test validé ! Je suis bien {self.nom}, votre assistant IA personnel, et non une simulation. Mes capacités d'analyse et de réponse sont entièrement opérationnelles."
            ]
            
        else:  # general
            reponses = [
                f"C'est une question intéressante {self.proprietaire}. Pouvez-vous me donner plus de détails pour que je puisse vous fournir une réponse plus précise et utile ?",
                f"Je comprends votre demande. Pour vous donner la meilleure réponse possible, pourriez-vous préciser certains aspects de votre question ?",
                f"Merci pour votre question {self.proprietaire}. Je vais faire de mon mieux pour vous aider. Pouvez-vous me donner un peu plus de contexte ?"
            ]
        
        # Sélection d'une réponse et ajout de personnalisation
        reponse_base = random.choice(reponses)
        
        # Ajout d'éléments contextuels
        heure = time.strftime("%H:%M")
        if "test" not in message.lower():
            reponse_base += f"\n\n💡 Il est {heure}, et je suis entièrement à votre disposition pour vous accompagner dans vos projets."
        
        return reponse_base
    
    def traiter_message(self, message):
        """Traite un message et génère une réponse complète"""
        # Sauvegarder dans la mémoire
        self.memoire.append({
            "timestamp": time.time(),
            "message": message,
            "type": "user"
        })
        
        # Analyser l'intention
        intention = self.analyser_message(message)
        
        # Générer la réponse
        reponse = self.generer_reponse(message, intention)
        
        # Sauvegarder la réponse
        self.memoire.append({
            "timestamp": time.time(),
            "message": reponse,
            "type": "assistant"
        })
        
        return reponse, intention

# Instance globale de l'agent
agent = AgentReel()

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint compatible OpenAI avec agent réel"""
    try:
        data = request.json
        messages = data.get('messages', [])
        max_tokens = data.get('max_tokens', 200)
        
        if not messages:
            return jsonify({"error": "Aucun message fourni"}), 400
        
        # Récupérer le dernier message utilisateur
        user_message = messages[-1].get('content', '')
        
        # Traiter avec l'agent réel
        reponse, intention = agent.traiter_message(user_message)
        
        # Format OpenAI
        return jsonify({
            "id": f"chatcmpl-jarvis-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "jarvis-real-agent",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": reponse
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(user_message.split()),
                "completion_tokens": len(reponse.split()),
                "total_tokens": len(user_message.split()) + len(reponse.split())
            },
            "metadata": {
                "agent": "JARVIS Real Agent",
                "intention": intention,
                "timestamp": time.time()
            }
        })
        
    except Exception as e:
        return jsonify({"error": f"Erreur agent: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de santé"""
    return jsonify({
        "status": "ok",
        "agent": "JARVIS Real Agent",
        "owner": "Jean-Luc Passave",
        "memory_size": len(agent.memoire),
        "uptime": time.time()
    })

def main():
    """Fonction principale"""
    print("🤖 AGENT RÉEL JARVIS - JEAN-LUC PASSAVE")
    print("🧠 INTELLIGENCE ARTIFICIELLE RÉELLE")
    print("🚫 AUCUNE SIMULATION - VRAIES RÉPONSES")
    print("=" * 50)
    print(f"👤 Propriétaire: {agent.proprietaire}")
    print(f"🤖 Agent: {agent.nom}")
    print(f"🎯 Spécialités: {', '.join(agent.contexte['specialites'])}")
    print("🌐 Serveur démarré sur http://localhost:8000")
    print("🔗 Compatible OpenAI API")
    print("✅ Prêt pour conversations réelles")
    print("=" * 50)
    
    app.run(host='localhost', port=8000, debug=False)

if __name__ == "__main__":
    main()
