#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS INTERFACE AUDIO AMÉLIORÉE - JEAN-LUC PASSAVE
Interface basée sur le code qui fonctionne avec ajout des fonctionnalités audio
"""

import gradio as gr
import json
import os
import datetime
import tempfile
import webbrowser
import requests
import time

def text_to_speech_jarvis(text):
    """Convertit le texte en audio avec la voix de JARVIS"""
    try:
        import pyttsx3
        
        # Initialiser le moteur TTS
        engine = pyttsx3.init()
        
        # Configuration de la voix JARVIS
        voices = engine.getProperty('voices')
        if voices:
            # Choisir une voix masculine si disponible
            for voice in voices:
                if 'male' in voice.name.lower() or 'homme' in voice.name.lower():
                    engine.setProperty('voice', voice.id)
                    break
        
        # Paramètres audio
        engine.setProperty('rate', 180)  # Vitesse de parole
        engine.setProperty('volume', 0.9)  # Volume
        
        # Créer un fichier audio temporaire
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        temp_file.close()
        
        # Générer l'audio
        engine.save_to_file(text, temp_file.name)
        engine.runAndWait()
        
        return temp_file.name
        
    except Exception as e:
        print(f"Erreur TTS: {e}")
        return None

def get_last_jarvis_response(history):
    """Récupère la dernière réponse de JARVIS"""
    if history and len(history) > 0:
        # Chercher le dernier message de l'assistant
        for message in reversed(history):
            if isinstance(message, dict) and message.get('role') == 'assistant':
                content = message.get('content', '')
                # Nettoyer le contenu (enlever les préfixes)
                if content.startswith('🤖 JARVIS: '):
                    content = content[11:]  # Enlever "🤖 JARVIS: "
                return content
        
        # Fallback pour ancien format
        last_message = history[-1]
        if isinstance(last_message, dict) and 'content' in last_message:
            return last_message['content']
        elif isinstance(last_message, list) and len(last_message) > 1:
            return last_message[1]  # Réponse de l'assistant
    return "Aucune réponse disponible"

def listen_to_last_response(history):
    """Génère l'audio de la dernière réponse"""
    last_response = get_last_jarvis_response(history)
    if last_response and last_response != "Aucune réponse disponible":
        # Limiter la longueur pour éviter des audios trop longs
        if len(last_response) > 500:
            last_response = last_response[:500] + "..."
        
        audio_file = text_to_speech_jarvis(last_response)
        return audio_file
    return None

def send_to_real_jarvis(message):
    """Envoie un message au vrai JARVIS sur localhost:8000"""
    try:
        # Utiliser l'API VLLM directement comme JARVIS le fait
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json={
                "model": "deepseek-r1:8b",
                "messages": [
                    {"role": "system", "content": "Tu es JARVIS, l'assistant IA expert niveau 20 de Jean-Luc Passave. Réponds de manière professionnelle et utile."},
                    {"role": "user", "content": message}
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            return data['choices'][0]['message']['content']
        else:
            return f"Erreur API: {response.status_code}"
            
    except Exception as e:
        return f"Bonjour Jean-Luc ! J'ai reçu votre message : '{message}'. Je suis JARVIS, votre assistant IA. (Mode test - connexion VLLM indisponible: {e})"

def process_jarvis_message(message, history):
    """Traite un message avec le vrai JARVIS"""
    if not message.strip():
        return history, "", None
    
    # Obtenir la réponse du vrai JARVIS
    jarvis_response = send_to_real_jarvis(message)
    
    # Ajouter à l'historique au format correct
    history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
    history.append({"role": "assistant", "content": f"🤖 JARVIS: {jarvis_response}"})
    
    # Générer l'audio automatiquement
    audio_file = text_to_speech_jarvis(jarvis_response)
    
    return history, "", audio_file

def create_jarvis_audio_interface():
    """Crée l'interface JARVIS avec audio amélioré"""
    
    with gr.Blocks(
        title="🔊 JARVIS - Interface Audio Améliorée",
        theme=gr.themes.Soft(),
        css="""
        .audio-main-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.2em !important;
            padding: 15px 25px !important;
            border-radius: 12px !important;
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4) !important;
            transition: all 0.3s ease !important;
        }
        .audio-main-btn:hover {
            background: linear-gradient(45deg, #c0392b, #a93226) !important;
            transform: translateY(-3px) !important;
            box-shadow: 0 8px 16px rgba(231, 76, 60, 0.5) !important;
        }
        .audio-secondary-btn {
            background: linear-gradient(45deg, #9C27B0, #7B1FA2) !important;
            color: white !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }
        .audio-secondary-btn:hover {
            transform: scale(1.05) !important;
            box-shadow: 0 4px 8px rgba(156, 39, 176, 0.3) !important;
        }
        """
    ) as interface:
        
        # ENTÊTE JARVIS
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #2c2c2c, #6a4c93, #9c27b0); color: white; padding: 20px; margin: -20px -20px 20px -20px; border-radius: 0 0 15px 15px;">
            <h1 style="margin: 0; font-size: 2em;">🔊 JARVIS - Interface Audio Améliorée</h1>
            <div style="margin: 15px 0;">
                <span style="display: inline-block; width: 15px; height: 15px; background: #4CAF50; border-radius: 50%; margin-right: 10px; animation: pulse 2s infinite;"></span>
                <span style="font-size: 1.2em; font-weight: bold;">JARVIS ACTIF - Audio Fonctionnel</span>
            </div>
            <p style="margin: 10px 0 0 0; font-size: 1em; opacity: 0.9;">Interface basée sur le code qui fonctionne + Audio</p>
        </div>
        <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        </style>
        """)
        
        with gr.Row():
            # COLONNE PRINCIPALE
            with gr.Column(scale=2):
                # CHAT PRINCIPAL AVEC JARVIS RÉEL
                main_chat = gr.Chatbot(
                    value=[],
                    height=600,
                    label="💬 Conversation avec JARVIS Expert Niveau 20 - AUDIO ACTIVÉ",
                    show_copy_button=True,
                    avatar_images=("👨‍💻", "🤖"),
                    type="messages",
                    elem_id="main_chatbot"
                )
                
                # ZONE AUDIO PRINCIPALE
                audio_output = gr.Audio(
                    label="🔊 Audio JARVIS - Écouter les Réponses",
                    visible=True,
                    autoplay=True,  # Lecture automatique
                    elem_id="jarvis_audio_player"
                )
                
                # ZONE DE SAISIE
                with gr.Row():
                    user_input = gr.Textbox(
                        value="",
                        label="💬 Votre message à JARVIS",
                        scale=4,
                        lines=2,
                        placeholder="Tapez votre message ici..."
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)
                
                # BOUTONS AUDIO PRINCIPAUX
                gr.HTML("""
                <h3 style='margin: 20px 0 15px 0; color: #e74c3c; text-align: center; font-size: 1.4em;'>
                    🎵 CONTRÔLES AUDIO JARVIS
                </h3>
                """)
                
                with gr.Row():
                    listen_last_btn = gr.Button(
                        "🔊 ÉCOUTER DERNIÈRE RÉPONSE", 
                        variant="primary", 
                        size="lg",
                        elem_classes=["audio-main-btn"]
                    )
                
                with gr.Row():
                    test_audio_btn = gr.Button("🎧 Test Audio", variant="secondary", size="sm", elem_classes=["audio-secondary-btn"])
                    clear_chat_btn = gr.Button("🗑️ Effacer Chat", variant="secondary", size="sm", elem_classes=["audio-secondary-btn"])
                    open_main_btn = gr.Button("🏠 Interface Principale", variant="secondary", size="sm", elem_classes=["audio-secondary-btn"])
            
            # COLONNE LATÉRALE
            with gr.Column(scale=1):
                gr.HTML("<h3 style='color: #9C27B0; margin: 0 0 15px 0;'>🧠 Statut JARVIS</h3>")
                
                status_display = gr.HTML("""
                <div style='background: linear-gradient(135deg, #f3e5f5, #e1bee7); padding: 20px; border-radius: 15px; border-left: 6px solid #9C27B0; font-size: 1.1em;'>
                    <div style='margin: 10px 0; padding: 12px; background: #2c3e50; color: #ecf0f1; border-radius: 8px; border: 2px solid #3498db;'>
                        <strong style="color: #f39c12; font-size: 1.2em;">💭 Statut:</strong><br>
                        Interface audio prête
                    </div>
                    <div style='margin: 8px 0; padding: 10px; background: #34495e; color: #ecf0f1; border-radius: 5px; border: 1px solid #27ae60;'>
                        <strong style="color: #27ae60;">🔍 Connexion:</strong><br>
                        VLLM localhost:8000
                    </div>
                    <div style='margin: 8px 0; padding: 10px; background: #2c3e50; color: #ecf0f1; border-radius: 5px; border: 1px solid #e74c3c;'>
                        <strong style="color: #e74c3c;">⚡ Audio:</strong><br>
                        Synthèse vocale active
                    </div>
                </div>
                """)
                
                # INFORMATIONS TECHNIQUES
                gr.HTML("<h4 style='color: #6a4c93; margin: 20px 0 10px 0;'>ℹ️ Informations</h4>")
                
                info_display = gr.HTML("""
                <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; border: 1px solid #dee2e6; font-size: 0.9em;'>
                    <p><strong>🔊 Audio:</strong> Synthèse vocale automatique</p>
                    <p><strong>🤖 IA:</strong> DeepSeek R1 8B via VLLM</p>
                    <p><strong>🌐 Port:</strong> 8106 (Interface Audio)</p>
                    <p><strong>⚡ Statut:</strong> Opérationnel</p>
                </div>
                """)
        
        # FONCTIONS
        def test_audio_simple():
            """Test audio simple"""
            test_text = "Bonjour Jean-Luc, ceci est un test audio de JARVIS. L'interface audio fonctionne parfaitement."
            audio_file = text_to_speech_jarvis(test_text)
            return audio_file
        
        def clear_conversation():
            """Efface la conversation"""
            return [], "Conversation effacée"
        
        def open_main_interface():
            """Ouvre l'interface principale"""
            webbrowser.open("http://localhost:8100")
            return "🏠 Interface principale ouverte dans votre navigateur"
        
        # CONNEXIONS DES BOUTONS
        send_btn.click(
            fn=process_jarvis_message,
            inputs=[user_input, main_chat],
            outputs=[main_chat, user_input, audio_output]
        )
        
        listen_last_btn.click(
            fn=listen_to_last_response,
            inputs=[main_chat],
            outputs=[audio_output]
        )
        
        test_audio_btn.click(
            fn=test_audio_simple,
            outputs=[audio_output]
        )
        
        clear_chat_btn.click(
            fn=clear_conversation,
            outputs=[main_chat, status_display]
        )
        
        open_main_btn.click(
            fn=open_main_interface,
            outputs=[status_display]
        )
        
        # RACCOURCI CLAVIER ENTER
        user_input.submit(
            fn=process_jarvis_message,
            inputs=[user_input, main_chat],
            outputs=[main_chat, user_input, audio_output]
        )
    
    return interface

if __name__ == "__main__":
    print("🔊 LANCEMENT JARVIS INTERFACE AUDIO AMÉLIORÉE - JEAN-LUC PASSAVE")
    print("=" * 70)
    print("🤖 Connexion au vrai JARVIS sur localhost:8000")
    print("🔊 Synthèse vocale activée")
    print("🌐 Interface disponible sur localhost:8106")
    print("=" * 70)
    
    # Créer et lancer l'interface
    jarvis_interface = create_jarvis_audio_interface()
    
    # Lancer sur le port 8106
    jarvis_interface.launch(
        server_name="localhost",
        server_port=8106,
        share=False,
        debug=True,
        show_error=True
    )
