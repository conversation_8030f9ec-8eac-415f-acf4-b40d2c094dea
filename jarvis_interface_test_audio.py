#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS INTERFACE TEST AUDIO - JEAN-LUC PASSAVE
Interface dédiée aux tests audio avec navigation aller-retour
Port 8111 - Interface audio complète
"""

import gradio as gr
import datetime

def create_audio_status(message):
    """Crée l'affichage de statut pour les tests audio"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    
    return f"""
    <div style="background: linear-gradient(135deg, #e3f2fd, #bbdefb); padding: 20px; border-radius: 15px; border-left: 6px solid #2196f3; margin: 10px 0;">
        <div style="text-align: center; margin-bottom: 15px;">
            <h3 style="color: #2196f3; margin: 0; font-size: 1.4em;">🔊 TEST AUDIO JARVIS COMPLET</h3>
            <p style="margin: 5px 0; color: #666;">Interface Audio Dédiée - <PERSON><PERSON></p>
        </div>
        
        <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin: 10px 0; border-left: 3px solid #2196f3;">
            <strong style="color: #2196f3; font-size: 1.1em;">🔊 Statut Audio [{current_time}]:</strong>
            <div style="margin-top: 5px;">{message}</div>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin: 0 0 10px 0;">🎧 Tests Audio Disponibles</h4>
            <div style="font-size: 0.9em; line-height: 1.4;">
                <div style="margin: 5px 0;"><strong>🔊 Test Basique</strong> : Vérification synthèse vocale</div>
                <div style="margin: 5px 0;"><strong>🎵 Test Avancé</strong> : Voix française optimisée</div>
                <div style="margin: 5px 0;"><strong>🤖 Test JARVIS</strong> : Simulation réponse agent</div>
                <div style="margin: 5px 0;"><strong>⚡ Test Rapide</strong> : Vérification instantanée</div>
                <div style="margin: 5px 0;"><strong>⏹️ Arrêt Audio</strong> : Contrôle d'urgence</div>
            </div>
        </div>
        
        <div style="background: #d1ecf1; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #17a2b8;">
            <h4 style="color: #0c5460; margin: 0 0 10px 0;">🏠 Navigation Aller-Retour</h4>
            <div style="font-size: 0.9em; line-height: 1.4;">
                <div style="margin: 5px 0;">🏠 <strong>Interface Principale</strong> : Retour à localhost:7866</div>
                <div style="margin: 5px 0;">⚙️ <strong>Configuration</strong> : Contrôles audio localhost:8114</div>
                <div style="margin: 5px 0;">📊 <strong>Surveillance</strong> : Monitoring localhost:8260</div>
                <div style="margin: 5px 0;">↩️ <strong>Dernier Onglet</strong> : Dernière interface visitée</div>
            </div>
        </div>
    </div>
    
    <script>
    // TESTS AUDIO JARVIS - JEAN-LUC PASSAVE
    function testAudioBasique() {{
        if ('speechSynthesis' in window) {{
            window.speechSynthesis.cancel();
            const utterance = new SpeechSynthesisUtterance('Test audio basique JARVIS. Bonjour Jean-Luc.');
            utterance.rate = 0.85;
            utterance.pitch = 0.8;
            utterance.volume = 1.0;
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée');
        }}
    }}
    
    function testAudioAvance() {{
        if ('speechSynthesis' in window) {{
            window.speechSynthesis.cancel();
            const utterance = new SpeechSynthesisUtterance('Test audio avancé JARVIS. Interface complète opérationnelle. Tous les systèmes fonctionnent parfaitement pour Jean-Luc Passave.');
            utterance.rate = 0.85;
            utterance.pitch = 0.8;
            utterance.volume = 1.0;
            
            const voices = window.speechSynthesis.getVoices();
            const frenchVoice = voices.find(voice => 
                voice.lang.includes('fr') || 
                voice.name.toLowerCase().includes('french')
            );
            
            if (frenchVoice) {{
                utterance.voice = frenchVoice;
            }}
            
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée');
        }}
    }}
    
    function testAudioJarvis() {{
        if ('speechSynthesis' in window) {{
            window.speechSynthesis.cancel();
            const utterance = new SpeechSynthesisUtterance('Bonjour Jean-Luc. Je suis JARVIS, votre assistant intelligent. Tous mes systèmes audio sont parfaitement opérationnels. Interface de test audio validée et fonctionnelle.');
            utterance.rate = 0.85;
            utterance.pitch = 0.8;
            utterance.volume = 1.0;
            
            const voices = window.speechSynthesis.getVoices();
            const frenchVoice = voices.find(voice => 
                voice.lang.includes('fr') || 
                voice.name.toLowerCase().includes('french')
            );
            
            if (frenchVoice) {{
                utterance.voice = frenchVoice;
            }}
            
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée');
        }}
    }}
    
    function testAudioRapide() {{
        if ('speechSynthesis' in window) {{
            window.speechSynthesis.cancel();
            const utterance = new SpeechSynthesisUtterance('Test rapide OK.');
            utterance.rate = 1.0;
            utterance.pitch = 0.8;
            utterance.volume = 1.0;
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée');
        }}
    }}
    
    function arreterAudio() {{
        window.speechSynthesis.cancel();
        console.log('⏹️ Audio JARVIS arrêté');
    }}
    
    // NAVIGATION ALLER-RETOUR
    function goToHome() {{
        localStorage.setItem('jarvis_last_url', window.location.href);
        window.location.href = 'http://localhost:7866';
    }}
    
    function goToConfig() {{
        localStorage.setItem('jarvis_last_url', window.location.href);
        window.open('http://localhost:8114', '_blank');
    }}
    
    function goToSurveillance() {{
        localStorage.setItem('jarvis_last_url', window.location.href);
        window.open('http://localhost:8260', '_blank');
    }}
    
    function goToLastTab() {{
        const lastUrl = localStorage.getItem('jarvis_last_url');
        if (lastUrl && lastUrl !== window.location.href) {{
            window.open(lastUrl, '_blank');
        }} else {{
            window.open('http://localhost:7866', '_blank');
        }}
    }}
    
    console.log('🔊 Interface Test Audio JARVIS initialisée');
    </script>
    """

def create_jarvis_audio_interface():
    """Interface de test audio JARVIS"""
    
    with gr.Blocks(
        title="🔊 JARVIS Test Audio Complet",
        theme=gr.themes.Soft(),
        css="""
        .audio-btn {
            background: linear-gradient(45deg, #2196f3, #21cbf3) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.2em !important;
            padding: 15px 25px !important;
            border-radius: 20px !important;
            box-shadow: 0 6px 15px rgba(33, 150, 243, 0.3) !important;
            transition: all 0.3s ease !important;
        }
        .audio-btn:hover {
            background: linear-gradient(45deg, #21cbf3, #2196f3) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.4) !important;
        }
        .stop-btn {
            background: linear-gradient(45deg, #f44336, #ff5722) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.2em !important;
            padding: 15px 25px !important;
            border-radius: 20px !important;
            box-shadow: 0 6px 15px rgba(244, 67, 54, 0.3) !important;
            transition: all 0.3s ease !important;
        }
        .stop-btn:hover {
            background: linear-gradient(45deg, #ff5722, #f44336) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(244, 67, 54, 0.4) !important;
        }
        """
    ) as interface:
        
        # Header avec Navigation
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #2196f3, #21cbf3); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <button onclick="goToHome()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold;">
                    🏠 INTERFACE PRINCIPALE
                </button>
                <div style="text-align: center;">
                    <h1 style="margin: 0; font-size: 2.2em;">🔊 Test Audio Complet</h1>
                </div>
                <button onclick="goToLastTab()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold;">
                    ↩️ DERNIER ONGLET
                </button>
            </div>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Interface Audio Dédiée - Jean-Luc Passave</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                # TESTS AUDIO PRINCIPAUX
                gr.HTML("<h3 style='color: #2196f3; text-align: center;'>🔊 TESTS AUDIO JARVIS</h3>")
                
                with gr.Row():
                    test_basique_btn = gr.Button("🔊 TEST BASIQUE", elem_classes=["audio-btn"])
                    test_avance_btn = gr.Button("🎵 TEST AVANCÉ", elem_classes=["audio-btn"])
                
                with gr.Row():
                    test_jarvis_btn = gr.Button("🤖 TEST JARVIS", elem_classes=["audio-btn"])
                    test_rapide_btn = gr.Button("⚡ TEST RAPIDE", elem_classes=["audio-btn"])
                
                with gr.Row():
                    stop_audio_btn = gr.Button("⏹️ ARRÊTER AUDIO", elem_classes=["stop-btn"])
                
                # CONTRÔLES AUDIO AVANCÉS
                gr.HTML("<h3 style='color: #2196f3; text-align: center; margin-top: 30px;'>🎛️ Contrôles Audio</h3>")
                
                with gr.Row():
                    volume_slider = gr.Slider(
                        minimum=0.1,
                        maximum=1.0,
                        value=1.0,
                        step=0.1,
                        label="🔊 Volume",
                        scale=2
                    )
                    speed_slider = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        value=0.85,
                        step=0.05,
                        label="⚡ Vitesse",
                        scale=1
                    )
                
                with gr.Row():
                    pitch_slider = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        value=0.8,
                        step=0.1,
                        label="🎵 Tonalité",
                        scale=2
                    )
                    test_custom_btn = gr.Button("🎧 TEST PERSONNALISÉ", variant="primary", scale=1)
                
                # NAVIGATION
                gr.HTML("<h3 style='color: #17a2b8; text-align: center; margin-top: 30px;'>🏠 NAVIGATION</h3>")
                
                with gr.Row():
                    home_btn = gr.Button("🏠 INTERFACE PRINCIPALE", variant="primary")
                    config_btn = gr.Button("⚙️ CONFIGURATION", variant="secondary")
                    surveillance_btn = gr.Button("📊 SURVEILLANCE", variant="secondary")
            
            with gr.Column(scale=1):
                # Zone de statut
                status_display = gr.HTML(create_audio_status("Interface de test audio JARVIS prête"))
                
                # Informations
                gr.HTML("""
                <div style="background: #f8d7da; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin: 0 0 10px 0;">ℹ️ Informations Audio</h4>
                    <div style="font-size: 0.9em; line-height: 1.4;">
                        <div style="margin: 5px 0;">🔊 <strong>Port Audio</strong> : 8111</div>
                        <div style="margin: 5px 0;">🎧 <strong>Synthèse Vocale</strong> : Navigateur</div>
                        <div style="margin: 5px 0;">🎵 <strong>Voix Française</strong> : Auto-détection</div>
                        <div style="margin: 5px 0;">⚡ <strong>Contrôles</strong> : Volume, vitesse, tonalité</div>
                    </div>
                </div>
                """)
        
        # Fonctions
        def test_audio_basique():
            return create_audio_status("Test audio basique lancé - Vérification synthèse vocale")
        
        def test_audio_avance():
            return create_audio_status("Test audio avancé lancé - Voix française optimisée")
        
        def test_audio_jarvis():
            return create_audio_status("Test JARVIS lancé - Simulation réponse agent complète")
        
        def test_audio_rapide():
            return create_audio_status("Test rapide lancé - Vérification instantanée")
        
        def stop_audio():
            return create_audio_status("Audio JARVIS arrêté - Synthèse vocale interrompue")
        
        def test_custom_audio(volume, speed, pitch):
            return create_audio_status(f"Test personnalisé - Volume: {int(volume*100)}%, Vitesse: {speed:.2f}, Tonalité: {pitch:.1f}")
        
        # Connexions
        test_basique_btn.click(
            fn=test_audio_basique,
            outputs=[status_display],
            js="() => testAudioBasique()"
        )
        
        test_avance_btn.click(
            fn=test_audio_avance,
            outputs=[status_display],
            js="() => testAudioAvance()"
        )
        
        test_jarvis_btn.click(
            fn=test_audio_jarvis,
            outputs=[status_display],
            js="() => testAudioJarvis()"
        )
        
        test_rapide_btn.click(
            fn=test_audio_rapide,
            outputs=[status_display],
            js="() => testAudioRapide()"
        )
        
        stop_audio_btn.click(
            fn=stop_audio,
            outputs=[status_display],
            js="() => arreterAudio()"
        )
        
        test_custom_btn.click(
            fn=test_custom_audio,
            inputs=[volume_slider, speed_slider, pitch_slider],
            outputs=[status_display],
            js="(volume, speed, pitch) => { if ('speechSynthesis' in window) { window.speechSynthesis.cancel(); const utterance = new SpeechSynthesisUtterance('Test personnalisé JARVIS avec paramètres ajustés pour Jean-Luc.'); utterance.rate = speed; utterance.pitch = pitch; utterance.volume = volume; window.speechSynthesis.speak(utterance); } }"
        )
        
        home_btn.click(
            fn=lambda: create_audio_status("Redirection vers interface principale"),
            outputs=[status_display],
            js="() => goToHome()"
        )
        
        config_btn.click(
            fn=lambda: create_audio_status("Ouverture configuration système"),
            outputs=[status_display],
            js="() => goToConfig()"
        )
        
        surveillance_btn.click(
            fn=lambda: create_audio_status("Ouverture surveillance système"),
            outputs=[status_display],
            js="() => goToSurveillance()"
        )
    
    return interface

if __name__ == "__main__":
    print("🔊 JARVIS INTERFACE TEST AUDIO - JEAN-LUC PASSAVE")
    print("=" * 60)
    print("🔊 Tests audio complets: Basique, Avancé, JARVIS, Rapide")
    print("🎛️ Contrôles: Volume, vitesse, tonalité personnalisables")
    print("🏠 Navigation: Boutons aller-retour vers toutes interfaces")
    print("⏹️ Contrôle: Arrêt d'urgence audio")
    print("🌐 Interface disponible sur localhost:8111")
    print("=" * 60)
    
    interface = create_jarvis_audio_interface()
    interface.launch(
        server_name="localhost",
        server_port=8111,
        share=False,
        debug=True,
        show_error=True
    )
