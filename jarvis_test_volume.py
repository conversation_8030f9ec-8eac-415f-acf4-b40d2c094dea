#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS TEST VOLUME - JEAN-LUC PASSAVE
Test rapide pour vérifier que le volume fonctionne correctement
"""

import gradio as gr
import datetime

def create_volume_test_interface():
    """Interface de test rapide du volume JARVIS"""
    
    with gr.Blocks(
        title="🔊 Test Volume JARVIS",
        theme=gr.themes.Soft(),
        css="""
        .volume-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.3em !important;
            padding: 15px 25px !important;
            border-radius: 20px !important;
            box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4) !important;
            transition: all 0.3s ease !important;
        }
        .volume-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.5) !important;
        }
        """
    ) as interface:
        
        # Header
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #e74c3c, #c0392b); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 2.2em;">🔊 TEST VOLUME JARVIS</h1>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Vérification rapide du contrôle volume - Jean-Luc Passave</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column():
                # Contrôle de volume principal
                volume_slider = gr.Slider(
                    minimum=0.1,
                    maximum=1.0,
                    value=1.0,
                    step=0.1,
                    label="🔊 Volume JARVIS (0.1 = 10%, 1.0 = 100%)",
                    info="Bougez le curseur pour ajuster le volume"
                )
                
                # Boutons de test volume
                with gr.Row():
                    test_10_btn = gr.Button("🔉 TEST 10%", elem_classes=["volume-btn"])
                    test_50_btn = gr.Button("🔊 TEST 50%", elem_classes=["volume-btn"])
                    test_100_btn = gr.Button("📢 TEST 100%", elem_classes=["volume-btn"])
                
                # Tests de vitesse
                with gr.Row():
                    test_slow_btn = gr.Button("🐌 LENT", variant="secondary")
                    test_normal_btn = gr.Button("🎯 NORMAL", variant="secondary")
                    test_fast_btn = gr.Button("⚡ RAPIDE", variant="secondary")
                
                # Tests de tonalité
                with gr.Row():
                    test_grave_btn = gr.Button("🎭 GRAVE", variant="secondary")
                    test_medium_btn = gr.Button("🎵 MOYEN", variant="secondary")
                    test_aigu_btn = gr.Button("🎶 AIGU", variant="secondary")
        
        # Zone de statut
        status_display = gr.HTML("""
        <div style="background: #f0f8ff; padding: 20px; border-radius: 15px; margin: 20px 0; border-left: 6px solid #2196f3;">
            <h3 style="color: #2196f3; margin: 0 0 15px 0;">📊 Statut Test Volume</h3>
            <div style="font-size: 1.1em; line-height: 1.6;">
                <p style="margin: 8px 0;"><strong>🔊 Volume actuel:</strong> 100%</p>
                <p style="margin: 8px 0;"><strong>⚡ Vitesse actuelle:</strong> Normale (0.85)</p>
                <p style="margin: 8px 0;"><strong>🎵 Tonalité actuelle:</strong> Grave (0.8)</p>
                <p style="margin: 8px 0;"><strong>⏰ Dernière mise à jour:</strong> """ + datetime.datetime.now().strftime('%H:%M:%S') + """</p>
            </div>
        </div>
        """)
        
        # JavaScript pour les tests de volume
        gr.HTML("""
        <script>
        function testVolumeLevel(volume, label) {
            localStorage.setItem('jarvis_volume', volume);
            
            if ('speechSynthesis' in window) {
                window.speechSynthesis.cancel();
                
                const message = 'Test volume JARVIS à ' + Math.round(volume * 100) + ' pourcent. ' + label + '. Jean-Luc, entendez-vous bien cette annonce ?';
                const utterance = new SpeechSynthesisUtterance(message);
                
                utterance.volume = volume;
                utterance.rate = 0.85;
                utterance.pitch = 0.8;
                
                // Chercher une voix française
                const voices = window.speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice => 
                    voice.lang.includes('fr') || 
                    voice.name.toLowerCase().includes('french')
                );
                if (frenchVoice) utterance.voice = frenchVoice;
                
                utterance.onstart = () => console.log('🔊 Test volume démarré:', volume);
                utterance.onend = () => console.log('✅ Test volume terminé');
                
                window.speechSynthesis.speak(utterance);
            }
        }
        
        function testSpeed(speed, label) {
            localStorage.setItem('jarvis_speed', speed);
            
            if ('speechSynthesis' in window) {
                window.speechSynthesis.cancel();
                
                const volume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
                const message = 'Test vitesse JARVIS. Mode ' + label + '. Vitesse réglée à ' + speed + '.';
                const utterance = new SpeechSynthesisUtterance(message);
                
                utterance.volume = volume;
                utterance.rate = speed;
                utterance.pitch = 0.8;
                
                const voices = window.speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice => voice.lang.includes('fr'));
                if (frenchVoice) utterance.voice = frenchVoice;
                
                window.speechSynthesis.speak(utterance);
            }
        }
        
        function testPitch(pitch, label) {
            localStorage.setItem('jarvis_pitch', pitch);
            
            if ('speechSynthesis' in window) {
                window.speechSynthesis.cancel();
                
                const volume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
                const message = 'Test tonalité JARVIS. Mode ' + label + '. Tonalité réglée à ' + pitch + '.';
                const utterance = new SpeechSynthesisUtterance(message);
                
                utterance.volume = volume;
                utterance.rate = 0.85;
                utterance.pitch = pitch;
                
                const voices = window.speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice => voice.lang.includes('fr'));
                if (frenchVoice) utterance.voice = frenchVoice;
                
                window.speechSynthesis.speak(utterance);
            }
        }
        
        function updateVolumeFromSlider(volume) {
            localStorage.setItem('jarvis_volume', volume);
            console.log('🔊 Volume mis à jour via curseur:', volume);
            
            // Test automatique du nouveau volume
            setTimeout(() => {
                testVolumeLevel(volume, 'Volume ajusté via curseur');
            }, 500);
        }
        
        // Message de bienvenue
        window.addEventListener('load', function() {
            setTimeout(() => {
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance('Interface de test volume JARVIS chargée. Utilisez les boutons pour tester différents niveaux de volume.');
                    utterance.volume = 1.0;
                    utterance.rate = 0.85;
                    utterance.pitch = 0.8;
                    
                    const voices = window.speechSynthesis.getVoices();
                    const frenchVoice = voices.find(voice => voice.lang.includes('fr'));
                    if (frenchVoice) utterance.voice = frenchVoice;
                    
                    window.speechSynthesis.speak(utterance);
                }
            }, 2000);
        });
        </script>
        """)
        
        # Fonctions Python
        def update_volume_status(volume):
            current_time = datetime.datetime.now().strftime('%H:%M:%S')
            return f"""
            <div style="background: #e8f5e8; padding: 20px; border-radius: 15px; margin: 20px 0; border-left: 6px solid #27ae60;">
                <h3 style="color: #27ae60; margin: 0 0 15px 0;">🔊 Volume Mis à Jour</h3>
                <div style="font-size: 1.1em; line-height: 1.6;">
                    <p style="margin: 8px 0;"><strong>🔊 Nouveau volume:</strong> {int(volume * 100)}%</p>
                    <p style="margin: 8px 0;"><strong>⚡ Vitesse:</strong> Normale (0.85)</p>
                    <p style="margin: 8px 0;"><strong>🎵 Tonalité:</strong> Grave (0.8)</p>
                    <p style="margin: 8px 0;"><strong>⏰ Mise à jour:</strong> {current_time}</p>
                    <p style="margin: 8px 0; color: #27ae60;"><strong>✅ Test audio automatique lancé</strong></p>
                </div>
            </div>
            """
        
        def test_volume_level(level, label):
            current_time = datetime.datetime.now().strftime('%H:%M:%S')
            return f"""
            <div style="background: #fff3e0; padding: 20px; border-radius: 15px; margin: 20px 0; border-left: 6px solid #ff9800;">
                <h3 style="color: #ff9800; margin: 0 0 15px 0;">🔊 Test Volume {label}</h3>
                <div style="font-size: 1.1em; line-height: 1.6;">
                    <p style="margin: 8px 0;"><strong>🔊 Volume testé:</strong> {int(level * 100)}%</p>
                    <p style="margin: 8px 0;"><strong>📢 Niveau:</strong> {label}</p>
                    <p style="margin: 8px 0;"><strong>⏰ Test lancé:</strong> {current_time}</p>
                    <p style="margin: 8px 0; color: #ff9800;"><strong>🎧 Écoutez le message de test</strong></p>
                </div>
            </div>
            """
        
        # Connexions
        volume_slider.change(
            fn=update_volume_status,
            inputs=[volume_slider],
            outputs=[status_display],
            js="updateVolumeFromSlider"
        )
        
        test_10_btn.click(
            fn=lambda: test_volume_level(0.1, "10%"),
            outputs=[status_display],
            js="() => testVolumeLevel(0.1, 'Volume faible')"
        )
        
        test_50_btn.click(
            fn=lambda: test_volume_level(0.5, "50%"),
            outputs=[status_display],
            js="() => testVolumeLevel(0.5, 'Volume moyen')"
        )
        
        test_100_btn.click(
            fn=lambda: test_volume_level(1.0, "100%"),
            outputs=[status_display],
            js="() => testVolumeLevel(1.0, 'Volume maximum')"
        )
        
        test_slow_btn.click(
            fn=lambda: test_volume_level(0.5, "Lent"),
            outputs=[status_display],
            js="() => testSpeed(0.5, 'lent')"
        )
        
        test_normal_btn.click(
            fn=lambda: test_volume_level(0.85, "Normal"),
            outputs=[status_display],
            js="() => testSpeed(0.85, 'normal')"
        )
        
        test_fast_btn.click(
            fn=lambda: test_volume_level(1.5, "Rapide"),
            outputs=[status_display],
            js="() => testSpeed(1.5, 'rapide')"
        )
        
        test_grave_btn.click(
            fn=lambda: test_volume_level(0.6, "Grave"),
            outputs=[status_display],
            js="() => testPitch(0.6, 'grave')"
        )
        
        test_medium_btn.click(
            fn=lambda: test_volume_level(1.0, "Moyen"),
            outputs=[status_display],
            js="() => testPitch(1.0, 'moyen')"
        )
        
        test_aigu_btn.click(
            fn=lambda: test_volume_level(1.4, "Aigu"),
            outputs=[status_display],
            js="() => testPitch(1.4, 'aigu')"
        )
    
    return interface

if __name__ == "__main__":
    print("🔊 JARVIS TEST VOLUME - JEAN-LUC PASSAVE")
    print("=" * 50)
    print("🎧 Test rapide du contrôle volume")
    print("🔊 Vérification curseur et boutons")
    print("⚡ Tests vitesse et tonalité")
    print("🌐 Interface: localhost:8113")
    print("=" * 50)
    
    interface = create_volume_test_interface()
    interface.launch(
        server_name="localhost",
        server_port=8113,
        share=False,
        debug=True,
        show_error=True
    )
