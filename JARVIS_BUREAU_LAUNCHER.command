#!/bin/bash

# 🚀 LANCEUR BUREAU JARVIS - JEAN-LUC PASSAVE
# Double-clic depuis le bureau pour démarrer JARVIS - 25 JUIN 2025

# Ouvrir une nouvelle fenêtre Terminal pour voir les logs
osascript -e 'tell application "Terminal" to do script "cd \"'$(dirname "$0")'\"; echo \"🤖 JARVIS BUREAU LAUNCHER - JEAN-LUC PASSAVE\"; echo \"===========================================\"; echo \"🚀 Démarrage depuis le bureau...\"; echo \"\"; ./JARVIS_SIMPLE_LAUNCHER.command"'

# Ouvrir automatiquement le navigateur sur l'interface JARVIS
sleep 3
open "http://localhost:8110"
