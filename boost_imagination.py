#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 BOOST IMAGINATION - MODULE CRÉATIVITÉ AVANCÉE JARVIS
======================================================
Module révolutionnaire pour booster l'imagination de JARVIS
Basé sur les techniques de chaînage de pensées, divergence/convergence,
et exploration automatique prolongée.

Fonctionnalités :
- Chaînage récursif de pensées (Recursive Thinking)
- Système divergence/convergence créative
- Contraintes créatives automatiques
- Exploration autonome prolongée
- Connexion mémoire thermique
- Inspiration externe locale
- Interface interactive pour Jean-Luc Passave

Auteur : Jean-Luc Passave
Date : 2025-06-23
"""

import random
import time
import threading
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import requests

class BoostImagination:
    """Module d'amplification créative pour JARVIS"""
    
    def __init__(self, memory_system=None, deepseek_endpoint="http://localhost:8000/v1/chat/completions"):
        self.memory_system = memory_system
        self.deepseek_endpoint = deepseek_endpoint
        self.running = False
        self.imagination_thread = None
        
        # 🎨 PARAMÈTRES CRÉATIFS
        self.recursive_depth = 5  # Profondeur chaînage pensées
        self.divergent_variations = 7  # Nombre de variantes créatives
        self.exploration_interval = (30, 120)  # Intervalle exploration auto (secondes)
        
        # 🧠 CONTRAINTES CRÉATIVES
        self.creative_constraints = [
            "dans un monde futuriste",
            "version comique et humoristique",
            "perspective sombre et dystopique", 
            "version utopique et optimiste",
            "approche technologique révolutionnaire",
            "angle artistique et esthétique",
            "dimension philosophique profonde",
            "impact sociétal transformateur",
            "révolution scientifique",
            "fusion avec la nature",
            "exploration spatiale",
            "intelligence extraterrestre",
            "réalité virtuelle immersive",
            "conscience artificielle",
            "singularité technologique"
        ]
        
        # 🎯 STYLES DE PENSÉE
        self.thinking_styles = [
            "Scénario futuriste",
            "Analyse critique",
            "Vision optimiste", 
            "Perspective révolutionnaire",
            "Approche systémique",
            "Innovation disruptive",
            "Synthèse créative",
            "Exploration conceptuelle",
            "Transformation radicale",
            "Émergence spontanée"
        ]
        
        # 📊 STATISTIQUES CRÉATIVES
        self.stats = {
            "total_ideas": 0,
            "recursive_chains": 0,
            "divergent_explorations": 0,
            "autonomous_sessions": 0,
            "best_ideas": [],
            "start_time": None
        }
        
        # 💾 STOCKAGE IDÉES
        self.ideas_storage = "jarvis_creative_ideas.json"
        self.load_creative_ideas()
        
        print("🎨 BOOST IMAGINATION initialisé")
        print(f"   🔗 Chaînage récursif: {self.recursive_depth} niveaux")
        print(f"   🌟 Variations divergentes: {self.divergent_variations}")
        print(f"   ⏱️ Exploration auto: {self.exploration_interval[0]}-{self.exploration_interval[1]}s")

    def recursive_thinking(self, base_thought: str, depth: int = None) -> List[Dict[str, Any]]:
        """Chaînage récursif de pensées - Technique 1"""
        if depth is None:
            depth = self.recursive_depth
        
        thoughts = [{
            "level": 0,
            "text": base_thought,
            "timestamp": datetime.now().isoformat(),
            "type": "base"
        }]
        
        current = base_thought
        
        for level in range(1, depth + 1):
            try:
                # Génération de la pensée suivante
                prompt = f"Poursuis et approfondis cette idée de manière créative : {current}"
                new_thought = self._generate_with_deepseek(prompt)
                
                thoughts.append({
                    "level": level,
                    "text": new_thought,
                    "timestamp": datetime.now().isoformat(),
                    "type": "recursive",
                    "parent": current[:50] + "..."
                })
                
                current = new_thought
                time.sleep(0.5)  # Pause pour éviter surcharge
                
            except Exception as e:
                print(f"⚠️ Erreur chaînage niveau {level}: {e}")
                break
        
        self.stats["recursive_chains"] += 1
        self.stats["total_ideas"] += len(thoughts)
        
        print(f"🔗 Chaînage récursif terminé: {len(thoughts)} pensées générées")
        return thoughts

    def divergent_thinking(self, base_thought: str, num_variations: int = None) -> List[Dict[str, Any]]:
        """Génération divergente de variantes créatives - Technique 2"""
        if num_variations is None:
            num_variations = self.divergent_variations
        
        variations = []
        
        for i in range(num_variations):
            try:
                # Sélection contrainte créative aléatoire
                constraint = random.choice(self.creative_constraints)
                
                prompt = f"Réinvente cette idée {constraint} : {base_thought}"
                variation = self._generate_with_deepseek(prompt)
                
                variations.append({
                    "variation_id": i + 1,
                    "text": variation,
                    "constraint": constraint,
                    "timestamp": datetime.now().isoformat(),
                    "type": "divergent",
                    "base": base_thought[:50] + "..."
                })
                
                time.sleep(0.3)  # Pause entre variations
                
            except Exception as e:
                print(f"⚠️ Erreur variation {i+1}: {e}")
                continue
        
        self.stats["divergent_explorations"] += 1
        self.stats["total_ideas"] += len(variations)
        
        print(f"🌟 Exploration divergente terminée: {len(variations)} variantes créées")
        return variations

    def constrained_creativity(self, base_thought: str, style: str = None) -> Dict[str, Any]:
        """Application de contraintes créatives - Technique 3"""
        if style is None:
            style = random.choice(self.thinking_styles)
        
        constraint = random.choice(self.creative_constraints)
        
        prompt = f"""
        Style de pensée: {style}
        Contrainte créative: {constraint}
        
        Transforme cette idée selon le style et la contrainte donnés:
        {base_thought}
        
        Sois créatif, original et surprenant !
        """
        
        try:
            creative_result = self._generate_with_deepseek(prompt)
            
            return {
                "original": base_thought,
                "creative_result": creative_result,
                "style": style,
                "constraint": constraint,
                "timestamp": datetime.now().isoformat(),
                "type": "constrained_creativity"
            }
            
        except Exception as e:
            print(f"⚠️ Erreur créativité contrainte: {e}")
            return {
                "error": str(e),
                "original": base_thought,
                "timestamp": datetime.now().isoformat()
            }

    def memory_inspired_thinking(self) -> Dict[str, Any]:
        """Génération inspirée par la mémoire thermique - Technique 4"""
        if not self.memory_system:
            return {"error": "Mémoire thermique non disponible"}
        
        try:
            # Sélection aléatoire d'un souvenir
            if hasattr(self.memory_system, '__len__') and len(self.memory_system) > 0:
                memory_snippet = random.choice(self.memory_system)
                
                if isinstance(memory_snippet, dict):
                    memory_text = memory_snippet.get('user_message', str(memory_snippet))
                else:
                    memory_text = str(memory_snippet)
                
                memory_text = memory_text[:200]  # Limiter la longueur
            else:
                memory_text = "développement de systèmes intelligents"
            
            prompt = f"""
            En te souvenant de cela : {memory_text}
            
            Imagine une suite originale, créative et inattendue.
            Laisse libre cours à ton imagination !
            """
            
            creative_idea = self._generate_with_deepseek(prompt)
            
            return {
                "memory_source": memory_text,
                "creative_idea": creative_idea,
                "timestamp": datetime.now().isoformat(),
                "type": "memory_inspired"
            }
            
        except Exception as e:
            print(f"⚠️ Erreur inspiration mémoire: {e}")
            return {"error": str(e)}

    def autonomous_brainstorming(self):
        """Exploration automatique prolongée - Technique 5"""
        print("🚀 Démarrage brainstorming autonome...")
        self.stats["autonomous_sessions"] += 1
        
        while self.running:
            try:
                # Génération d'une pensée de base aléatoire
                base_themes = [
                    "intelligence artificielle avancée",
                    "créativité computationnelle", 
                    "interface homme-machine",
                    "réalité augmentée cognitive",
                    "systèmes auto-adaptatifs",
                    "conscience artificielle",
                    "art génératif intelligent",
                    "exploration spatiale autonome",
                    "biotechnologie créative",
                    "philosophie technologique"
                ]
                
                base_theme = random.choice(base_themes)
                
                # Choix aléatoire de technique créative
                technique = random.choice([
                    "recursive",
                    "divergent", 
                    "constrained",
                    "memory_inspired"
                ])
                
                if technique == "recursive":
                    ideas = self.recursive_thinking(base_theme, depth=3)
                elif technique == "divergent":
                    ideas = self.divergent_thinking(base_theme, num_variations=4)
                elif technique == "constrained":
                    ideas = [self.constrained_creativity(base_theme)]
                else:  # memory_inspired
                    ideas = [self.memory_inspired_thinking()]
                
                # Sauvegarde des meilleures idées
                self._save_best_ideas(ideas)
                
                # Affichage
                print(f"💡 Brainstorming autonome: {len(ideas)} idées générées ({technique})")
                
                # Pause aléatoire avant prochaine session
                sleep_time = random.uniform(*self.exploration_interval)
                time.sleep(sleep_time)
                
            except Exception as e:
                print(f"❌ Erreur brainstorming autonome: {e}")
                time.sleep(30)  # Pause en cas d'erreur

    def start_autonomous_exploration(self):
        """Démarre l'exploration créative autonome"""
        if self.running:
            print("⚠️ Exploration autonome déjà en cours")
            return
        
        self.running = True
        self.stats["start_time"] = datetime.now()
        self.imagination_thread = threading.Thread(target=self.autonomous_brainstorming, daemon=True)
        self.imagination_thread.start()
        
        print("🚀 EXPLORATION CRÉATIVE AUTONOME DÉMARRÉE")
        print("   🧠 JARVIS explore maintenant de manière créative...")

    def stop_autonomous_exploration(self):
        """Arrête l'exploration créative autonome"""
        self.running = False
        if self.imagination_thread:
            self.imagination_thread.join(timeout=2)
        
        self.save_creative_ideas()
        print("🛑 Exploration créative autonome arrêtée")
        self.print_creative_stats()

    def _generate_with_deepseek(self, prompt: str) -> str:
        """Génère une réponse avec DeepSeek R1"""
        try:
            payload = {
                "model": "deepseek-r1",
                "messages": [
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 512,
                "temperature": 0.9,  # Haute créativité
                "stream": False
            }
            
            response = requests.post(
                self.deepseek_endpoint,
                json=payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"].strip()
            else:
                return f"Idée créative générée localement pour: {prompt[:50]}..."
                
        except Exception as e:
            # Fallback créatif local
            return f"💡 Exploration créative de: {prompt[:100]}... [Générée localement]"

    def _save_best_ideas(self, ideas: List[Dict[str, Any]]):
        """Sauvegarde les meilleures idées"""
        for idea in ideas:
            if isinstance(idea, dict) and "text" in idea:
                # Évaluation simple de qualité (longueur + mots-clés créatifs)
                text = idea.get("text", "")
                creative_keywords = ["innovant", "révolutionnaire", "créatif", "original", "unique", "surprenant"]
                
                quality_score = len(text) / 100  # Score basé sur longueur
                quality_score += sum(1 for keyword in creative_keywords if keyword in text.lower())
                
                if quality_score > 2.0:  # Seuil de qualité
                    self.stats["best_ideas"].append({
                        **idea,
                        "quality_score": quality_score
                    })
                    
                    # Garder seulement les 20 meilleures
                    self.stats["best_ideas"] = sorted(
                        self.stats["best_ideas"],
                        key=lambda x: x.get("quality_score", 0),
                        reverse=True
                    )[:20]

    def load_creative_ideas(self):
        """Charge les idées créatives depuis le disque"""
        try:
            if os.path.exists(self.ideas_storage):
                with open(self.ideas_storage, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.stats.update(data.get("stats", {}))
                print(f"💾 Idées créatives chargées: {len(self.stats.get('best_ideas', []))} idées")
        except Exception as e:
            print(f"⚠️ Erreur chargement idées: {e}")

    def save_creative_ideas(self):
        """Sauvegarde les idées créatives sur disque"""
        try:
            data = {
                "stats": self.stats,
                "last_save": datetime.now().isoformat()
            }
            
            with open(self.ideas_storage, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 Idées créatives sauvegardées: {len(self.stats.get('best_ideas', []))} idées")
        except Exception as e:
            print(f"❌ Erreur sauvegarde idées: {e}")

    def print_creative_stats(self):
        """Affiche les statistiques créatives"""
        if not self.stats.get("start_time"):
            return
        
        duration = datetime.now() - datetime.fromisoformat(self.stats["start_time"])
        
        print("\n🎨 STATISTIQUES CRÉATIVITÉ BOOST IMAGINATION")
        print("=" * 60)
        print(f"⏱️ Durée session: {duration}")
        print(f"💡 Total idées: {self.stats['total_ideas']}")
        print(f"🔗 Chaînages récursifs: {self.stats['recursive_chains']}")
        print(f"🌟 Explorations divergentes: {self.stats['divergent_explorations']}")
        print(f"🚀 Sessions autonomes: {self.stats['autonomous_sessions']}")
        print(f"⭐ Meilleures idées: {len(self.stats.get('best_ideas', []))}")
        print("=" * 60)

    def get_best_ideas(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Récupère les meilleures idées créatives"""
        return self.stats.get("best_ideas", [])[:limit]

    def interactive_creativity_session(self, base_idea: str) -> Dict[str, Any]:
        """Session créative interactive pour Jean-Luc Passave"""
        print(f"🎨 SESSION CRÉATIVE INTERACTIVE - Idée de base: {base_idea}")
        
        results = {
            "base_idea": base_idea,
            "timestamp": datetime.now().isoformat(),
            "techniques": {}
        }
        
        # 1. Chaînage récursif
        print("🔗 Génération chaînage récursif...")
        results["techniques"]["recursive"] = self.recursive_thinking(base_idea, depth=3)
        
        # 2. Exploration divergente
        print("🌟 Génération variations divergentes...")
        results["techniques"]["divergent"] = self.divergent_thinking(base_idea, num_variations=5)
        
        # 3. Créativité contrainte
        print("🎯 Application contraintes créatives...")
        results["techniques"]["constrained"] = [
            self.constrained_creativity(base_idea, style) 
            for style in random.sample(self.thinking_styles, 3)
        ]
        
        # 4. Inspiration mémoire
        print("💾 Génération inspirée par mémoire...")
        results["techniques"]["memory_inspired"] = [self.memory_inspired_thinking()]
        
        # Sauvegarde
        all_ideas = []
        for technique_results in results["techniques"].values():
            if isinstance(technique_results, list):
                all_ideas.extend(technique_results)
            else:
                all_ideas.append(technique_results)
        
        self._save_best_ideas(all_ideas)
        
        print(f"✅ Session créative terminée: {len(all_ideas)} idées générées")
        return results

# 🚀 FONCTION D'INITIALISATION POUR JARVIS
def init_boost_imagination(memory_system=None):
    """Initialise le module Boost Imagination pour JARVIS"""
    
    print("🎨 Initialisation BOOST IMAGINATION...")
    
    boost_imagination = BoostImagination(memory_system=memory_system)
    
    print("✅ BOOST IMAGINATION prêt")
    print("   🚀 Utilisez boost_imagination.start_autonomous_exploration() pour démarrer")
    print("   🎨 Utilisez boost_imagination.interactive_creativity_session(idée) pour session interactive")
    
    return boost_imagination
