#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📡 JARVIS CONNECTIVITY CONTROLS
==============================
Contrôles Wi-Fi, Bluetooth, AirDrop pour toutes les interfaces JARVIS
Boutons visibles partout pour transfert de fichiers vers l'agent
"""

import subprocess
import os
import json
from datetime import datetime
from typing import Dict, List, Any

class JarvisConnectivityManager:
    def __init__(self):
        self.wifi_status = self.get_wifi_status()
        self.bluetooth_status = self.get_bluetooth_status()
        self.airdrop_status = self.get_airdrop_status()
        
    def get_wifi_status(self) -> bool:
        """Vérifie le statut Wi-Fi"""
        try:
            result = subprocess.run(['networksetup', '-getairportpower', 'en0'], 
                                  capture_output=True, text=True)
            return "On" in result.stdout
        except:
            return False
    
    def get_bluetooth_status(self) -> bool:
        """Vérifie le statut Bluetooth"""
        try:
            result = subprocess.run(['system_profiler', 'SPBluetoothDataType'], 
                                  capture_output=True, text=True)
            return "State: On" in result.stdout
        except:
            return False
    
    def get_airdrop_status(self) -> bool:
        """Vérifie le statut AirDrop"""
        try:
            # AirDrop est généralement activé si Wi-Fi et Bluetooth sont actifs
            return self.wifi_status and self.bluetooth_status
        except:
            return False
    
    def toggle_wifi(self) -> Dict[str, Any]:
        """Active/Désactive Wi-Fi"""
        try:
            current_status = self.get_wifi_status()
            new_status = "off" if current_status else "on"
            
            result = subprocess.run(['networksetup', '-setairportpower', 'en0', new_status], 
                                  capture_output=True, text=True)
            
            self.wifi_status = not current_status
            
            return {
                "success": True,
                "status": self.wifi_status,
                "message": f"Wi-Fi {'activé' if self.wifi_status else 'désactivé'}"
            }
        except Exception as e:
            return {
                "success": False,
                "status": self.wifi_status,
                "message": f"Erreur Wi-Fi: {e}"
            }
    
    def toggle_bluetooth(self) -> Dict[str, Any]:
        """Active/Désactive Bluetooth"""
        try:
            current_status = self.get_bluetooth_status()
            command = "off" if current_status else "on"
            
            # Utiliser blueutil si disponible, sinon commande système
            try:
                result = subprocess.run(['blueutil', f'--power', command], 
                                      capture_output=True, text=True)
            except:
                # Fallback vers commande système
                script = f'tell application "System Preferences" to set Bluetooth to {command}'
                result = subprocess.run(['osascript', '-e', script], 
                                      capture_output=True, text=True)
            
            self.bluetooth_status = not current_status
            
            return {
                "success": True,
                "status": self.bluetooth_status,
                "message": f"Bluetooth {'activé' if self.bluetooth_status else 'désactivé'}"
            }
        except Exception as e:
            return {
                "success": False,
                "status": self.bluetooth_status,
                "message": f"Erreur Bluetooth: {e}"
            }
    
    def open_airdrop(self) -> Dict[str, Any]:
        """Ouvre AirDrop pour transfert de fichiers"""
        try:
            # Ouvrir AirDrop dans Finder
            subprocess.run(['open', '-a', 'Finder', '/System/Library/CoreServices/Finder.app/Contents/Applications/AirDrop.app'], 
                          capture_output=True)
            
            return {
                "success": True,
                "message": "AirDrop ouvert - Prêt pour transfert de fichiers vers JARVIS"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Erreur AirDrop: {e}"
            }
    
    def get_status_summary(self) -> Dict[str, Any]:
        """Retourne le résumé des statuts"""
        return {
            "wifi": {
                "status": self.get_wifi_status(),
                "icon": "📶" if self.get_wifi_status() else "📵"
            },
            "bluetooth": {
                "status": self.get_bluetooth_status(),
                "icon": "🔵" if self.get_bluetooth_status() else "⚫"
            },
            "airdrop": {
                "status": self.get_airdrop_status(),
                "icon": "📡" if self.get_airdrop_status() else "📴"
            }
        }

# Instance globale
connectivity_manager = JarvisConnectivityManager()

def create_connectivity_buttons_html() -> str:
    """Crée les boutons de connectivité en HTML"""
    status = connectivity_manager.get_status_summary()
    
    return f"""
    <div style="position: fixed; top: 10px; right: 10px; z-index: 9999; 
                background: rgba(0,0,0,0.8); padding: 10px; border-radius: 15px;
                display: flex; gap: 10px; backdrop-filter: blur(10px);">
        
        <!-- Bouton Wi-Fi -->
        <button onclick="toggleWifi()" 
                style="background: {'#4CAF50' if status['wifi']['status'] else '#f44336'}; 
                       color: white; border: none; padding: 8px 12px; 
                       border-radius: 10px; cursor: pointer; font-size: 14px;
                       transition: all 0.3s ease;">
            {status['wifi']['icon']} Wi-Fi
        </button>
        
        <!-- Bouton Bluetooth -->
        <button onclick="toggleBluetooth()" 
                style="background: {'#2196F3' if status['bluetooth']['status'] else '#f44336'}; 
                       color: white; border: none; padding: 8px 12px; 
                       border-radius: 10px; cursor: pointer; font-size: 14px;
                       transition: all 0.3s ease;">
            {status['bluetooth']['icon']} Bluetooth
        </button>
        
        <!-- Bouton AirDrop -->
        <button onclick="openAirDrop()" 
                style="background: {'#FF9800' if status['airdrop']['status'] else '#757575'}; 
                       color: white; border: none; padding: 8px 12px; 
                       border-radius: 10px; cursor: pointer; font-size: 14px;
                       transition: all 0.3s ease;">
            {status['airdrop']['icon']} AirDrop
        </button>
        
        <!-- Bouton Transfert Fichiers -->
        <button onclick="openFileTransfer()" 
                style="background: linear-gradient(45deg, #9C27B0, #673AB7); 
                       color: white; border: none; padding: 8px 12px; 
                       border-radius: 10px; cursor: pointer; font-size: 14px;
                       transition: all 0.3s ease;">
            📁 Transfert → JARVIS
        </button>
    </div>
    
    <script>
        function toggleWifi() {{
            fetch('/api/connectivity/wifi/toggle', {{method: 'POST'}})
                .then(response => response.json())
                .then(data => {{
                    alert(data.message);
                    location.reload();
                }})
                .catch(error => alert('Erreur Wi-Fi: ' + error));
        }}
        
        function toggleBluetooth() {{
            fetch('/api/connectivity/bluetooth/toggle', {{method: 'POST'}})
                .then(response => response.json())
                .then(data => {{
                    alert(data.message);
                    location.reload();
                }})
                .catch(error => alert('Erreur Bluetooth: ' + error));
        }}
        
        function openAirDrop() {{
            fetch('/api/connectivity/airdrop/open', {{method: 'POST'}})
                .then(response => response.json())
                .then(data => alert(data.message))
                .catch(error => alert('Erreur AirDrop: ' + error));
        }}
        
        function openFileTransfer() {{
            // Ouvrir une interface de transfert de fichiers
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = '*/*';
            
            input.onchange = function(event) {{
                const files = event.target.files;
                if (files.length > 0) {{
                    const formData = new FormData();
                    for (let i = 0; i < files.length; i++) {{
                        formData.append('files', files[i]);
                    }}
                    
                    fetch('/api/jarvis/upload', {{
                        method: 'POST',
                        body: formData
                    }})
                    .then(response => response.json())
                    .then(data => alert('Fichiers transférés vers JARVIS: ' + data.message))
                    .catch(error => alert('Erreur transfert: ' + error));
                }}
            }};
            
            input.click();
        }}
        
        // Actualiser les statuts toutes les 30 secondes
        setInterval(() => {{
            fetch('/api/connectivity/status')
                .then(response => response.json())
                .then(data => {{
                    // Mettre à jour les icônes des boutons
                    console.log('Statuts connectivité mis à jour:', data);
                }})
                .catch(error => console.log('Erreur actualisation:', error));
        }}, 30000);
    </script>
    """

def create_connectivity_gradio_components():
    """Crée les composants Gradio pour la connectivité"""
    import gradio as gr
    
    def toggle_wifi_gradio():
        result = connectivity_manager.toggle_wifi()
        return result["message"]
    
    def toggle_bluetooth_gradio():
        result = connectivity_manager.toggle_bluetooth()
        return result["message"]
    
    def open_airdrop_gradio():
        result = connectivity_manager.open_airdrop()
        return result["message"]
    
    def get_status_gradio():
        status = connectivity_manager.get_status_summary()
        return f"""
📡 **STATUT CONNECTIVITÉ JARVIS**

📶 **Wi-Fi**: {status['wifi']['icon']} {'Activé' if status['wifi']['status'] else 'Désactivé'}
🔵 **Bluetooth**: {status['bluetooth']['icon']} {'Activé' if status['bluetooth']['status'] else 'Désactivé'}  
📡 **AirDrop**: {status['airdrop']['icon']} {'Disponible' if status['airdrop']['status'] else 'Indisponible'}

💡 **Utilisez ces boutons pour transférer des fichiers vers JARVIS**
        """
    
    with gr.Row():
        wifi_btn = gr.Button("📶 Wi-Fi", variant="primary", size="sm")
        bluetooth_btn = gr.Button("🔵 Bluetooth", variant="primary", size="sm")
        airdrop_btn = gr.Button("📡 AirDrop", variant="primary", size="sm")
        status_btn = gr.Button("📊 Statut", variant="secondary", size="sm")
    
    status_output = gr.HTML(value=get_status_gradio())
    
    wifi_btn.click(toggle_wifi_gradio, outputs=[status_output])
    bluetooth_btn.click(toggle_bluetooth_gradio, outputs=[status_output])
    airdrop_btn.click(open_airdrop_gradio, outputs=[status_output])
    status_btn.click(get_status_gradio, outputs=[status_output])
    
    return status_output

def get_connectivity_manager():
    """Retourne l'instance du gestionnaire de connectivité"""
    return connectivity_manager

if __name__ == "__main__":
    print("📡 TEST JARVIS CONNECTIVITY CONTROLS")
    print("=" * 50)
    
    manager = get_connectivity_manager()
    
    # Test des statuts
    status = manager.get_status_summary()
    print(f"📶 Wi-Fi: {status['wifi']['icon']} {status['wifi']['status']}")
    print(f"🔵 Bluetooth: {status['bluetooth']['icon']} {status['bluetooth']['status']}")
    print(f"📡 AirDrop: {status['airdrop']['icon']} {status['airdrop']['status']}")
    
    # Test HTML
    html = create_connectivity_buttons_html()
    print(f"\n📝 HTML généré: {len(html)} caractères")
    
    print("\n✅ Module de connectivité prêt pour intégration")
