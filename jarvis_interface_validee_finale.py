#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS INTERFACE VALIDÉE FINALE - JEAN-LUC PASSAVE
Interface principale validée avec agent connecté, navigation et audio complet
"""

import gradio as gr
import requests
import datetime

# Configuration
SERVER_URL = "http://localhost:8000/v1/chat/completions"

def test_deepseek_connection():
    """Test de connexion DeepSeek R1 8B avec diagnostic complet"""
    try:
        # Test direct de l'API
        test_payload = {
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 10,
            "temperature": 0.1
        }
        response = requests.post(SERVER_URL, json=test_payload, timeout=10)
        
        if response.status_code == 200:
            return True, "✅ DeepSeek R1 8B complètement opérationnel"
        else:
            return False, f"❌ Erreur API: {response.status_code}"
            
    except requests.exceptions.ConnectionError:
        return False, "❌ DeepSeek R1 8B non accessible - Serveur arrêté"
    except requests.exceptions.Timeout:
        return False, "⏱️ DeepSeek R1 8B timeout - Serveur lent"
    except Exception as e:
        return False, f"❌ Erreur DeepSeek: {str(e)}"

def maintain_agent_connection():
    """Système de maintien de connexion agent - JEAN-LUC PASSAVE"""
    try:
        # Test de connexion périodique
        connected, status = test_deepseek_connection()
        if not connected:
            print("🔄 SYSTÈME ANTI-DÉCONNEXION: Tentative de redémarrage agent DeepSeek...")
            # Tentative de reconnexion automatique
            import subprocess
            try:
                # Redémarrage du serveur DeepSeek si possible
                subprocess.run(["python", "redemarrer_agent_deepseek.py"], timeout=30)
                return False, "🔄 Reconnexion agent en cours - Redémarrage automatique"
            except:
                return False, "❌ Redémarrage automatique échoué - Intervention manuelle requise"
        return True, status
    except:
        return False, "❌ Système de maintien connexion défaillant"

def process_jarvis_message(message, history):
    """Traite un message avec JARVIS RÉEL et extraction des pensées + système anti-déconnexion"""
    if not message.strip():
        return history, "", create_status_display("Aucun message à traiter")

    # SYSTÈME ANTI-DÉCONNEXION - Vérification avant traitement
    connected, connection_status = maintain_agent_connection()
    if not connected:
        return history, "", create_status_display(connection_status, "Agent déconnecté", "Tentative de reconnexion automatique en cours...")

    try:
        # Appel à DeepSeek R1 8B RÉEL
        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": "Tu es JARVIS, l'assistant IA personnel de Jean-Luc Passave. Tu dois TOUJOURS montrer tes pensées en français dans les balises <think>...</think> au début de chaque réponse, puis répondre de manière naturelle et utile en français."
                },
                {"role": "user", "content": message}
            ],
            "max_tokens": 500,
            "temperature": 0.7,
            "stream": False
        }

        response = requests.post(SERVER_URL, json=payload, timeout=45)

        if response.status_code == 200:
            result = response.json()
            full_response = result['choices'][0]['message']['content']
            
            # Extraire les pensées DeepSeek R1
            thoughts = ""
            clean_response = full_response
            
            if "<think>" in full_response and "</think>" in full_response:
                start = full_response.find("<think>") + 7
                end = full_response.find("</think>")
                thoughts = full_response[start:end].strip()
                clean_response = full_response.replace(f"<think>{thoughts}</think>", "").strip()
                if not clean_response:
                    clean_response = "Je réfléchis à votre demande..."
            else:
                # Si pas de pensées explicites, créer des pensées basées sur la réponse
                thoughts = f"💭 Analyse de la demande de Jean-Luc: '{message[:50]}...' - Préparation d'une réponse adaptée et personnalisée. [⏰ {datetime.datetime.now().strftime('%H:%M:%S')}]"
                clean_response = full_response
            
            # Nettoyer la réponse
            clean_response = clean_response.replace("<｜end▁of▁sentence｜>", "").strip()
            
            # Ajouter à l'historique
            history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
            history.append({"role": "assistant", "content": f"🤖 JARVIS: {clean_response}"})
            
            # Créer le statut avec audio
            status_html = create_status_display(f"✅ Réponse DeepSeek R1 8B: '{message[:30]}...'", clean_response, thoughts)
            
            return history, "", status_html
        else:
            error_msg = f"❌ Erreur serveur DeepSeek: {response.status_code}"
            history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
            history.append({"role": "assistant", "content": f"🤖 JARVIS: {error_msg}"})
            return history, "", create_status_display(error_msg)

    except requests.exceptions.ConnectionError:
        error_msg = "❌ Impossible de se connecter au serveur DeepSeek R1 8B (localhost:8000)"
        history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
        history.append({"role": "assistant", "content": f"🤖 JARVIS: {error_msg}"})
        return history, "", create_status_display(error_msg)
    except Exception as e:
        error_msg = f"❌ Erreur DeepSeek R1 8B: {str(e)}"
        history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
        history.append({"role": "assistant", "content": f"🤖 JARVIS: {error_msg}"})
        return history, "", create_status_display(error_msg)

def create_status_display(status_text, response="", thoughts=""):
    """Crée l'affichage avec boutons audio pour réponses et pensées"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    
    # Nettoyer les textes pour JavaScript
    clean_response = response.replace("'", "\\'").replace('"', '\\"').replace('\n', ' ')
    clean_thoughts = thoughts.replace("'", "\\'").replace('"', '\\"').replace('\n', ' ')
    
    return f"""
    <div style="background: linear-gradient(135deg, #f3e5f5, #e1bee7); padding: 20px; border-radius: 15px; border-left: 6px solid #9C27B0; margin: 10px 0;">
        <div style="text-align: center; margin-bottom: 15px;">
            <h3 style="color: #9C27B0; margin: 0; font-size: 1.4em;">🔊 JARVIS VALIDÉ - AGENT CONNECTÉ</h3>
            <p style="margin: 5px 0; color: #666;">Interface Principale Validée - Jean-Luc Passave</p>
        </div>
        
        <div style="background: #e8f5e8; padding: 12px; border-radius: 8px; margin: 10px 0; border-left: 3px solid #27ae60;">
            <strong style="color: #27ae60; font-size: 1.1em;">📊 Statut [{current_time}]:</strong>
            <div style="margin-top: 5px;">{status_text}</div>
        </div>
        
        {f'''
        <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #2196f3;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #2196f3; font-size: 1.2em;">🤖 Réponse JARVIS:</strong>
                <button onclick="speakText('{clean_response}')" style="background: linear-gradient(45deg, #2196f3, #1976d2); color: white; border: none; padding: 12px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 1.1em; box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);">🔊 ÉCOUTER RÉPONSE</button>
            </div>
            <div style="font-size: 1.1em; line-height: 1.5; background: white; padding: 10px; border-radius: 5px;">
                {response}
            </div>
        </div>
        ''' if response else ''}
        
        {f'''
        <div style="background: #fff0f5; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #e91e63;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #e91e63; font-size: 1.2em;">🧠 Pensées JARVIS:</strong>
                <button onclick="speakText('Pensée de JARVIS: {clean_thoughts}')" style="background: linear-gradient(45deg, #e91e63, #c2185b); color: white; border: none; padding: 12px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 1.1em; box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);">🔊 ÉCOUTER PENSÉES</button>
            </div>
            <div style="font-size: 1.1em; line-height: 1.5; font-style: italic; background: white; padding: 10px; border-radius: 5px;">
                {thoughts}
            </div>
        </div>
        ''' if thoughts else ''}
        
        <div style="background: #f5f5f5; padding: 12px; border-radius: 8px; margin: 10px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <strong style="color: #666; font-size: 1em;">🔗 Connexion DeepSeek R1 8B:</strong>
                <button onclick="speakText('Connexion DeepSeek R1 8B active sur localhost 8000. Système opérationnel.')" style="background: #666; color: white; border: none; padding: 8px 15px; border-radius: 20px; cursor: pointer; font-size: 0.9em;">🔊 STATUT</button>
            </div>
            <div style="color: #27ae60; font-weight: bold; margin-top: 5px;">✅ DeepSeek R1 8B localhost:8000 opérationnel</div>
        </div>
    </div>
    
    <script>
    function speakText(text) {{
        if ('speechSynthesis' in window) {{
            // Arrêter toute lecture en cours
            window.speechSynthesis.cancel();
            
            // RÉCUPÉRER LES PARAMÈTRES SAUVEGARDÉS - JEAN-LUC PASSAVE
            const savedVolume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
            const savedSpeed = parseFloat(localStorage.getItem('jarvis_speed') || '0.85');
            const savedPitch = parseFloat(localStorage.getItem('jarvis_pitch') || '0.8');
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = savedSpeed;
            utterance.pitch = savedPitch;
            utterance.volume = savedVolume;
            
            console.log('🔊 JARVIS Audio - Volume:', savedVolume, 'Vitesse:', savedSpeed, 'Tonalité:', savedPitch);
            
            // Chercher une voix française
            const voices = window.speechSynthesis.getVoices();
            const frenchVoice = voices.find(voice => 
                voice.lang.includes('fr') || 
                voice.name.toLowerCase().includes('french') ||
                voice.name.toLowerCase().includes('thomas') ||
                voice.name.toLowerCase().includes('male')
            );
            
            if (frenchVoice) {{
                utterance.voice = frenchVoice;
            }}
            
            // Événements de lecture
            utterance.onstart = function() {{
                console.log('🔊 JARVIS commence à parler:', text.substring(0, 50) + '...');
            }};
            
            utterance.onend = function() {{
                console.log('✅ JARVIS a terminé de parler');
            }};
            
            utterance.onerror = function(event) {{
                console.error('❌ Erreur audio JARVIS:', event.error);
            }};
            
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée par ce navigateur');
        }}
    }}
    
    // NAVIGATION ALLER-RETOUR - JEAN-LUC PASSAVE
    function goToHome() {{
        // Si nous sommes déjà sur la page d'accueil, recharger la page
        if (window.location.href.includes('localhost:7866')) {{
            window.location.reload();
        }} else {{
            localStorage.setItem('jarvis_last_url', window.location.href);
            window.location.href = 'http://localhost:7866';
        }}
    }}

    function goToLastTab() {{
        const lastUrl = localStorage.getItem('jarvis_last_url');
        if (lastUrl && lastUrl !== window.location.href) {{
            window.location.href = lastUrl;
        }} else {{
            window.location.href = 'http://localhost:8111';
        }}
    }}
    
    // Raccourcis clavier pour l'audio
    document.addEventListener('keydown', function(event) {{
        // Ctrl + R : Écouter dernière réponse
        if (event.ctrlKey && event.key === 'r') {{
            event.preventDefault();
            const chatbot = document.querySelector('[data-testid="chatbot"]');
            if (chatbot) {{
                const messages = chatbot.querySelectorAll('.message');
                if (messages.length > 0) {{
                    const lastMessage = messages[messages.length - 1];
                    const text = lastMessage.textContent || lastMessage.innerText;
                    if (text.includes('JARVIS:')) {{
                        const jarvisText = text.split('JARVIS:')[1]?.trim();
                        if (jarvisText) {{
                            speakText(jarvisText);
                        }}
                    }}
                }}
            }}
            console.log('🔊 Raccourci Ctrl+R: Lecture dernière réponse');
        }}
        
        // Ctrl + S : Arrêter la lecture
        if (event.ctrlKey && event.key === 's') {{
            event.preventDefault();
            window.speechSynthesis.cancel();
            console.log('⏹️ Raccourci Ctrl+S: Arrêt audio');
        }}
        
        // Ctrl + A : Test audio
        if (event.ctrlKey && event.key === 'a') {{
            event.preventDefault();
            speakText('Test audio rapide de JARVIS. Bonjour Jean-Luc, l\\'interface validée fonctionne parfaitement.');
            console.log('🎧 Raccourci Ctrl+A: Test audio');
        }}
    }});
    
    // Message de bienvenue audio
    window.addEventListener('load', function() {{
        console.log('🔊 JARVIS Interface Validée initialisée');
        console.log('🎹 Raccourcis: Ctrl+R (réponse), Ctrl+S (stop), Ctrl+A (test)');
        
        setTimeout(() => {{
            if (localStorage.getItem('jarvis_welcome_played') !== 'true') {{
                speakText('Bonjour Jean-Luc. Interface JARVIS validée et agent DeepSeek R1 8B connecté. Tous les systèmes sont opérationnels.');
                localStorage.setItem('jarvis_welcome_played', 'true');
            }}
        }}, 2000);
    }});
    </script>
    """

if __name__ == "__main__":
    print("🔊 JARVIS INTERFACE VALIDÉE FINALE - JEAN-LUC PASSAVE")
    print("=" * 70)
    print("✅ Interface principale validée avec agent connecté")
    print("🔧 Configuration: DeepSeek R1 8B via localhost:8000")
    print("🧠 Extraction automatique des pensées réelles")
    print("🔊 Synthèse vocale navigateur complète")
    print("🏠 Navigation: Boutons accueil + dernier onglet")
    print("🎹 Raccourcis clavier: Ctrl+R, Ctrl+S, Ctrl+A")
    print("🌐 Interface disponible sur localhost:7866")
    print("=" * 70)
    
    # Test de connexion au démarrage
    connected, status_msg = test_deepseek_connection()
    print(f"📊 Statut agent: {status_msg}")
    
    # Interface JARVIS complète validée
    def create_jarvis_interface_validee():
        """Interface JARVIS validée finale avec navigation et agent connecté"""

        with gr.Blocks(
            title="🔊 JARVIS Interface Validée - Agent Connecté",
            theme=gr.themes.Soft(),
            css="""
            .audio-btn-main {
                background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
                color: white !important;
                font-weight: bold !important;
                font-size: 1.4em !important;
                padding: 18px 35px !important;
                border-radius: 25px !important;
                box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4) !important;
                transition: all 0.3s ease !important;
            }
            .audio-btn-main:hover {
                background: linear-gradient(45deg, #c0392b, #a93226) !important;
                transform: translateY(-3px) !important;
                box-shadow: 0 12px 25px rgba(231, 76, 60, 0.5) !important;
            }
            """
        ) as interface:

            # Header avec Navigation
            gr.HTML("""
            <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
                <!-- BOUTONS NAVIGATION ALLER-RETOUR -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <button onclick="goToHome()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s;" title="Actualiser la page d'accueil">
                        🏠 ACCUEIL
                    </button>
                    <div style="text-align: center;">
                        <h1 style="margin: 0; font-size: 2.2em;">🔊 JARVIS - Interface Validée</h1>
                    </div>
                    <button onclick="goToLastTab()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                        ↩️ DERNIER ONGLET
                    </button>
                </div>

                <p style="margin: 10px 0 0 0; font-size: 1.2em;">Interface Principale Validée - Jean-Luc Passave</p>
                <div style="margin: 15px 0;">
                    <span style="display: inline-block; width: 15px; height: 15px; background: #4CAF50; border-radius: 50%; margin-right: 10px; animation: pulse 2s infinite;"></span>
                    <span style="font-size: 1.1em; font-weight: bold;">DeepSeek R1 8B Connecté + Navigation Intégrée</span>
                </div>
            </div>

            <style>
            @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
            button:hover { background: rgba(255,255,255,0.3) !important; transform: translateY(-2px); }
            </style>
            """)

            with gr.Row():
                with gr.Column(scale=3):
                    # Zone de conversation AGRANDIE
                    chat = gr.Chatbot(
                        value=[],
                        height=600,
                        label="💬 Conversation avec JARVIS (Agent Connecté)",
                        show_copy_button=True,
                        avatar_images=("👨‍💻", "🤖"),
                        type="messages"
                    )

                    # Zone de saisie AGRANDIE
                    with gr.Row():
                        user_input = gr.Textbox(
                            value="",
                            label="💬 Votre message à JARVIS",
                            scale=4,
                            lines=3,
                            placeholder="Tapez votre message ici..."
                        )
                        send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)

                    # BOUTONS PRINCIPAUX SIMPLIFIÉS
                    with gr.Row():
                        clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
                        status_btn = gr.Button("📊 STATUT", variant="secondary")
                        config_btn = gr.Button("⚙️ CONFIGURATION SYSTÈME", variant="primary")

                with gr.Column(scale=1):
                    # Zone de statut
                    status_display = gr.HTML(create_status_display("Interface JARVIS Validée prête - Agent DeepSeek R1 8B connecté"))

                    # Applications JARVIS
                    gr.HTML("""
                    <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #2196f3;">
                        <h4 style="color: #2196f3; margin: 0 0 10px 0;">🚀 Applications JARVIS</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.9em;">
                            <button onclick="window.open('http://localhost:8111', '_blank')" style="background: #e74c3c; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🔊 Audio Complet</button>
                            <button onclick="window.open('http://localhost:8260', '_blank')" style="background: #4caf50; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">📊 Surveillance</button>
                            <button onclick="window.open('http://localhost:8115', '_blank')" style="background: #673ab7; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🧠 Mémoire</button>
                            <button onclick="window.open('http://localhost:8116', '_blank')" style="background: #2196f3; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">📝 Éditeur</button>
                            <button onclick="window.open('http://localhost:8117', '_blank')" style="background: #9c27b0; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🧠 Pensées Réelles</button>
                            <button onclick="window.open('http://localhost:8118', '_blank')" style="background: #ff5722; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🔍 Recherche Sécurisée</button>
                            <button onclick="window.open('http://localhost:8119', '_blank')" style="background: #795548; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🎨 Générateur Multimédia</button>
                            <button onclick="window.open('http://localhost:8120', '_blank')" style="background: #607d8b; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🔒 Sécurité</button>
                        </div>
                    </div>

                    <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #2196f3;">
                        <h4 style="color: #2196f3; margin: 0 0 10px 0;">🔧 Outils Avancés</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.9em;">
                            <button onclick="window.open('http://localhost:8121', '_blank')" style="background: #3f51b5; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🔍 Analyseur Code</button>
                            <button onclick="window.open('http://localhost:8122', '_blank')" style="background: #009688; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🔧 Correcteur Auto</button>
                            <button onclick="window.open('http://localhost:8123', '_blank')" style="background: #8bc34a; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">💾 Sauvegarde</button>
                            <button onclick="window.open('http://localhost:8124', '_blank')" style="background: #ff9800; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">📈 Monitoring</button>
                        </div>
                    </div>

                    <div style="background: #fff3e0; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #ff9800;">
                        <h4 style="color: #ff9800; margin: 0 0 10px 0;">⚙️ Configuration Système</h4>
                        <div style="font-size: 0.9em; line-height: 1.4;">
                            <div style="margin: 5px 0;">🔊 <strong>Contrôles Audio</strong> - Volume, vitesse, tonalité</div>
                            <div style="margin: 5px 0;">🎧 <strong>Test Audio</strong> - Vérification haut-parleur</div>
                            <div style="margin: 5px 0;">⏹️ <strong>Arrêt Audio</strong> - Contrôle d'urgence</div>
                            <div style="margin: 5px 0;">🎹 <strong>Raccourcis</strong> - Ctrl+R, Ctrl+S, Ctrl+A</div>
                            <div style="margin: 5px 0;">📊 <strong>Diagnostic</strong> - Statut connexions</div>
                        </div>
                    </div>

                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #4caf50;">
                        <h4 style="color: #4caf50; margin: 0 0 10px 0;">✅ Interface Validée</h4>
                        <div style="font-size: 0.9em; line-height: 1.4;">
                            <div style="margin: 5px 0;">✅ <strong>Agent Connecté</strong> - DeepSeek R1 8B opérationnel</div>
                            <div style="margin: 5px 0;">✅ <strong>Navigation</strong> - Boutons accueil + dernier onglet</div>
                            <div style="margin: 5px 0;">✅ <strong>Pensées Réelles</strong> - Extraction automatique</div>
                            <div style="margin: 5px 0;">✅ <strong>Interface Simplifiée</strong> - Audio dans configuration</div>
                        </div>
                    </div>
                    """)

            # Fonctions
            def clear_chat_complete():
                return [], create_status_display("Conversation effacée - Interface validée prête pour une nouvelle discussion")

            def show_status():
                connected, status_msg = test_deepseek_connection()
                if connected:
                    return create_status_display(status_msg, "Système JARVIS validé opérationnel", "Tous les modules sont connectés et fonctionnels")
                else:
                    return create_status_display(status_msg, "Tentative de reconnexion en cours...", "Diagnostic de connexion DeepSeek R1 8B")

            # Connexions
            send_btn.click(
                fn=process_jarvis_message,
                inputs=[user_input, chat],
                outputs=[chat, user_input, status_display]
            )

            user_input.submit(
                fn=process_jarvis_message,
                inputs=[user_input, chat],
                outputs=[chat, user_input, status_display]
            )

            config_btn.click(
                fn=lambda: create_status_display("Ouverture Configuration Système - Interface dédiée aux contrôles audio"),
                outputs=[status_display],
                js="() => window.open('http://localhost:8114', '_blank')"
            )

            clear_btn.click(
                fn=clear_chat_complete,
                outputs=[chat, status_display]
            )

            status_btn.click(
                fn=show_status,
                outputs=[status_display]
            )



        return interface

    interface = create_jarvis_interface_validee()
    interface.launch(
        server_name="localhost",
        server_port=7866,
        share=False,
        debug=True,
        show_error=True
    )
