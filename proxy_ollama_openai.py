#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PROXY OLLAMA -> OPENAI - JEAN-LUC PASSAVE
Convertit l'API Ollama en format OpenAI pour JARVIS
"""

from flask import Flask, request, jsonify
import requests
import time
import threading

app = Flask(__name__)

def test_ollama():
    """Test si Ollama fonctionne"""
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.2",
                "prompt": "Test",
                "stream": False
            },
            timeout=10
        )
        return response.status_code == 200
    except:
        return False

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint compatible OpenAI"""
    try:
        data = request.json
        messages = data.get('messages', [])
        max_tokens = data.get('max_tokens', 100)
        
        # Construire le prompt à partir des messages
        prompt = ""
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            if role == 'user':
                prompt += f"Utilisateur: {content}\n"
            elif role == 'assistant':
                prompt += f"Assistant: {content}\n"
        
        prompt += "Assistant: "
        
        # Appel à Ollama
        ollama_response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.2",
                "prompt": prompt,
                "stream": False,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": 0.7
                }
            },
            timeout=30
        )
        
        if ollama_response.status_code == 200:
            ollama_data = ollama_response.json()
            response_text = ollama_data.get('response', 'Désolé, je ne peux pas répondre.')
            
            # Format OpenAI
            return jsonify({
                "id": f"chatcmpl-ollama-{int(time.time())}",
                "object": "chat.completion",
                "created": int(time.time()),
                "model": "llama3.2",
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_text
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": len(prompt.split()),
                    "completion_tokens": len(response_text.split()),
                    "total_tokens": len(prompt.split()) + len(response_text.split())
                }
            })
        else:
            return jsonify({"error": "Erreur Ollama"}), 500
            
    except Exception as e:
        return jsonify({"error": f"Erreur proxy: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de santé"""
    ollama_ok = test_ollama()
    return jsonify({
        "status": "ok" if ollama_ok else "error",
        "ollama": "connected" if ollama_ok else "disconnected"
    })

def main():
    """Fonction principale"""
    print("🔄 PROXY OLLAMA -> OPENAI - JEAN-LUC PASSAVE")
    print("=" * 50)
    
    # Test Ollama
    if test_ollama():
        print("✅ Ollama connecté")
    else:
        print("❌ Ollama non accessible")
        print("🔧 Démarrage Ollama...")
        import subprocess
        subprocess.Popen(['ollama', 'serve'])
        time.sleep(5)
        
        if test_ollama():
            print("✅ Ollama démarré")
        else:
            print("❌ Impossible de démarrer Ollama")
            return False
    
    print("🌐 Proxy démarré sur http://localhost:8000")
    print("🔗 Compatible OpenAI API")
    print("🤖 Modèle: llama3.2 via Ollama")
    
    app.run(host='localhost', port=8000, debug=False)
    return True

if __name__ == "__main__":
    main()
