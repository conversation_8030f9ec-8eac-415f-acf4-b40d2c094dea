# FICHIER 2 - Extrait jarvis_architecture_multi_fenetres.py
# PROBLÈME : Erreur mémoire thermique "ThermalMemory object is not subscriptable"

def generate_intelligent_response(message, history, thermal_memory, goap_planner, cognitive_reasoning):
    """Génère une réponse intelligente avec GOAP et raisonnement cognitif"""
    
    try:
        # ... code précédent ...
        
        # PROBLÈME ICI : Contexte mémoire thermique STRUCTURÉE - CORRIGÉ DÉFINITIF JEAN-LUC PASSAVE
        context = ""
        try:
            if thermal_memory:
                # Méthode sécurisée d'accès à la mémoire thermique
                if hasattr(thermal_memory, 'get_recent_memories'):
                    recent_memories = thermal_memory.get_recent_memories(5)
                    context = "\n".join([
                        f"Souvenir: {mem.get('content', '')[:100]}..."
                        for mem in recent_memories if isinstance(mem, dict)
                    ])
                elif hasattr(thermal_memory, 'memory') and isinstance(thermal_memory.memory, dict):
                    # ERREUR ICI : thermal_memory.memory.values() peut ne pas être subscriptable
                    recent_conversations = list(thermal_memory.memory.values())[-5:]
                    context = "\n".join([
                        f"Souvenir: {conv.get('user_message', '')} -> {conv.get('agent_response', '')[:100]}..."
                        for conv in recent_conversations if isinstance(conv, dict)
                    ])
                elif hasattr(thermal_memory, 'episodes') and isinstance(thermal_memory.episodes, list):
                    recent_episodes = thermal_memory.episodes[-5:]
                    context = "\n".join([
                        f"Épisode: {ep.get('description', '')[:100]}..."
                        for ep in recent_episodes if isinstance(ep, dict)
                    ])
                else:
                    context = "Mémoire thermique active mais format non reconnu"
            else:
                context = "Mémoire thermique non initialisée"
        except Exception as e:
            print(f"✅ Erreur mémoire thermique gérée proprement: {e}")
            context = "Mémoire thermique en cours d'initialisation..."

        # ... reste du code ...

# CLASSE THERMAL MEMORY (approximative basée sur les erreurs)
class ThermalMemory:
    """Classe mémoire thermique qui cause l'erreur"""
    
    def __init__(self):
        # PROBLÈME : Structure interne inconnue qui cause "not subscriptable"
        self.memory = {}  # Ou autre structure
        self.episodes = []
        self.neuron_count = 0
    
    # MÉTHODES MANQUANTES qui causent les erreurs d'accès
    def get_recent_memories(self, count):
        """Méthode qui devrait exister mais n'existe peut-être pas"""
        pass
    
    def add_memory(self, content):
        """Ajouter une mémoire"""
        pass

# LOGS D'ERREUR OBSERVÉS :
"""
⚠️ Erreur intégration mémoire thermique: 'ThermalMemory' object is not subscriptable
✅ Erreur mémoire thermique gérée proprement: 'ThermalMemory' object is not subscriptable
"""

# PROBLÈMES IDENTIFIÉS :
# 1. thermal_memory.memory.values() peut retourner un objet non-subscriptable
# 2. L'accès [-5:] sur list(thermal_memory.memory.values()) échoue
# 3. La structure interne de ThermalMemory n'est pas compatible avec les accès
# 4. Les hasattr() ne garantissent pas que les attributs sont utilisables
# 5. Même avec try/catch, l'erreur persiste et pollue les logs

# QUESTIONS POUR CHATGPT :
# 1. Comment accéder de manière sûre à thermal_memory sans "not subscriptable" ?
# 2. Comment détecter le vrai type de thermal_memory.memory ?
# 3. Faut-il refactorer complètement l'accès à la mémoire thermique ?
# 4. Comment gérer les différents formats possibles de mémoire ?
