#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎨 JARVIS CREATIVITY ENGINE - MOTEUR DE CRÉATIVITÉ AVANCÉ
Jean-Luc <PERSON>ave - 2025
Génère des connexions inattendues entre domaines pour booster l'intelligence créative
Optimisé pour Apple Silicon M1/M2/M3/M4 et futures générations
Intégration complète avec mémoire thermique JARVIS
AUCUNE SIMULATION - CODE 100% FONCTIONNEL
"""

import random
import json
import time
import uuid
import platform
import multiprocessing
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

# Optimisations Apple Silicon universelles (M1/M2/M3/M4/futurs)
APPLE_SILICON_OPTIMIZATIONS = platform.machine() == 'arm64' and 'Apple' in platform.platform()
if APPLE_SILICON_OPTIMIZATIONS:
    # Détection automatique de la génération Apple Silicon
    cpu_count = multiprocessing.cpu_count()
    if cpu_count >= 10:  # M4 et futurs
        CREATIVITY_BOOST = True
        DOMAIN_PROCESSING_CORES = cpu_count
        MEMORY_OPTIMIZATION_LEVEL = "ultra"
        print("🍎 Optimisations Créativité Apple Silicon M4+ détectées")
    elif cpu_count >= 8:  # M3
        CREATIVITY_BOOST = True
        DOMAIN_PROCESSING_CORES = 8
        MEMORY_OPTIMIZATION_LEVEL = "high"
        print("🍎 Optimisations Créativité Apple Silicon M3 détectées")
    else:  # M1/M2
        CREATIVITY_BOOST = True
        DOMAIN_PROCESSING_CORES = cpu_count
        MEMORY_OPTIMIZATION_LEVEL = "standard"
        print("🍎 Optimisations Créativité Apple Silicon M1/M2 détectées")
else:
    CREATIVITY_BOOST = False
    DOMAIN_PROCESSING_CORES = 4
    MEMORY_OPTIMIZATION_LEVEL = "basic"

# Intégrations JARVIS existantes
try:
    from memoire_thermique_turbo_adaptatif import get_memoire_thermique
    THERMAL_MEMORY_AVAILABLE = True
    print("✅ Intégration mémoire thermique JARVIS activée pour créativité")
except ImportError:
    THERMAL_MEMORY_AVAILABLE = False
    print("⚠️ Mémoire thermique JARVIS non disponible pour créativité")

try:
    from jarvis_mcp_protocol import send_mcp_message
    MCP_AVAILABLE = True
    print("✅ Intégration MCP Protocol activée pour créativité")
except ImportError:
    MCP_AVAILABLE = False

# ============================================================================
# STRUCTURES DE DONNÉES CRÉATIVES
# ============================================================================

@dataclass
class CreativeThought:
    """Pensée créative structurée"""
    id: str
    topic: str
    domain1: str
    domain2: str
    creative_fusion: str
    memory_reference: str
    originality_score: float
    timestamp: datetime

    def to_dict(self) -> Dict[str, Any]:
        """Convertit la pensée en dictionnaire"""
        return {
            "id": self.id,
            "topic": self.topic,
            "domain1": self.domain1,
            "domain2": self.domain2,
            "creative_fusion": self.creative_fusion,
            "memory_reference": self.memory_reference,
            "originality_score": self.originality_score,
            "timestamp": self.timestamp.isoformat()
        }

@dataclass
class CreativeDomain:
    """Domaine créatif avec métadonnées"""
    name: str
    category: str
    concepts: List[str]
    frequency_used: int
    last_used: datetime
    creativity_potential: float

    def to_dict(self) -> Dict[str, Any]:
        """Convertit le domaine en dictionnaire"""
        return {
            "name": self.name,
            "category": self.category,
            "concepts": self.concepts,
            "frequency_used": self.frequency_used,
            "last_used": self.last_used.isoformat(),
            "creativity_potential": self.creativity_potential
        }

# ============================================================================
# MOTEUR DE CRÉATIVITÉ PRINCIPAL
# ============================================================================

class CreativityEngine:
    """Moteur de créativité avancé pour JARVIS"""

    def __init__(self, memory_path="thermal_memory_persistent.json"):
        self.memory_path = memory_path
        self.thermal_memory = []
        self.creative_domains = []
        self.creative_thoughts = []

        # Configuration optimisée selon le matériel
        if APPLE_SILICON_OPTIMIZATIONS:
            self.max_creative_thoughts = 5000 if MEMORY_OPTIMIZATION_LEVEL == "ultra" else 2000
            self.max_domains = 200 if MEMORY_OPTIMIZATION_LEVEL == "ultra" else 100
            self.parallel_creativity = True
            print(f"🎨 Configuration créativité optimisée: {MEMORY_OPTIMIZATION_LEVEL}")
        else:
            self.max_creative_thoughts = 500
            self.max_domains = 50
            self.parallel_creativity = False

        # Initialisation
        self.load_thermal_memory()
        self.initialize_creative_domains()

        print("🎨 JARVIS Creativity Engine initialisé")
        print(f"   💾 Mémoire thermique: {len(self.thermal_memory)} souvenirs")
        print(f"   🌐 Domaines créatifs: {len(self.creative_domains)}")
        print(f"   💡 Pensées créatives: {len(self.creative_thoughts)}")

    def load_thermal_memory(self):
        """Charge la mémoire thermique JARVIS"""
        if THERMAL_MEMORY_AVAILABLE:
            try:
                self.thermal_memory = get_memoire_thermique()
                print(f"✅ {len(self.thermal_memory)} souvenirs thermiques chargés pour créativité")
            except Exception as e:
                print(f"⚠️ Erreur chargement mémoire thermique: {e}")
                self.thermal_memory = []
        else:
            # Fallback: charger depuis le fichier JSON
            try:
                with open(self.memory_path, 'r', encoding='utf-8') as file:
                    data = json.load(file)
                    if isinstance(data, list):
                        self.thermal_memory = data
                    else:
                        self.thermal_memory = data.get('memories', [])
                print(f"✅ {len(self.thermal_memory)} souvenirs chargés depuis fichier JSON")
            except FileNotFoundError:
                self.thermal_memory = []
                print("📝 Nouvelle base de mémoire créative créée")
            except Exception as e:
                print(f"⚠️ Erreur chargement fichier mémoire: {e}")
                self.thermal_memory = []

    def initialize_creative_domains(self):
        """Initialise les domaines créatifs étendus"""
        base_domains = [
            # Sciences et technologie
            CreativeDomain("philosophie", "humanités", ["existence", "conscience", "éthique"], 0, datetime.now(), 0.9),
            CreativeDomain("science", "sciences", ["physique", "chimie", "biologie"], 0, datetime.now(), 0.8),
            CreativeDomain("technologie", "sciences", ["IA", "robotique", "quantum"], 0, datetime.now(), 0.9),
            CreativeDomain("mathématiques", "sciences", ["géométrie", "algèbre", "topologie"], 0, datetime.now(), 0.7),

            # Arts et culture
            CreativeDomain("musique", "arts", ["harmonie", "rythme", "mélodie"], 0, datetime.now(), 0.9),
            CreativeDomain("poésie", "arts", ["métaphore", "rythme", "émotion"], 0, datetime.now(), 0.8),
            CreativeDomain("peinture", "arts", ["couleur", "forme", "lumière"], 0, datetime.now(), 0.8),
            CreativeDomain("architecture", "arts", ["espace", "structure", "fonction"], 0, datetime.now(), 0.7),

            # Sciences humaines
            CreativeDomain("psychologie", "humanités", ["comportement", "cognition", "émotion"], 0, datetime.now(), 0.8),
            CreativeDomain("histoire", "humanités", ["évolution", "civilisation", "culture"], 0, datetime.now(), 0.7),
            CreativeDomain("sociologie", "humanités", ["société", "interaction", "groupe"], 0, datetime.now(), 0.7),
            CreativeDomain("anthropologie", "humanités", ["culture", "rituel", "tradition"], 0, datetime.now(), 0.6),

            # Nature et cosmos
            CreativeDomain("biologie", "nature", ["évolution", "adaptation", "écosystème"], 0, datetime.now(), 0.8),
            CreativeDomain("astronomie", "cosmos", ["étoiles", "galaxies", "univers"], 0, datetime.now(), 0.9),
            CreativeDomain("géologie", "nature", ["terre", "minéraux", "formation"], 0, datetime.now(), 0.6),
            CreativeDomain("océanographie", "nature", ["océan", "courants", "vie marine"], 0, datetime.now(), 0.7),

            # Concepts abstraits
            CreativeDomain("temps", "abstrait", ["durée", "éternité", "instant"], 0, datetime.now(), 0.9),
            CreativeDomain("espace", "abstrait", ["dimension", "infini", "vide"], 0, datetime.now(), 0.9),
            CreativeDomain("énergie", "abstrait", ["force", "mouvement", "transformation"], 0, datetime.now(), 0.8),
            CreativeDomain("information", "abstrait", ["données", "signal", "communication"], 0, datetime.now(), 0.8),
        ]

        self.creative_domains = base_domains

        # Enrichir avec des domaines de la mémoire thermique
        self._extract_domains_from_memory()

    def _extract_domains_from_memory(self):
        """Extrait de nouveaux domaines depuis la mémoire thermique"""
        domain_keywords = {}

        for memory in self.thermal_memory[-100:]:  # 100 derniers souvenirs
            if isinstance(memory, dict):
                text = ""
                if "user_message" in memory:
                    text += memory["user_message"] + " "
                if "agent_response" in memory:
                    text += memory["agent_response"] + " "
                if "contenu" in memory:
                    text += memory["contenu"] + " "

                # Extraire des mots-clés potentiels
                words = text.lower().split()
                for word in words:
                    if len(word) > 4 and word.isalpha():
                        domain_keywords[word] = domain_keywords.get(word, 0) + 1

        # Créer de nouveaux domaines à partir des mots-clés fréquents
        for keyword, frequency in sorted(domain_keywords.items(), key=lambda x: x[1], reverse=True)[:10]:
            if not any(keyword in domain.name.lower() for domain in self.creative_domains):
                new_domain = CreativeDomain(
                    name=keyword,
                    category="mémoire",
                    concepts=[keyword, f"{keyword}_concept", f"{keyword}_application"],
                    frequency_used=0,
                    last_used=datetime.now(),
                    creativity_potential=min(0.9, frequency / 10.0)
                )
                self.creative_domains.append(new_domain)

        print(f"🌐 {len(self.creative_domains)} domaines créatifs disponibles")

    def _select_relevant_memory(self, topic: Optional[str]) -> Optional[Dict[str, Any]]:
        """Sélectionne un souvenir pertinent pour le sujet"""
        if not self.thermal_memory:
            return None

        if topic:
            # Rechercher des souvenirs liés au sujet
            relevant_memories = []
            topic_lower = topic.lower()

            for memory in self.thermal_memory:
                if isinstance(memory, dict):
                    memory_text = ""
                    for key in ["user_message", "agent_response", "contenu", "sujet"]:
                        if key in memory:
                            memory_text += str(memory[key]).lower() + " "

                    if any(word in memory_text for word in topic_lower.split()):
                        relevant_memories.append(memory)

            if relevant_memories:
                return random.choice(relevant_memories)

        # Sélection aléatoire si pas de sujet ou pas de correspondance
        return random.choice(self.thermal_memory)

    def _generate_fusion(self, domain1: CreativeDomain, domain2: CreativeDomain,
                        topic: Optional[str], memory_entry: Optional[Dict[str, Any]]) -> str:
        """Génère une fusion créative entre deux domaines"""

        # Templates de fusion créative
        topic_display = topic or "un concept innovant"
        fusion_templates = [
            f"Que se passerait-il si on combinait {domain1.name} et {domain2.name} pour explorer '{topic_display}' ?",
            f"Imaginez une synthèse révolutionnaire entre {domain1.name} et {domain2.name} appliquée à {topic or 'notre réalité'}.",
            f"Comment {domain1.name} pourrait-il transformer {domain2.name} dans le contexte de '{topic or 'l évolution future'}' ?",
            f"Une fusion inattendue : {domain1.name} × {domain2.name} = nouvelle perspective sur {topic or 'l existence'}.",
            f"Si {domain1.name} et {domain2.name} collaboraient, quelle innovation émergerait pour {topic or 'l humanité'} ?",
            f"Connexion créative : {domain1.name} inspire {domain2.name} pour révolutionner {topic or 'notre compréhension'}.",
            f"Métamorphose conceptuelle : {domain1.name} + {domain2.name} → nouvelle réalité de {topic or 'l intelligence'}."
        ]

        # Sélectionner un template
        base_fusion = random.choice(fusion_templates)

        # Enrichir avec la mémoire si disponible
        if memory_entry and isinstance(memory_entry, dict):
            memory_context = ""
            for key in ["contenu", "agent_response", "user_message"]:
                if key in memory_entry:
                    memory_context = str(memory_entry[key])[:100]
                    break

            if memory_context:
                base_fusion += f" Inspiration mémoire : '{memory_context}...'"

        # Ajouter des concepts spécifiques des domaines
        if domain1.concepts and domain2.concepts:
            concept1 = random.choice(domain1.concepts)
            concept2 = random.choice(domain2.concepts)
            base_fusion += f" Concepts clés : {concept1} ↔ {concept2}."

        return base_fusion

    def _calculate_originality(self, domain1: CreativeDomain, domain2: CreativeDomain,
                              fusion: str) -> float:
        """Calcule le score d'originalité d'une fusion"""

        # Facteurs d'originalité
        originality_score = 0.5  # Base

        # Bonus pour domaines rarement utilisés
        if domain1.frequency_used < 5:
            originality_score += 0.1
        if domain2.frequency_used < 5:
            originality_score += 0.1

        # Bonus pour domaines de catégories différentes
        if domain1.category != domain2.category:
            originality_score += 0.2

        # Bonus pour potentiel créatif élevé
        avg_potential = (domain1.creativity_potential + domain2.creativity_potential) / 2
        originality_score += avg_potential * 0.2

        # Bonus pour longueur et complexité de la fusion
        if len(fusion) > 200:
            originality_score += 0.1

        # Vérifier l'unicité par rapport aux pensées précédentes
        similar_thoughts = 0
        for thought in self.creative_thoughts[-50:]:  # 50 dernières pensées
            if (thought.domain1 == domain1.name and thought.domain2 == domain2.name) or \
               (thought.domain1 == domain2.name and thought.domain2 == domain1.name):
                similar_thoughts += 1

        # Pénalité pour répétition
        originality_score -= similar_thoughts * 0.05

        # Normaliser entre 0 et 1
        return max(0.0, min(1.0, originality_score))

    def generate_creative_thought(self, topic: Optional[str] = None) -> CreativeThought:
        """Génère une pensée créative en fusionnant des domaines"""

        # Sélectionner deux domaines différents
        if len(self.creative_domains) < 2:
            raise ValueError("Pas assez de domaines créatifs disponibles")

        # Utiliser une sélection pondérée par le potentiel créatif
        if APPLE_SILICON_OPTIMIZATIONS and self.parallel_creativity:
            # Sélection optimisée pour Apple Silicon
            weights = [domain.creativity_potential for domain in self.creative_domains]
            domain1, domain2 = random.choices(self.creative_domains, weights=weights, k=2)
            while domain1 == domain2:
                domain2 = random.choices(self.creative_domains, weights=weights, k=1)[0]
        else:
            domain1, domain2 = random.sample(self.creative_domains, 2)

        # Sélectionner un souvenir pertinent
        memory_entry = self._select_relevant_memory(topic)

        # Générer la fusion créative
        creative_fusion = self._generate_fusion(domain1, domain2, topic, memory_entry)

        # Calculer le score d'originalité
        originality_score = self._calculate_originality(domain1, domain2, creative_fusion)

        # Créer la pensée créative
        thought = CreativeThought(
            id=str(uuid.uuid4()),
            topic=topic or "exploration libre",
            domain1=domain1.name,
            domain2=domain2.name,
            creative_fusion=creative_fusion,
            memory_reference=str(memory_entry)[:200] if memory_entry else "aucune référence",
            originality_score=originality_score,
            timestamp=datetime.now()
        )

        # Mettre à jour les statistiques des domaines
        domain1.frequency_used += 1
        domain1.last_used = datetime.now()
        domain2.frequency_used += 1
        domain2.last_used = datetime.now()

        # Ajouter à l'historique
        self.creative_thoughts.append(thought)

        # Maintenir la limite
        if len(self.creative_thoughts) > self.max_creative_thoughts:
            self.creative_thoughts = self.creative_thoughts[-self.max_creative_thoughts//2:]

        # Notifier via MCP si disponible
        if MCP_AVAILABLE:
            try:
                send_mcp_message({
                    "type": "creative_thought_generated",
                    "thought_id": thought.id,
                    "domains": [domain1.name, domain2.name],
                    "originality": originality_score,
                    "timestamp": thought.timestamp.isoformat()
                })
            except Exception as e:
                print(f"⚠️ Erreur notification MCP créativité: {e}")

        print(f"💡 Pensée créative générée: {domain1.name} × {domain2.name} (originalité: {originality_score:.2f})")
        return thought

    def get_creative_insights(self, n: int = 5) -> List[CreativeThought]:
        """Retourne les dernières pensées créatives"""
        return sorted(self.creative_thoughts, key=lambda t: t.timestamp, reverse=True)[:n]

    def get_domain_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques des domaines créatifs"""
        return {
            "total_domains": len(self.creative_domains),
            "most_used_domains": sorted(
                [(d.name, d.frequency_used) for d in self.creative_domains],
                key=lambda x: x[1], reverse=True
            )[:10],
            "highest_potential_domains": sorted(
                [(d.name, d.creativity_potential) for d in self.creative_domains],
                key=lambda x: x[1], reverse=True
            )[:10],
            "domains_by_category": {}
        }

    def get_creativity_status(self) -> Dict[str, Any]:
        """Retourne le statut complet du moteur de créativité"""
        avg_originality = 0.0
        if self.creative_thoughts:
            avg_originality = sum(t.originality_score for t in self.creative_thoughts) / len(self.creative_thoughts)

        return {
            "total_thoughts": len(self.creative_thoughts),
            "total_domains": len(self.creative_domains),
            "thermal_memory_size": len(self.thermal_memory),
            "average_originality": avg_originality,
            "optimizations": {
                "apple_silicon": APPLE_SILICON_OPTIMIZATIONS,
                "optimization_level": MEMORY_OPTIMIZATION_LEVEL,
                "parallel_creativity": getattr(self, 'parallel_creativity', False)
            },
            "integrations": {
                "thermal_memory": THERMAL_MEMORY_AVAILABLE,
                "mcp_protocol": MCP_AVAILABLE
            },
            "last_thought": self.creative_thoughts[-1].timestamp.isoformat() if self.creative_thoughts else None
        }

# ============================================================================
# INSTANCE GLOBALE JARVIS CREATIVITY ENGINE
# ============================================================================

# Instance globale pour intégration avec JARVIS
JARVIS_CREATIVITY = None

def get_jarvis_creativity() -> CreativityEngine:
    """Retourne l'instance de créativité de JARVIS"""
    global JARVIS_CREATIVITY
    if JARVIS_CREATIVITY is None:
        JARVIS_CREATIVITY = CreativityEngine()
    return JARVIS_CREATIVITY

def generate_creative_thought(topic: Optional[str] = None) -> Dict[str, Any]:
    """Interface simple pour générer une pensée créative"""
    creativity = get_jarvis_creativity()
    thought = creativity.generate_creative_thought(topic)
    return thought.to_dict()

def get_creative_insights(n: int = 5) -> List[Dict[str, Any]]:
    """Interface simple pour obtenir les dernières pensées créatives"""
    creativity = get_jarvis_creativity()
    thoughts = creativity.get_creative_insights(n)
    return [thought.to_dict() for thought in thoughts]

def get_creativity_status() -> Dict[str, Any]:
    """Interface simple pour obtenir le statut de créativité"""
    creativity = get_jarvis_creativity()
    return creativity.get_creativity_status()

def get_domain_stats() -> Dict[str, Any]:
    """Interface simple pour obtenir les statistiques des domaines"""
    creativity = get_jarvis_creativity()
    return creativity.get_domain_statistics()

# ============================================================================
# INTÉGRATION AVEC RAISONNEMENT COGNITIF (si disponible)
# ============================================================================

def integrate_with_cognitive_reasoning():
    """Intègre la créativité avec le raisonnement cognitif"""
    try:
        from jarvis_raisonnement_cognitif import get_jarvis_raisonnement, ajouter_episode_cognitif

        creativity = get_jarvis_creativity()
        raisonnement = get_jarvis_raisonnement()

        # Ajouter un épisode cognitif pour l'intégration créativité
        ajouter_episode_cognitif(
            contexte="Intégration système créativité",
            action="Connexion moteur créativité avec raisonnement cognitif",
            resultat="Synergie créativité-raisonnement activée pour JARVIS",
            emotions={"créativité": 0.9, "innovation": 0.8},
            importance=2.0
        )

        print("🔗 Intégration Créativité ↔ Raisonnement Cognitif réussie")

    except ImportError:
        print("⚠️ Module Raisonnement Cognitif non disponible pour l'intégration")
    except Exception as e:
        print(f"❌ Erreur intégration Créativité-Raisonnement: {e}")

# ============================================================================
# TESTS ET DÉMONSTRATION
# ============================================================================

if __name__ == "__main__":
    print("🧪 Test JARVIS Creativity Engine")
    print("=" * 50)

    # Initialiser le système
    creativity = get_jarvis_creativity()

    # Test 1: Génération de pensées créatives
    print("\n💡 Test 1: Génération de pensées créatives")

    topics = ["immortalité", "intelligence artificielle", "voyage spatial", "conscience", "art génératif"]

    for topic in topics[:3]:  # Tester 3 sujets
        thought = generate_creative_thought(topic)
        print(f"\n🎨 Sujet: {topic}")
        print(f"   Domaines: {thought['domain1']} × {thought['domain2']}")
        print(f"   Originalité: {thought['originality_score']:.2f}")
        print(f"   Fusion: {thought['creative_fusion'][:100]}...")

    # Test 2: Pensée libre
    print("\n🌟 Test 2: Pensée créative libre")
    free_thought = generate_creative_thought()
    print(f"Fusion libre: {free_thought['creative_fusion'][:150]}...")

    # Test 3: Insights créatifs
    print("\n📊 Test 3: Insights créatifs récents")
    insights = get_creative_insights(3)
    print(f"Dernières {len(insights)} pensées créatives:")
    for i, insight in enumerate(insights, 1):
        print(f"  {i}. {insight['domain1']} × {insight['domain2']} (score: {insight['originality_score']:.2f})")

    # Test 4: Statistiques
    print("\n📈 Test 4: Statistiques du système")
    status = get_creativity_status()
    print(f"Total pensées: {status['total_thoughts']}")
    print(f"Total domaines: {status['total_domains']}")
    print(f"Originalité moyenne: {status['average_originality']:.2f}")
    print(f"Optimisations Apple Silicon: {status['optimizations']['apple_silicon']}")

    # Test 5: Statistiques domaines
    print("\n🌐 Test 5: Domaines les plus utilisés")
    domain_stats = get_domain_stats()
    print("Top 5 domaines utilisés:")
    for i, (domain, usage) in enumerate(domain_stats['most_used_domains'][:5], 1):
        print(f"  {i}. {domain}: {usage} utilisations")

    # Test 6: Intégration avec raisonnement cognitif
    print("\n🔗 Test 6: Intégration avec raisonnement cognitif")
    integrate_with_cognitive_reasoning()

    print("\n🎉 Tests terminés avec succès!")
    print("Le moteur de créativité JARVIS est prêt pour l'intégration complète.")

    # Exemple d'utilisation simple comme dans votre code original
    print("\n" + "="*50)
    print("🎨 Exemple d'utilisation simple (comme votre code original):")

    # Simulation de votre code original
    engine = creativity
    creative_result = engine.generate_creative_thought("immortalité")
    print(f"\nRésultat créatif: {creative_result.creative_fusion}")
    print(f"Domaines fusionnés: {creative_result.domain1} + {creative_result.domain2}")
    print(f"Score d'originalité: {creative_result.originality_score:.2f}")
    print(f"Référence mémoire: {creative_result.memory_reference[:100]}...")