#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR COMPLET JARVIS - JEAN-LUC PASSAVE
Script principal pour appliquer toutes les corrections et ajouter le bouton de surveillance
"""

import os
import sys
import time
import subprocess
from datetime import datetime

class CorrecteurCompletJarvis:
    """CORRECTEUR COMPLET POUR JARVIS"""
    
    def __init__(self):
        self.etapes_completees = []
        
    def log_etape(self, etape, status, details=""):
        """ENREGISTRER UNE ÉTAPE"""
        self.etapes_completees.append({
            "timestamp": datetime.now().isoformat(),
            "etape": etape,
            "status": status,
            "details": details
        })
        
        status_icon = "✅" if status == "SUCCESS" else "❌" if status == "ERROR" else "⚠️"
        print(f"{status_icon} {etape}: {status}")
        if details:
            print(f"   📝 {details}")
    
    def verifier_prerequis(self):
        """VÉRIFIER LES PRÉREQUIS"""
        print("🔍 ÉTAPE 1: VÉRIFICATION PRÉREQUIS")
        print("-" * 50)
        
        # Vérifier Python
        if sys.version_info >= (3, 8):
            self.log_etape("Version Python", "SUCCESS", f"Python {sys.version}")
        else:
            self.log_etape("Version Python", "ERROR", "Python 3.8+ requis")
            return False
        
        # Vérifier fichiers principaux
        fichiers_requis = [
            "jarvis_architecture_multi_fenetres.py",
            "surveillance_jarvis_activite.py"
        ]
        
        for fichier in fichiers_requis:
            if os.path.exists(fichier):
                self.log_etape(f"Fichier {fichier}", "SUCCESS", "Présent")
            else:
                self.log_etape(f"Fichier {fichier}", "ERROR", "Manquant")
                return False
        
        return True
    
    def creer_fallbacks_modules(self):
        """CRÉER LES FALLBACKS POUR MODULES MANQUANTS"""
        print("\n🔧 ÉTAPE 2: CRÉATION FALLBACKS MODULES")
        print("-" * 50)
        
        try:
            from correcteur_modules_manquants import CorrecteurModulesManquants
            correcteur = CorrecteurModulesManquants()
            correcteur.corriger_tous_modules()
            self.log_etape("Fallbacks modules", "SUCCESS", "Tous les fallbacks créés")
            return True
        except Exception as e:
            self.log_etape("Fallbacks modules", "ERROR", str(e))
            return False
    
    def corriger_securite_electron(self):
        """CORRIGER LA SÉCURITÉ ELECTRON"""
        print("\n🔒 ÉTAPE 3: CORRECTION SÉCURITÉ ELECTRON")
        print("-" * 50)
        
        try:
            from correcteur_securite_electron import CorrecteurSecuriteElectron
            correcteur = CorrecteurSecuriteElectron()
            correcteur.corriger_tous_fichiers()
            self.log_etape("Sécurité Electron", "SUCCESS", "Corrections appliquées")
            return True
        except Exception as e:
            self.log_etape("Sécurité Electron", "ERROR", str(e))
            return False
    
    def verifier_corrections_ports(self):
        """VÉRIFIER LES CORRECTIONS DE PORTS"""
        print("\n🔌 ÉTAPE 4: VÉRIFICATION PORTS")
        print("-" * 50)
        
        try:
            with open("jarvis_architecture_multi_fenetres.py", 'r', encoding='utf-8') as f:
                contenu = f.read()
            
            if '"code_port": 8108' in contenu:
                self.log_etape("Port Code corrigé", "SUCCESS", "8102 → 8108")
            else:
                self.log_etape("Port Code corrigé", "WARNING", "Correction manuelle nécessaire")
            
            if '"surveillance": 8260' in contenu:
                self.log_etape("Port Surveillance", "SUCCESS", "Port 8260 configuré")
            else:
                self.log_etape("Port Surveillance", "WARNING", "Configuration manuelle nécessaire")
            
            return True
        except Exception as e:
            self.log_etape("Vérification ports", "ERROR", str(e))
            return False
    
    def verifier_interface_surveillance(self):
        """VÉRIFIER L'INTERFACE DE SURVEILLANCE"""
        print("\n🕐 ÉTAPE 5: VÉRIFICATION INTERFACE SURVEILLANCE")
        print("-" * 50)
        
        if os.path.exists("interface_surveillance_jarvis.py"):
            self.log_etape("Interface surveillance", "SUCCESS", "Fichier créé")
            
            try:
                with open("interface_surveillance_jarvis.py", 'r', encoding='utf-8') as f:
                    contenu = f.read()
                
                if "server_port=8260" in contenu:
                    self.log_etape("Port surveillance configuré", "SUCCESS", "Port 8260")
                else:
                    self.log_etape("Port surveillance configuré", "WARNING", "Vérification nécessaire")
                
                return True
            except Exception as e:
                self.log_etape("Analyse interface surveillance", "ERROR", str(e))
                return False
        else:
            self.log_etape("Interface surveillance", "ERROR", "Fichier manquant")
            return False
    
    def tester_corrections(self):
        """TESTER TOUTES LES CORRECTIONS"""
        print("\n🧪 ÉTAPE 6: TESTS CORRECTIONS")
        print("-" * 50)
        
        try:
            from testeur_corrections_jarvis import TesteurCorrectionsJarvis
            testeur = TesteurCorrectionsJarvis()
            testeur.executer_tous_tests()
            self.log_etape("Tests corrections", "SUCCESS", "Tests exécutés")
            return True
        except Exception as e:
            self.log_etape("Tests corrections", "ERROR", str(e))
            return False
    
    def creer_script_lancement(self):
        """CRÉER UN SCRIPT DE LANCEMENT POUR LA SURVEILLANCE"""
        print("\n🚀 ÉTAPE 7: SCRIPT LANCEMENT SURVEILLANCE")
        print("-" * 50)
        
        script_lancement = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🕐 LANCEUR SURVEILLANCE JARVIS - JEAN-LUC PASSAVE
Script pour lancer l'interface de surveillance sur le port 8260
"""

import os
import sys
import time
import webbrowser
from datetime import datetime

def lancer_surveillance():
    """LANCER L'INTERFACE DE SURVEILLANCE"""
    print("🕐 DÉMARRAGE SURVEILLANCE JARVIS - JEAN-LUC PASSAVE")
    print("=" * 60)
    print(f"⏰ Heure: {datetime.now().strftime('%H:%M:%S')}")
    print(f"📅 Date: {datetime.now().strftime('%d/%m/%Y')}")
    print("=" * 60)
    
    try:
        # Importer et lancer l'interface
        from interface_surveillance_jarvis import create_surveillance_interface
        
        print("🔧 Création de l'interface...")
        interface = create_surveillance_interface()
        
        print("🌐 Lancement du serveur sur le port 8260...")
        print("🔗 URL: http://localhost:8260")
        print("=" * 60)
        print("✅ SURVEILLANCE JARVIS ACTIVE")
        print("💡 Utilisez Ctrl+C pour arrêter")
        print("🌐 Ouverture automatique du navigateur dans 3 secondes...")
        
        # Ouvrir le navigateur après 3 secondes
        import threading
        def ouvrir_navigateur():
            time.sleep(3)
            webbrowser.open("http://localhost:8260")
        
        thread_nav = threading.Thread(target=ouvrir_navigateur)
        thread_nav.daemon = True
        thread_nav.start()
        
        # Lancer l'interface
        interface.launch(
            server_name="0.0.0.0",
            server_port=8260,
            share=False,
            show_error=True,
            quiet=False
        )
        
    except KeyboardInterrupt:
        print("\\n🛑 ARRÊT DEMANDÉ PAR L'UTILISATEUR")
        print("✅ SURVEILLANCE ARRÊTÉE PROPREMENT")
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        print("💡 Vérifiez que tous les fichiers sont présents")
        return False
    
    return True

if __name__ == "__main__":
    lancer_surveillance()
'''
        
        try:
            with open('lancer_surveillance_jarvis.py', 'w', encoding='utf-8') as f:
                f.write(script_lancement)
            
            # Rendre exécutable sur Unix
            if os.name != 'nt':
                os.chmod('lancer_surveillance_jarvis.py', 0o755)
            
            self.log_etape("Script lancement", "SUCCESS", "lancer_surveillance_jarvis.py créé")
            return True
        except Exception as e:
            self.log_etape("Script lancement", "ERROR", str(e))
            return False
    
    def generer_rapport_final(self):
        """GÉNÉRER LE RAPPORT FINAL"""
        print("\n" + "=" * 70)
        print("📊 RAPPORT FINAL CORRECTEUR COMPLET")
        print("=" * 70)
        
        total_etapes = len(self.etapes_completees)
        etapes_success = len([e for e in self.etapes_completees if e['status'] == 'SUCCESS'])
        etapes_error = len([e for e in self.etapes_completees if e['status'] == 'ERROR'])
        etapes_warning = len([e for e in self.etapes_completees if e['status'] == 'WARNING'])
        
        print(f"📈 STATISTIQUES:")
        print(f"   ✅ Étapes réussies: {etapes_success}/{total_etapes}")
        print(f"   ❌ Étapes échouées: {etapes_error}/{total_etapes}")
        print(f"   ⚠️ Avertissements: {etapes_warning}/{total_etapes}")
        
        pourcentage_reussite = (etapes_success / total_etapes) * 100 if total_etapes > 0 else 0
        print(f"   📊 Taux de réussite: {pourcentage_reussite:.1f}%")
        
        print(f"\\n🎯 RÉSULTATS:")
        if pourcentage_reussite >= 90:
            print("🎉 CORRECTIONS COMPLÈTES - JARVIS ENTIÈREMENT CORRIGÉ")
            print("✅ Interface de surveillance disponible sur http://localhost:8260")
            print("🚀 Utilisez: python lancer_surveillance_jarvis.py")
        elif pourcentage_reussite >= 70:
            print("⚠️ CORRECTIONS PARTIELLES - QUELQUES AJUSTEMENTS NÉCESSAIRES")
            print("🔧 Vérifiez les avertissements et erreurs ci-dessus")
        else:
            print("❌ CORRECTIONS INSUFFISANTES - RÉVISION MAJEURE REQUISE")
            print("🛠️ Contactez le support technique")
        
        print(f"\\n📋 INSTRUCTIONS POST-CORRECTION:")
        print("1. Vérifiez le rapport de tests: rapport_tests_corrections.json")
        print("2. Lancez JARVIS: python jarvis_architecture_multi_fenetres.py")
        print("3. Testez la surveillance: python lancer_surveillance_jarvis.py")
        print("4. Vérifiez tous les boutons de navigation")
        print("5. Surveillez les logs pour détecter d'éventuels problèmes")
        
        # Sauvegarder le rapport
        import json
        rapport = {
            "timestamp": datetime.now().isoformat(),
            "statistiques": {
                "total": total_etapes,
                "success": etapes_success,
                "error": etapes_error,
                "warning": etapes_warning,
                "taux_reussite": pourcentage_reussite
            },
            "etapes_detaillees": self.etapes_completees,
            "fichiers_crees": [
                "interface_surveillance_jarvis.py",
                "correcteur_securite_electron.py",
                "correcteur_modules_manquants.py",
                "testeur_corrections_jarvis.py",
                "lancer_surveillance_jarvis.py",
                "redis_fallback.py",
                "diffusers_fallback.py",
                "torch_fallback.py"
            ]
        }
        
        with open('rapport_correcteur_complet.json', 'w', encoding='utf-8') as f:
            json.dump(rapport, f, ensure_ascii=False, indent=2)
        
        print(f"\\n💾 Rapport complet sauvegardé: rapport_correcteur_complet.json")
    
    def executer_correction_complete(self):
        """EXÉCUTER LA CORRECTION COMPLÈTE"""
        print("🔧 DÉMARRAGE CORRECTEUR COMPLET JARVIS - JEAN-LUC PASSAVE")
        print("=" * 70)
        print("🎯 OBJECTIFS:")
        print("   ✅ Corriger tous les logs d'erreur")
        print("   🔒 Renforcer la sécurité Electron")
        print("   🔌 Optimiser la gestion des ports")
        print("   🕐 Ajouter l'interface de surveillance")
        print("   🧪 Valider toutes les corrections")
        print("=" * 70)
        
        # Exécuter toutes les étapes
        if not self.verifier_prerequis():
            print("❌ PRÉREQUIS NON SATISFAITS - ARRÊT")
            return False
        
        self.creer_fallbacks_modules()
        self.corriger_securite_electron()
        self.verifier_corrections_ports()
        self.verifier_interface_surveillance()
        self.tester_corrections()
        self.creer_script_lancement()
        
        self.generer_rapport_final()
        return True

def main():
    """FONCTION PRINCIPALE"""
    correcteur = CorrecteurCompletJarvis()
    correcteur.executer_correction_complete()

if __name__ == "__main__":
    main()
