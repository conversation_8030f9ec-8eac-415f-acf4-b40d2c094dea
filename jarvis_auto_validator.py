#!/usr/bin/env python3
"""
🔍 JARVIS AUTO VALIDATOR - JEAN-LUC PASSAVE
Système de validation automatique du code avant solidification
"""

import ast
import subprocess
import json
import time
from pathlib import Path
from jarvis_code_solidifier import JarvisCodeSolidifier

class JarvisAutoValidator:
    """Validateur automatique du code JARVIS"""
    
    def __init__(self, base_path="/Volumes/seagate/Louna_Electron_Latest"):
        self.base_path = Path(base_path)
        self.solidifier = JarvisCodeSolidifier(base_path)
        
        # Tests de validation
        self.validation_tests = {
            "syntax_check": self.check_python_syntax,
            "import_check": self.check_imports,
            "function_check": self.check_critical_functions,
            "no_simulation": self.check_no_simulation,
            "no_ollama": self.check_no_ollama,
            "startup_test": self.test_startup
        }
    
    def check_python_syntax(self):
        """Vérifier la syntaxe Python"""
        print("🔍 Vérification syntaxe Python...")
        
        python_files = [
            "jarvis_architecture_multi_fenetres.py",
            "jarvis_goap_planner.py",
            "jarvis_intelligent_thought_generator.py",
            "jarvis_cerveau_artificiel_structure.py"
        ]
        
        for file_name in python_files:
            file_path = self.base_path / file_name
            if not file_path.exists():
                print(f"⚠️ {file_name} introuvable")
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Vérifier la syntaxe avec AST
                ast.parse(content)
                print(f"✅ {file_name} - Syntaxe OK")
                
            except SyntaxError as e:
                print(f"❌ {file_name} - Erreur syntaxe ligne {e.lineno}: {e.msg}")
                return False
            except Exception as e:
                print(f"❌ {file_name} - Erreur: {e}")
                return False
        
        return True
    
    def check_imports(self):
        """Vérifier que tous les imports sont disponibles"""
        print("🔍 Vérification imports...")
        
        required_modules = [
            "gradio", "fastapi", "uvicorn", "redis", "networkx",
            "transformers", "diffusers", "torch", "numpy", "pandas"
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module} disponible")
            except ImportError:
                print(f"❌ {module} MANQUANT")
                missing_modules.append(module)
        
        if missing_modules:
            print(f"❌ Modules manquants: {missing_modules}")
            return False
        
        return True
    
    def check_critical_functions(self):
        """Vérifier que les fonctions critiques existent"""
        print("🔍 Vérification fonctions critiques...")
        
        main_file = self.base_path / "jarvis_architecture_multi_fenetres.py"
        if not main_file.exists():
            print("❌ Fichier principal introuvable")
            return False
        
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            critical_functions = [
                "create_jarvis_interface",
                "generate_intelligent_response",
                "thermal_consciousness_stream",
                "create_energy_dashboard"
            ]
            
            for func_name in critical_functions:
                if f"def {func_name}" in content:
                    print(f"✅ {func_name} trouvée")
                else:
                    print(f"❌ {func_name} MANQUANTE")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur vérification fonctions: {e}")
            return False
    
    def check_no_simulation(self):
        """Vérifier qu'il n'y a pas de simulation"""
        print("🔍 Vérification absence de simulation...")
        
        python_files = [
            "jarvis_architecture_multi_fenetres.py",
            "jarvis_goap_planner.py",
            "jarvis_intelligent_thought_generator.py"
        ]
        
        simulation_keywords = [
            "simulation de", "simulé", "fake data", "mock response",
            "test data", "exemple data", "placeholder response"
        ]
        
        for file_name in python_files:
            file_path = self.base_path / file_name
            if not file_path.exists():
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                for keyword in simulation_keywords:
                    if keyword in content:
                        print(f"❌ {file_name} contient '{keyword}'")
                        return False
                
                print(f"✅ {file_name} - Pas de simulation")
                
            except Exception as e:
                print(f"❌ Erreur lecture {file_name}: {e}")
                return False
        
        return True
    
    def check_no_ollama(self):
        """Vérifier qu'il n'y a pas d'Ollama (interdit)"""
        print("🔍 Vérification absence d'Ollama...")
        
        all_files = list(self.base_path.glob("*.py")) + list(self.base_path.glob("*.js"))
        
        for file_path in all_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                if "ollama" in content:
                    print(f"❌ {file_path.name} contient 'ollama' (INTERDIT)")
                    return False
                
            except Exception:
                continue
        
        print("✅ Aucun Ollama détecté")
        return True
    
    def test_startup(self):
        """Tester le démarrage de l'application"""
        print("🔍 Test de démarrage...")
        
        try:
            # Test import du module principal
            import sys
            sys.path.insert(0, str(self.base_path))
            
            # Tenter d'importer le module principal
            import jarvis_architecture_multi_fenetres
            print("✅ Module principal importable")
            
            # Vérifier que les classes principales existent
            if hasattr(jarvis_architecture_multi_fenetres, 'ThermalMemory'):
                print("✅ ThermalMemory disponible")
            else:
                print("❌ ThermalMemory manquante")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur test démarrage: {e}")
            return False
    
    def run_full_validation(self):
        """Exécuter la validation complète"""
        print("🔍 VALIDATION COMPLÈTE DU CODE JARVIS")
        print("=" * 50)
        
        validation_results = {}
        all_passed = True
        
        for test_name, test_func in self.validation_tests.items():
            print(f"\n🔍 Test: {test_name}")
            try:
                result = test_func()
                validation_results[test_name] = {
                    "passed": result,
                    "timestamp": time.time()
                }
                
                if result:
                    print(f"✅ {test_name} RÉUSSI")
                else:
                    print(f"❌ {test_name} ÉCHOUÉ")
                    all_passed = False
                    
            except Exception as e:
                print(f"❌ {test_name} ERREUR: {e}")
                validation_results[test_name] = {
                    "passed": False,
                    "error": str(e),
                    "timestamp": time.time()
                }
                all_passed = False
        
        # Sauvegarder les résultats
        results_file = self.base_path / "validation_results.json"
        with open(results_file, 'w') as f:
            json.dump(validation_results, f, indent=2)
        
        print("\n" + "=" * 50)
        if all_passed:
            print("🎉 VALIDATION COMPLÈTE RÉUSSIE !")
            print("🔒 Code prêt pour solidification")
            
            # Créer automatiquement une sauvegarde solidifiée
            print("\n🔒 Création automatique sauvegarde solidifiée...")
            self.solidifier.create_solid_backup("VALIDATED_VERSION")
            
        else:
            print("❌ VALIDATION ÉCHOUÉE")
            print("🔧 Corrections nécessaires avant solidification")
        
        return all_passed

def main():
    """Fonction principale"""
    validator = JarvisAutoValidator()
    
    print("🔍 JARVIS AUTO VALIDATOR - JEAN-LUC PASSAVE")
    print("Validation automatique avant solidification")
    print("=" * 50)
    
    # Exécuter la validation complète
    success = validator.run_full_validation()
    
    if success:
        print("\n✅ CODE VALIDÉ ET SOLIDIFIÉ")
    else:
        print("\n❌ VALIDATION ÉCHOUÉE - CORRECTIONS NÉCESSAIRES")

if __name__ == "__main__":
    main()
