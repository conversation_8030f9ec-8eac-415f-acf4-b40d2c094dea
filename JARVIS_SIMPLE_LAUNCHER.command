#!/bin/bash

# 🚀 LANCEUR SIMPLE JARVIS - JEAN-LUC PASSAVE
# Lanceur de secours simplifié - 25 JUIN 2025

echo "🤖 JARVIS SIMPLE LAUNCHER - JEAN-LUC PASSAVE"
echo "============================================="
echo "🔧 Lanceur de secours simplifié"
echo ""

# Aller dans le bon répertoire
cd "$(dirname "$0")"
echo "📁 Répertoire: $(pwd)"

# Nettoyer les anciens processus
echo "🧹 Nettoyage des processus..."
pkill -f "jarvis" 2>/dev/null || true
sleep 2

# Vérifier Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 non trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo "✅ Python3 disponible"

# Démarrer l'interface JARVIS la plus simple
echo ""
echo "🚀 Démarrage de JARVIS..."

# Essayer différentes interfaces dans l'ordre de préférence
if [ -f "jarvis_test_simple.py" ]; then
    echo "🧪 Démarrage interface de test simple..."
    python3 jarvis_test_simple.py &
    JARVIS_PID=$!
    echo "✅ Interface test démarrée (PID: $JARVIS_PID) sur port 8110"
    
    # Attendre et vérifier
    sleep 5
    if curl -s http://localhost:8110 > /dev/null 2>&1; then
        echo "✅ Interface accessible sur http://localhost:8110"
        echo ""
        echo "🌐 Ouvrez votre navigateur sur: http://localhost:8110"
        echo "💬 Interface de test JARVIS prête !"
        echo ""
        echo "Appuyez sur Ctrl+C pour arrêter JARVIS"
        wait $JARVIS_PID
    else
        echo "❌ Interface test non accessible"
    fi

elif [ -f "jarvis_interface_html_originale.py" ]; then
    echo "🎨 Démarrage interface HTML originale..."
    python3 jarvis_interface_html_originale.py &
    JARVIS_PID=$!
    echo "✅ Interface HTML démarrée (PID: $JARVIS_PID) sur port 8109"
    
    # Attendre et vérifier
    sleep 5
    if curl -s http://localhost:8109 > /dev/null 2>&1; then
        echo "✅ Interface accessible sur http://localhost:8109"
        echo ""
        echo "🌐 Ouvrez votre navigateur sur: http://localhost:8109"
        echo "💬 Interface HTML JARVIS prête !"
        echo ""
        echo "Appuyez sur Ctrl+C pour arrêter JARVIS"
        wait $JARVIS_PID
    else
        echo "❌ Interface HTML non accessible"
    fi

elif [ -f "jarvis_architecture_multi_fenetres.py" ]; then
    echo "🏗️ Démarrage interface complète..."
    python3 jarvis_architecture_multi_fenetres.py &
    JARVIS_PID=$!
    echo "✅ Interface complète démarrée (PID: $JARVIS_PID)"
    
    # Attendre et vérifier les ports
    sleep 8
    if curl -s http://localhost:8100 > /dev/null 2>&1; then
        echo "✅ Interface accessible sur http://localhost:8100"
        echo ""
        echo "🌐 Ouvrez votre navigateur sur: http://localhost:8100"
        echo "💬 Interface complète JARVIS prête !"
        echo ""
        echo "Appuyez sur Ctrl+C pour arrêter JARVIS"
        wait $JARVIS_PID
    elif curl -s http://localhost:8101 > /dev/null 2>&1; then
        echo "✅ Interface accessible sur http://localhost:8101"
        echo ""
        echo "🌐 Ouvrez votre navigateur sur: http://localhost:8101"
        echo "💬 Interface complète JARVIS prête !"
        echo ""
        echo "Appuyez sur Ctrl+C pour arrêter JARVIS"
        wait $JARVIS_PID
    else
        echo "❌ Interface complète non accessible"
    fi

else
    echo "❌ Aucune interface JARVIS trouvée"
    echo "📁 Fichiers disponibles:"
    ls -la jarvis*.py 2>/dev/null || echo "Aucun fichier jarvis*.py"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo ""
echo "🔄 JARVIS fermé"
echo "🛑 Nettoyage..."
kill $JARVIS_PID 2>/dev/null || true
pkill -f "jarvis" 2>/dev/null || true

echo "✅ Arrêt complet"
echo "👋 Au revoir Jean-Luc !"
read -p "Appuyez sur Entrée pour fermer..."
