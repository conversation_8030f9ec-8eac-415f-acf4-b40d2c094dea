import time
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS NLP ÉMOTIONNEL V2
Jean-Luc <PERSON>ave - 2025
Système NLP émotionnel hybrid selon les conseils de ChatGPT (grand frère)
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

class JarvisNLPEmotionnelV2:
    """Système NLP émotionnel hybrid pour JARVIS V2"""
    
    def __init__(self):
        self.nom_systeme = "JARVIS NLP Émotionnel V2"
        self.version = "2.0.0"
        
        # Dictionnaire émotionnel personnalisé (conseils ChatGPT)
        self.mots_cles_emotionnels = {
            'positif': {
                'joie': ['merci', 'génial', 'super', 'excellent', 'parfait', 'bravo', 'fantastique', 'magnifique'],
                'enthousiasme': ['wow', 'incroyable', 'extraordinaire', 'époustouflant', 'formidable', 'sensationnel'],
                'satisfaction': ['content', 'satisfait', 'heureux', 'ravi', 'enchanté', 'comblé'],
                'reconnaissance': ['merci', 'reconnaissant', 'grateful', 'apprécié', 'remercie']
            },
            'negatif': {
                'colere': ['nul', 'stupide', 'idiot', 'énervé', 'furieux', 'rage', 'merde', 'putain'],
                'frustration': ['comprends pas', 'problème', 'bug', 'erreur', 'marche pas', 'cassé', 'bloqué'],
                'tristesse': ['triste', 'déçu', 'malheureux', 'déprimé', 'abattu', 'découragé'],
                'inquietude': ['inquiet', 'soucieux', 'préoccupé', 'anxieux', 'stressé', 'tendu']
            },
            'neutre': {
                'curiosite': ['comment', 'pourquoi', 'qu\'est-ce', 'expliquer', 'comprendre', 'savoir'],
                'demande': ['peux-tu', 'pourrais-tu', 'aide-moi', 'montre-moi', 'fais', 'créer'],
                'information': ['info', 'détails', 'précisions', 'explications', 'documentation']
            }
        }
        
        # Patterns syntaxiques (conseils ChatGPT)
        self.patterns_syntaxiques = {
            'exclamatif': r'[!]{1,3}$',
            'interrogatif': r'\?+$',
            'affirmatif_fort': r'[.]{2,}$',
            'majuscules_emotion': r'[A-Z]{3,}',
            'repetition_lettres': r'([a-zA-Z])\1{2,}',  # ex: "suuuper"
            'emoticones': r'[😀-🙏]|:\)|:\(|:D|:P|;\)'
        }
        
        # Historique des analyses
        self.historique_analyses = []
        
        # Contexte conversationnel
        self.contexte_conversation = {
            'emotion_precedente': 'neutre',
            'intensite_precedente': 0.5,
            'sujet_actuel': None,
            'nb_messages_consecutifs': 0
        }
        
        # Charger les données
        self.load_nlp_data()
    
    def load_nlp_data(self):
        """Charge les données NLP"""
        try:
            if os.path.exists('jarvis_nlp_emotionnel_v2.json'):
                with open('jarvis_nlp_emotionnel_v2.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    self.historique_analyses = data.get('historique_analyses', [])
                    self.contexte_conversation = data.get('contexte_conversation', self.contexte_conversation)
                    
        except Exception as e:
            print(f"❌ Erreur chargement NLP: {e}")
    
    def save_nlp_data(self):
        """Sauvegarde les données NLP"""
        try:
            data = {
                'historique_analyses': self.historique_analyses,
                'contexte_conversation': self.contexte_conversation,
                'last_update': datetime.now().isoformat()
            }
            
            with open('jarvis_nlp_emotionnel_v2.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde NLP: {e}")
    
    def analyser_emotion_lexicale(self, message: str) -> Dict[str, Any]:
        """Analyse lexicale des émotions (méthode ChatGPT)"""
        
        message_lower = message.lower()
        emotions_detectees = defaultdict(float)
        mots_detectes = []
        
        # Parcourir le dictionnaire émotionnel
        for polarite, categories in self.mots_cles_emotionnels.items():
            for emotion, mots_cles in categories.items():
                for mot in mots_cles:
                    if mot in message_lower:
                        # Pondération selon la longueur du mot (plus précis)
                        poids = len(mot) / 10 + 0.5
                        emotions_detectees[emotion] += poids
                        mots_detectes.append(mot)
        
        # Normaliser les scores
        if emotions_detectees:
            max_score = max(emotions_detectees.values())
            emotions_detectees = {
                emotion: score / max_score 
                for emotion, score in emotions_detectees.items()
            }
        
        return {
            'emotions': dict(emotions_detectees),
            'mots_detectes': mots_detectes,
            'emotion_dominante': max(emotions_detectees, key=emotions_detectees.get) if emotions_detectees else 'neutre',
            'intensite': max(emotions_detectees.values()) if emotions_detectees else 0.0
        }
    
    def analyser_syntaxe(self, message: str) -> Dict[str, Any]:
        """Analyse syntaxique du ton (méthode ChatGPT)"""
        
        indicateurs_syntaxiques = {}
        intensite_syntaxique = 0.0
        
        # Vérifier les patterns
        for pattern_nom, pattern_regex in self.patterns_syntaxiques.items():
            matches = re.findall(pattern_regex, message)
            if matches:
                indicateurs_syntaxiques[pattern_nom] = len(matches)
                
                # Calculer l'intensité selon le pattern
                if pattern_nom == 'exclamatif':
                    intensite_syntaxique += 0.3 * len(matches)
                elif pattern_nom == 'majuscules_emotion':
                    intensite_syntaxique += 0.4 * len(matches)
                elif pattern_nom == 'repetition_lettres':
                    intensite_syntaxique += 0.2 * len(matches)
                elif pattern_nom == 'emoticones':
                    intensite_syntaxique += 0.3 * len(matches)
        
        # Analyser la structure des phrases
        nb_phrases = len([s for s in message.split('.') if s.strip()])
        nb_mots = len(message.split())
        
        # Déterminer le ton général
        if intensite_syntaxique > 0.5:
            ton = 'expressif'
        elif '?' in message:
            ton = 'interrogatif'
        elif nb_mots > 20 and nb_phrases > 2:
            ton = 'elabore'
        else:
            ton = 'neutre'
        
        return {
            'indicateurs': indicateurs_syntaxiques,
            'intensite_syntaxique': min(intensite_syntaxique, 1.0),
            'ton': ton,
            'nb_phrases': nb_phrases,
            'nb_mots': nb_mots
        }
    
    def analyser_contexte_conversationnel(self, message: str) -> Dict[str, Any]:
        """Analyse le contexte conversationnel"""
        
        # Détecter le sujet principal
        sujets_detectes = []
        mots_techniques = ['code', 'jarvis', 'ia', 'intelligence', 'système', 'programme', 'fonction']
        mots_personnels = ['je', 'moi', 'mon', 'ma', 'mes', 'nous', 'notre']
        
        message_lower = message.lower()
        
        for mot in mots_techniques:
            if mot in message_lower:
                sujets_detectes.append('technique')
                break
        
        for mot in mots_personnels:
            if mot in message_lower:
                sujets_detectes.append('personnel')
                break
        
        # Analyser la continuité
        if self.contexte_conversation['emotion_precedente'] != 'neutre':
            continuite_emotionnelle = True
        else:
            continuite_emotionnelle = False
        
        return {
            'sujets_detectes': sujets_detectes,
            'continuite_emotionnelle': continuite_emotionnelle,
            'longueur_message': len(message),
            'complexite': 'simple' if len(message.split()) < 10 else 'complexe'
        }
    
    def analyser_emotion_complete(self, message: str) -> Dict[str, Any]:
        """Analyse émotionnelle complète hybrid (méthode ChatGPT)"""
        
        print(f"🧠 Analyse NLP émotionnelle: {message[:50]}...")
        
        # 1. Analyse lexicale
        analyse_lexicale = self.analyser_emotion_lexicale(message)
        
        # 2. Analyse syntaxique
        analyse_syntaxique = self.analyser_syntaxe(message)
        
        # 3. Analyse contextuelle
        analyse_contextuelle = self.analyser_contexte_conversationnel(message)
        
        # 4. Fusion des analyses (algorithme hybrid)
        emotion_finale = self._fusionner_analyses(
            analyse_lexicale, analyse_syntaxique, analyse_contextuelle
        )
        
        # 5. Créer le rapport complet
        rapport_complet = {
            'timestamp': datetime.now().isoformat(),
            'message_original': message,
            'analyse_lexicale': analyse_lexicale,
            'analyse_syntaxique': analyse_syntaxique,
            'analyse_contextuelle': analyse_contextuelle,
            'emotion_finale': emotion_finale,
            'confiance': self._calculer_confiance(analyse_lexicale, analyse_syntaxique)
        }
        
        # 6. Mettre à jour le contexte
        self._mettre_a_jour_contexte(emotion_finale)
        
        # 7. Ajouter à l'historique
        self.historique_analyses.append(rapport_complet)
        
        # Garder seulement les 100 dernières analyses
        if len(self.historique_analyses) > 100:
            self.historique_analyses = self.historique_analyses[-100:]
        
        # Sauvegarder
        self.save_nlp_data()
        
        print(f"   🎯 Émotion détectée: {emotion_finale['emotion']} ({emotion_finale['intensite']:.1%})")
        print(f"   🔍 Confiance: {rapport_complet['confiance']:.1%}")
        
        return rapport_complet
    
    def _fusionner_analyses(self, lexicale: Dict, syntaxique: Dict, contextuelle: Dict) -> Dict[str, Any]:
        """Fusionne les différentes analyses (algorithme hybrid ChatGPT)"""
        
        # Pondération des sources
        poids_lexical = 0.6
        poids_syntaxique = 0.3
        poids_contextuel = 0.1
        
        # Émotion de base (lexicale)
        emotion_base = lexicale['emotion_dominante']
        intensite_base = lexicale['intensite']
        
        # Ajustement syntaxique
        intensite_syntaxique = syntaxique['intensite_syntaxique']
        intensite_finale = (intensite_base * poids_lexical + 
                           intensite_syntaxique * poids_syntaxique)
        
        # Ajustement contextuel
        if contextuelle['continuite_emotionnelle']:
            intensite_finale *= 1.2  # Amplifier si continuité émotionnelle
        
        if contextuelle['complexite'] == 'complexe':
            intensite_finale *= 0.9  # Réduire pour messages complexes
        
        # Normaliser
        intensite_finale = min(intensite_finale, 1.0)
        
        # Déterminer la polarité finale
        if emotion_base in ['joie', 'enthousiasme', 'satisfaction', 'reconnaissance']:
            polarite = 'positif'
        elif emotion_base in ['colere', 'frustration', 'tristesse', 'inquietude']:
            polarite = 'negatif'
        else:
            polarite = 'neutre'
        
        return {
            'emotion': emotion_base,
            'polarite': polarite,
            'intensite': intensite_finale,
            'ton_syntaxique': syntaxique['ton'],
            'mots_cles': lexicale['mots_detectes']
        }
    
    def _calculer_confiance(self, lexicale: Dict, syntaxique: Dict) -> float:
        """Calcule le niveau de confiance de l'analyse"""
        
        confiance = 0.5  # Base
        
        # Augmenter si mots-clés détectés
        if lexicale['mots_detectes']:
            confiance += 0.2 * len(lexicale['mots_detectes'])
        
        # Augmenter si indicateurs syntaxiques
        if syntaxique['indicateurs']:
            confiance += 0.1 * len(syntaxique['indicateurs'])
        
        # Augmenter si intensité élevée
        if lexicale['intensite'] > 0.7:
            confiance += 0.2
        
        return min(confiance, 1.0)
    
    def _mettre_a_jour_contexte(self, emotion_finale: Dict):
        """Met à jour le contexte conversationnel"""
        
        self.contexte_conversation['emotion_precedente'] = emotion_finale['emotion']
        self.contexte_conversation['intensite_precedente'] = emotion_finale['intensite']
        self.contexte_conversation['nb_messages_consecutifs'] += 1
    
    def generer_reponse_adaptee(self, emotion_detectee: Dict) -> Dict[str, str]:
        """Génère une réponse adaptée à l'émotion détectée"""
        
        emotion = emotion_detectee['emotion']
        intensite = emotion_detectee['intensite']
        polarite = emotion_detectee['polarite']
        
        # Adapter le style selon l'émotion
        if polarite == 'positif':
            if intensite > 0.7:
                style = 'enthousiaste'
                prefixe = "🎉 Génial ! "
                ton = "Je partage votre enthousiasme !"
            else:
                style = 'positif'
                prefixe = "😊 "
                ton = "C'est formidable !"
        
        elif polarite == 'negatif':
            if emotion == 'frustration':
                style = 'comprehensif'
                prefixe = "🤔 Je comprends... "
                ton = "Laissez-moi vous aider à résoudre cela."
            elif emotion == 'colere':
                style = 'apaisant'
                prefixe = "😌 "
                ton = "Je vais faire de mon mieux pour améliorer les choses."
            else:
                style = 'empathique'
                prefixe = "💙 "
                ton = "Je suis là pour vous accompagner."
        
        else:  # neutre
            style = 'professionnel'
            prefixe = "🤖 "
            ton = "Comment puis-je vous aider ?"
        
        return {
            'style': style,
            'prefixe': prefixe,
            'ton': ton,
            'adaptation': f"Réponse adaptée à l'émotion {emotion} (intensité {intensite:.1%})"
        }
    
    def get_rapport_nlp(self) -> Dict[str, Any]:
        """Génère un rapport NLP complet"""
        
        if not self.historique_analyses:
            return {'analyses_total': 0}
        
        # Statistiques émotionnelles
        emotions_comptees = defaultdict(int)
        polarites_comptees = defaultdict(int)
        
        for analyse in self.historique_analyses[-20:]:  # 20 dernières
            emotion = analyse['emotion_finale']['emotion']
            polarite = analyse['emotion_finale']['polarite']
            
            emotions_comptees[emotion] += 1
            polarites_comptees[polarite] += 1
        
        # Émotion dominante
        emotion_dominante = max(emotions_comptees, key=emotions_comptees.get) if emotions_comptees else 'neutre'
        
        # Confiance moyenne
        confidences = [a.get('confiance', 0.5) for a in self.historique_analyses[-10:]]
        confiance_moyenne = sum(confidences) / len(confidences) if confidences else 0.5
        
        return {
            'analyses_total': len(self.historique_analyses),
            'emotion_dominante_recente': emotion_dominante,
            'repartition_emotions': dict(emotions_comptees),
            'repartition_polarites': dict(polarites_comptees),
            'confiance_moyenne': confiance_moyenne,
            'contexte_actuel': self.contexte_conversation,
            'derniere_analyse': self.historique_analyses[-1] if self.historique_analyses else None
        }

def test_nlp_emotionnel_v2():
    """Test du système NLP émotionnel V2"""
    
    print("🧠 TEST NLP ÉMOTIONNEL V2 (CONSEILS CHATGPT)")
    print("=" * 60)
    print("👤 Jean-Luc Passave")
    print("🤖 Méthode hybrid du grand frère ChatGPT")
    print()
    
    # Créer le système NLP
    nlp = JarvisNLPEmotionnelV2()
    
    # Messages de test
    messages_test = [
        "Merci Claude, c'est génial ce que tu as fait !",
        "Je comprends pas pourquoi ça marche pas, c'est frustrant...",
        "Comment fonctionne le système d'intelligence thermique ?",
        "WOW ! C'est EXTRAORDINAIRE ce JARVIS !!!",
        "Peux-tu m'aider à corriger ce bug s'il te plaît ?",
        "Tu es vraiment nul, ça marche jamais tes trucs",
        "Super travail ! Je suis très satisfait du résultat 😊"
    ]
    
    print("📝 ANALYSE DES MESSAGES:")
    for i, message in enumerate(messages_test, 1):
        print(f"\n{i}. Message: \"{message}\"")
        
        # Analyser l'émotion
        analyse = nlp.analyser_emotion_complete(message)
        
        # Générer réponse adaptée
        reponse = nlp.generer_reponse_adaptee(analyse['emotion_finale'])
        
        print(f"   🎯 Émotion: {analyse['emotion_finale']['emotion']}")
        print(f"   📊 Polarité: {analyse['emotion_finale']['polarite']}")
        print(f"   📈 Intensité: {analyse['emotion_finale']['intensite']:.1%}")
        print(f"   🔍 Confiance: {analyse['confiance']:.1%}")
        print(f"   💬 Style réponse: {reponse['style']}")
        print(f"   🗣️ Ton suggéré: {reponse['ton']}")
    
    # Rapport final
    print(f"\n📊 RAPPORT NLP FINAL:")
    rapport = nlp.get_rapport_nlp()
    print(f"   📋 Analyses totales: {rapport['analyses_total']}")
    print(f"   🎯 Émotion dominante: {rapport['emotion_dominante_recente']}")
    print(f"   📊 Répartition: {rapport['repartition_emotions']}")
    print(f"   🔍 Confiance moyenne: {rapport['confiance_moyenne']:.1%}")
    
    print(f"\n✅ NLP ÉMOTIONNEL V2 TESTÉ!")
    print(f"🤖 Merci grand frère ChatGPT pour la méthode hybrid!")

if __name__ == "__main__":
    test_nlp_emotionnel_v2()
