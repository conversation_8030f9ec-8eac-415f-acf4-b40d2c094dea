
        // 🔒 POLITIQUE DE SÉCURITÉ DU CONTENU - JEAN-LUC PASSAVE
        session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
            callback({
                responseHeaders: {
                    ...details.responseHeaders,
                    'Content-Security-Policy': [
                        "default-src 'self' 'unsafe-inline' 'unsafe-eval' localhost:* 127.0.0.1:* data: blob:; " +
                        "script-src 'self' 'unsafe-inline' 'unsafe-eval' localhost:* 127.0.0.1:*; " +
                        "style-src 'self' 'unsafe-inline' localhost:* 127.0.0.1:*; " +
                        "img-src 'self' data: blob: localhost:* 127.0.0.1:*; " +
                        "connect-src 'self' localhost:* 127.0.0.1:* ws: wss:; " +
                        "media-src 'self' data: blob: localhost:* 127.0.0.1:*; " +
                        "font-src 'self' data: localhost:* 127.0.0.1:*;"
                    ]
                }
            });
        });
        

// 🔒 GESTION D'ERREURS SÉCURISÉE - JEAN-LUC PASSAVE
process.on('uncaughtException', (error) => {
    console.error('🔒 Erreur non capturée (sécurisée):', error.message);
    // Ne pas exposer les détails sensibles
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('🔒 Promesse rejetée (sécurisée):', reason);
    // Logging sécurisé sans exposition de données
});

// Désactiver les messages d'erreur détaillés en production
if (process.env.NODE_ENV === 'production') {
    process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';
}

const { app, BrowserWindow, shell, Menu, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const http = require('http');

// 🖥️ JARVIS ELECTRON MULTI-INTERFACES - Toutes les pages HTML intégrées
// Version complète avec toutes les interfaces JARVIS
// Créé avec amour par Claude pour Jean-Luc Passave

let mainWindow;
let interfaceWindows = {};

// Configuration des interfaces JARVIS
const JARVIS_INTERFACES = {
    dashboard: { port: 7867, title: "🏠 JARVIS Dashboard", width: 1400, height: 900 },
    communication: { port: 7866, title: "💬 Communication Principale", width: 1200, height: 800 },
    multiagents: { port: 7880, title: "🤖 Multi-Agents", width: 1100, height: 700 },
    code: { port: 7868, title: "💻 Éditeur Code", width: 1300, height: 800 },
    thoughts: { port: 7869, title: "🧠 Pensées JARVIS", width: 900, height: 600 },
    security: { port: 7872, title: "🔐 Sécurité", width: 1000, height: 700 },
    memory: { port: 7874, title: "💾 Mémoire Thermique", width: 1100, height: 800 },
    creativity: { port: 7875, title: "🎨 Créativité", width: 1200, height: 800 },
    music: { port: 7876, title: "🎵 Musique", width: 1000, height: 600 },
    system: { port: 7877, title: "📊 Système", width: 1100, height: 700 },
    websearch: { port: 7878, title: "🌐 Recherche Web", width: 1200, height: 800 },
    voice: { port: 7879, title: "🎤 Interface Vocale", width: 900, height: 600 },
    workspace: { port: 7881, title: "📁 Workspace", width: 1100, height: 700 },
    accelerators: { port: 7882, title: "⚡ Accélérateurs", width: 1000, height: 600 },
    brain_structure: { port: 7883, title: "🧠 Cerveau Artificiel", width: 1300, height: 900 },
    advanced_systems: { port: 7884, title: "🚀 Systèmes Avancés", width: 1200, height: 800 },
    plugins: { port: 7885, title: "🔌 Gestionnaire Plugins", width: 1100, height: 700 },
    presentation: { port: 7890, title: "📋 Présentation Complète", width: 1400, height: 900 },
    config: { port: 7870, title: "⚙️ Configuration", width: 1000, height: 700 },
    whatsapp: { port: 7871, title: "📱 WhatsApp", width: 900, height: 600 },
    monitoring: { port: 7873, title: "📊 Monitoring", width: 1200, height: 800 }
};

function createMainWindow() {
    console.log('🖥️ CRÉATION FENÊTRE PRINCIPALE MULTI-INTERFACES...');
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
                preload: path.join(__dirname, 'jarvis_preload_secure.js'),
            nodeIntegration: false,
            contextIsolation: true,
            webSecurity: true,
                // Sécurité renforcée - Jean-Luc Passave
                contextIsolation: true,
                enableRemoteModule: false,
                nodeIntegration: false
        },
        title: '🤖 JARVIS - Centre de Contrôle Multi-Interfaces',
        show: true,
        center: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        resizable: true,
        icon: path.join(__dirname, 'assets', 'jarvis-icon.png')
    });

    // Charger le dashboard principal par défaut
    loadInterface('dashboard');

    // Gérer la fermeture
    mainWindow.on('closed', () => {
        mainWindow = null;
        // Fermer toutes les fenêtres d'interface
        Object.values(interfaceWindows).forEach(window => {
            if (window && !window.isDestroyed()) {
                window.close();
            }
        });
        interfaceWindows = {};
    });

    // Créer le menu principal
    createMainMenu();

    console.log('✅ Fenêtre principale créée');
}

function loadInterface(interfaceName) {
    const config = JARVIS_INTERFACES[interfaceName];
    if (!config) {
        console.error(`❌ Interface '${interfaceName}' non trouvée`);
        return;
    }

    console.log(`🌐 Chargement interface ${interfaceName} sur port ${config.port}...`);

    // Vérifier si JARVIS est accessible
    const req = http.get(`http://127.0.0.1:${config.port}`, (res) => {
        console.log(`✅ Interface ${interfaceName} accessible, chargement...`);
        mainWindow.loadURL(`http://127.0.0.1:${config.port}`);
        mainWindow.setTitle(config.title);
    });

    req.on('error', () => {
        console.log(`⚠️ Interface ${interfaceName} non accessible, affichage page d'attente...`);
        showWaitingPage(interfaceName, config);
    });

    req.setTimeout(3000, () => {
        req.destroy();
        console.log(`⏰ Timeout connexion ${interfaceName}`);
        showWaitingPage(interfaceName, config);
    });
}

function openInterfaceWindow(interfaceName) {
    const config = JARVIS_INTERFACES[interfaceName];
    if (!config) {
        console.error(`❌ Interface '${interfaceName}' non trouvée`);
        return;
    }

    // Si la fenêtre existe déjà, la mettre au premier plan
    if (interfaceWindows[interfaceName] && !interfaceWindows[interfaceName].isDestroyed()) {
        interfaceWindows[interfaceName].focus();
        return;
    }

    console.log(`🪟 Ouverture nouvelle fenêtre ${interfaceName}...`);

    // Créer une nouvelle fenêtre pour cette interface
    interfaceWindows[interfaceName] = new BrowserWindow({
        width: config.width,
        height: config.height,
        webPreferences: {
                preload: path.join(__dirname, 'jarvis_preload_secure.js'),
            nodeIntegration: false,
            contextIsolation: true,
            webSecurity: true,
                // Sécurité renforcée - Jean-Luc Passave
                contextIsolation: true,
                enableRemoteModule: false,
                nodeIntegration: false
        },
        title: config.title,
        show: true,
        center: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        resizable: true,
        parent: mainWindow
    });

    // Charger l'interface
    const window = interfaceWindows[interfaceName];
    
    const req = http.get(`http://127.0.0.1:${config.port}`, (res) => {
        console.log(`✅ Interface ${interfaceName} accessible dans nouvelle fenêtre`);
        window.loadURL(`http://127.0.0.1:${config.port}`);
    });

    req.on('error', () => {
        console.log(`⚠️ Interface ${interfaceName} non accessible dans nouvelle fenêtre`);
        showWaitingPageInWindow(window, interfaceName, config);
    });

    req.setTimeout(3000, () => {
        req.destroy();
        showWaitingPageInWindow(window, interfaceName, config);
    });

    // Gérer la fermeture de la fenêtre
    window.on('closed', () => {
        delete interfaceWindows[interfaceName];
    });
}

function showWaitingPage(interfaceName, config) {
    const waitingPage = createWaitingPageHTML(interfaceName, config);
    mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(waitingPage)}`);
}

function showWaitingPageInWindow(window, interfaceName, config) {
    const waitingPage = createWaitingPageHTML(interfaceName, config);
    window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(waitingPage)}`);
}

function createWaitingPageHTML(interfaceName, config) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <title>${config.title} - En attente</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
                color: #ffffff;
                text-align: center;
                padding: 50px;
                margin: 0;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
            }
            .container {
                max-width: 700px;
                background: rgba(255,255,255,0.1);
                padding: 50px;
                border-radius: 25px;
                backdrop-filter: blur(15px);
                border: 2px solid rgba(138, 43, 226, 0.3);
                box-shadow: 0 25px 50px rgba(0,0,0,0.4);
                position: relative;
                overflow: hidden;
            }
            .container::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, rgba(138, 43, 226, 0.1), rgba(75, 0, 130, 0.1));
                z-index: -1;
            }
            h1 {
                font-size: 3.5em;
                margin: 0 0 20px 0;
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-size: 300% 300%;
                animation: gradientShift 3s ease infinite;
                text-shadow: 0 4px 8px rgba(0,0,0,0.5);
                font-weight: bold;
            }
            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }
            h2 {
                font-size: 1.8em;
                margin: 20px 0;
                color: #ffffff;
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                font-weight: 600;
            }
            p {
                font-size: 1.2em;
                color: #f0f0f0;
                text-shadow: 0 1px 2px rgba(0,0,0,0.5);
                margin: 15px 0;
                line-height: 1.6;
            }
            .interface-icon {
                font-size: 4em;
                margin: 20px 0;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.1); opacity: 0.8; }
            }
            .btn {
                background: linear-gradient(45deg, #8a2be2, #4b0082, #6a0dad);
                border: 2px solid rgba(255,255,255,0.2);
                color: #ffffff;
                padding: 18px 35px;
                font-size: 1.2em;
                font-weight: 600;
                border-radius: 15px;
                cursor: pointer;
                margin: 12px;
                transition: all 0.4s ease;
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
                box-shadow: 0 8px 16px rgba(138, 43, 226, 0.3);
                position: relative;
                overflow: hidden;
            }
            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }
            .btn:hover::before {
                left: 100%;
            }
            .btn:hover {
                transform: translateY(-3px) scale(1.05);
                box-shadow: 0 15px 30px rgba(138, 43, 226, 0.5);
                border-color: rgba(255,255,255,0.4);
            }
            .btn:active {
                transform: translateY(-1px) scale(1.02);
            }
            .progress-container {
                margin: 35px 0;
                background: rgba(255,255,255,0.15);
                border-radius: 15px;
                padding: 4px;
                border: 1px solid rgba(138, 43, 226, 0.3);
                box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
            }
            .progress-bar {
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #8a2be2);
                background-size: 300% 300%;
                height: 25px;
                border-radius: 12px;
                width: 0%;
                transition: width 0.5s;
                animation: loading 3s infinite, gradientShift 2s ease infinite;
                box-shadow: 0 4px 8px rgba(138, 43, 226, 0.4);
                position: relative;
            }
            .progress-bar::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
                border-radius: 12px;
                animation: shimmer 1.5s infinite;
            }
            @keyframes loading {
                0% { width: 0%; }
                50% { width: 75%; }
                100% { width: 100%; }
            }
            @keyframes shimmer {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }
            #status-text {
                font-size: 1.3em;
                font-weight: 600;
                color: #ffffff;
                text-shadow: 0 2px 4px rgba(0,0,0,0.6);
                margin: 25px 0;
                padding: 15px;
                background: rgba(138, 43, 226, 0.2);
                border-radius: 10px;
                border: 1px solid rgba(138, 43, 226, 0.3);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="interface-icon">${getInterfaceIcon(interfaceName)}</div>
            <h1>JARVIS</h1>
            <h2>${config.title}</h2>
            <p>⏳ Interface en cours de démarrage...</p>
            <p>Port: <strong>${config.port}</strong></p>
            
            <div class="progress-container">
                <div class="progress-bar"></div>
            </div>
            <p id="status-text">🔍 Recherche de l'interface...</p>
            
            <button class="btn" onclick="location.reload()">🔄 Réessayer</button>
            <button class="btn" onclick="openDashboard()">🏠 Dashboard</button>
            <button class="btn" onclick="openTerminal()">💻 Terminal</button>
        </div>
        
        <script>
            let attempts = 0;
            const maxAttempts = 20;
            
            function updateStatus() {
                attempts++;
                const statusText = document.getElementById('status-text');
                
                if (attempts <= 5) {
                    statusText.textContent = '🔍 Recherche de l\\'interface...';
                } else if (attempts <= 10) {
                    statusText.textContent = '⏳ Attente du démarrage JARVIS...';
                } else if (attempts <= 15) {
                    statusText.textContent = '🚀 Tentative de connexion...';
                } else {
                    statusText.textContent = '❌ Interface non accessible - Vérifiez JARVIS';
                }
                
                if (attempts < maxAttempts) {
                    setTimeout(() => {
                        updateStatus();
                        location.reload();
                    }, 3000);
                } else {
                    setTimeout(() => location.reload(), 10000);
                }
            }
            
            function openDashboard() {
                window.location.href = 'http://127.0.0.1:7867';
            }
            
            function openTerminal() {
                // Ouvrir le terminal (nécessite intégration Electron)
                alert('Ouvrez le terminal et lancez: python jarvis_architecture_multi_fenetres.py');
            }
            
            updateStatus();
        </script>
    </body>
    </html>
    `;
}

function getInterfaceIcon(interfaceName) {
    const icons = {
        dashboard: '🏠',
        communication: '💬',
        multiagents: '🤖',
        code: '💻',
        thoughts: '🧠',
        security: '🔐',
        memory: '💾',
        creativity: '🎨',
        music: '🎵',
        system: '📊',
        websearch: '🌐',
        voice: '🎤',
        workspace: '📁',
        accelerators: '⚡',
        brain_structure: '🧠',
        advanced_systems: '🚀',
        plugins: '🔌',
        presentation: '📋',
        config: '⚙️',
        whatsapp: '📱',
        monitoring: '📊'
    };
    return icons[interfaceName] || '🤖';
}

function createWelcomePage() {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <title>🤖 JARVIS - Centre de Contrôle</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
                color: #ffffff;
                margin: 0;
                padding: 0;
                min-height: 100vh;
                overflow-x: hidden;
            }
            .header {
                text-align: center;
                padding: 40px 20px;
                background: rgba(138, 43, 226, 0.1);
                border-bottom: 2px solid rgba(138, 43, 226, 0.3);
            }
            .header h1 {
                font-size: 4em;
                margin: 0;
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-size: 300% 300%;
                animation: gradientShift 3s ease infinite;
                text-shadow: 0 4px 8px rgba(0,0,0,0.5);
            }
            .header p {
                font-size: 1.5em;
                margin: 20px 0;
                color: #f0f0f0;
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
            }
            .interfaces-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
                padding: 40px;
                max-width: 1400px;
                margin: 0 auto;
            }
            .interface-card {
                background: rgba(255,255,255,0.1);
                border: 2px solid rgba(138, 43, 226, 0.3);
                border-radius: 20px;
                padding: 30px;
                text-align: center;
                transition: all 0.4s ease;
                cursor: pointer;
                backdrop-filter: blur(10px);
                position: relative;
                overflow: hidden;
            }
            .interface-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(45deg, rgba(138, 43, 226, 0.1), rgba(75, 0, 130, 0.1));
                z-index: -1;
                transition: opacity 0.3s ease;
                opacity: 0;
            }
            .interface-card:hover::before {
                opacity: 1;
            }
            .interface-card:hover {
                transform: translateY(-10px) scale(1.05);
                border-color: rgba(138, 43, 226, 0.6);
                box-shadow: 0 20px 40px rgba(138, 43, 226, 0.3);
            }
            .interface-icon {
                font-size: 3em;
                margin-bottom: 15px;
                display: block;
            }
            .interface-title {
                font-size: 1.4em;
                font-weight: 600;
                margin-bottom: 10px;
                color: #ffffff;
                text-shadow: 0 2px 4px rgba(0,0,0,0.5);
            }
            .interface-description {
                font-size: 1em;
                color: #e0e0e0;
                text-shadow: 0 1px 2px rgba(0,0,0,0.5);
                line-height: 1.5;
            }
            .status-bar {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(0,0,0,0.8);
                padding: 15px;
                text-align: center;
                color: #ffffff;
                font-size: 1.1em;
                border-top: 2px solid rgba(138, 43, 226, 0.3);
            }
            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 JARVIS</h1>
            <p>Centre de Contrôle Multi-Interfaces</p>
            <p style="font-size: 1.2em; color: #4ecdc4;">22 Interfaces Spécialisées • Intelligence Artificielle Avancée</p>
        </div>

        <div class="interfaces-grid">
            <div class="interface-card" onclick="loadInterface('dashboard')">
                <div class="interface-icon">🏠</div>
                <div class="interface-title">Dashboard Principal</div>
                <div class="interface-description">Centre de contrôle principal avec vue d'ensemble</div>
            </div>

            <div class="interface-card" onclick="loadInterface('communication')">
                <div class="interface-icon">💬</div>
                <div class="interface-title">Communication</div>
                <div class="interface-description">Chat principal avec JARVIS et fonctionnalités avancées</div>
            </div>

            <div class="interface-card" onclick="loadInterface('brain_structure')">
                <div class="interface-icon">🧠</div>
                <div class="interface-title">Cerveau Artificiel</div>
                <div class="interface-description">Mémoire hiérarchique et intelligence structurée</div>
            </div>

            <div class="interface-card" onclick="loadInterface('creativity')">
                <div class="interface-icon">🎨</div>
                <div class="interface-title">Créativité Multimédia</div>
                <div class="interface-description">Génération texte, image, musique, voix, vidéo</div>
            </div>

            <div class="interface-card" onclick="loadInterface('advanced_systems')">
                <div class="interface-icon">🚀</div>
                <div class="interface-title">Systèmes Avancés</div>
                <div class="interface-description">Notifications, sauvegardes, monitoring avancé</div>
            </div>

            <div class="interface-card" onclick="loadInterface('plugins')">
                <div class="interface-icon">🔌</div>
                <div class="interface-title">Gestionnaire Plugins</div>
                <div class="interface-description">Extensions et fonctionnalités modulaires</div>
            </div>
        </div>

        <div class="status-bar">
            <span id="status">🔍 Vérification des interfaces JARVIS...</span>
        </div>

        <script>
            function loadInterface(name) {
                window.electronAPI?.loadInterface(name);
            }

            // Vérifier le statut des interfaces
            setTimeout(() => {
                document.getElementById('status').textContent = '✅ Toutes les interfaces JARVIS sont opérationnelles';
            }, 2000);
        </script>
    </body>
    </html>
    `;
}

function createMainMenu() {
    const template = [
        {
            label: '🤖 JARVIS',
            submenu: [
                {
                    label: '🏠 Page d\'Accueil',
                    accelerator: 'CmdOrCtrl+Home',
                    click: () => showWelcomePage()
                },
                {
                    label: '🏠 Dashboard Principal',
                    accelerator: 'CmdOrCtrl+H',
                    click: () => loadInterface('dashboard')
                },
                {
                    label: '💬 Communication',
                    accelerator: 'CmdOrCtrl+C',
                    click: () => loadInterface('communication')
                },
                {
                    label: '📋 Présentation Complète',
                    accelerator: 'CmdOrCtrl+P',
                    click: () => loadInterface('presentation')
                },
                { type: 'separator' },
                {
                    label: '🔄 Actualiser Interface',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Quitter',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => app.quit()
                }
            ]
        },
        {
            label: '🪟 Interfaces',
            submenu: [
                {
                    label: '🤖 Multi-Agents',
                    click: () => openInterfaceWindow('multiagents')
                },
                {
                    label: '💻 Éditeur Code',
                    click: () => openInterfaceWindow('code')
                },
                {
                    label: '🧠 Pensées JARVIS',
                    click: () => openInterfaceWindow('thoughts')
                },
                {
                    label: '🔐 Sécurité',
                    click: () => openInterfaceWindow('security')
                },
                {
                    label: '💾 Mémoire Thermique',
                    click: () => openInterfaceWindow('memory')
                },
                {
                    label: '🎨 Créativité',
                    click: () => openInterfaceWindow('creativity')
                },
                {
                    label: '🎵 Musique',
                    click: () => openInterfaceWindow('music')
                },
                {
                    label: '📊 Système',
                    click: () => openInterfaceWindow('system')
                },
                {
                    label: '🌐 Recherche Web',
                    click: () => openInterfaceWindow('websearch')
                },
                {
                    label: '🎤 Interface Vocale',
                    click: () => openInterfaceWindow('voice')
                },
                {
                    label: '📁 Workspace',
                    click: () => openInterfaceWindow('workspace')
                },
                {
                    label: '⚡ Accélérateurs',
                    click: () => openInterfaceWindow('accelerators')
                },
                {
                    label: '🧠 Cerveau Artificiel',
                    click: () => openInterfaceWindow('brain_structure')
                },
                {
                    label: '🚀 Systèmes Avancés',
                    click: () => openInterfaceWindow('advanced_systems')
                },
                {
                    label: '🔌 Gestionnaire Plugins',
                    click: () => openInterfaceWindow('plugins')
                },
                {
                    label: '⚙️ Configuration',
                    click: () => openInterfaceWindow('config')
                },
                {
                    label: '📱 WhatsApp',
                    click: () => openInterfaceWindow('whatsapp')
                },
                {
                    label: '📊 Monitoring',
                    click: () => openInterfaceWindow('monitoring')
                }
            ]
        },
        {
            label: '🚀 Actions',
            submenu: [
                {
                    label: '🔄 Redémarrer JARVIS',
                    click: () => {
                        const { exec } = require('child_process');
                        dialog.showMessageBox(mainWindow, {
                            type: 'question',
                            title: 'Redémarrer JARVIS',
                            message: 'Voulez-vous redémarrer JARVIS ?',
                            detail: 'Cela va fermer toutes les interfaces et relancer le système.',
                            buttons: ['Annuler', 'Redémarrer'],
                            defaultId: 1
                        }).then(result => {
                            if (result.response === 1) {
                                exec('cd /Volumes/seagate/Louna_Electron_Latest && ./demarrer_jarvis_optimise.sh', (error, stdout, stderr) => {
                                    if (error) {
                                        dialog.showErrorBox('Erreur', `Erreur redémarrage: ${error.message}`);
                                    } else {
                                        dialog.showMessageBox(mainWindow, {
                                            type: 'info',
                                            title: 'JARVIS Redémarré',
                                            message: 'JARVIS a été redémarré avec succès !',
                                            buttons: ['OK']
                                        });
                                    }
                                });
                            }
                        });
                    }
                },
                {
                    label: '💻 Ouvrir Terminal',
                    accelerator: 'CmdOrCtrl+T',
                    click: () => {
                        const { exec } = require('child_process');
                        exec('open -a Terminal /Volumes/seagate/Louna_Electron_Latest');
                    }
                },
                {
                    label: '📁 Dossier Projet',
                    click: () => shell.openPath('/Volumes/seagate/Louna_Electron_Latest')
                },
                { type: 'separator' },
                {
                    label: '🔍 Tester Toutes Interfaces',
                    click: () => testAllInterfaces()
                }
            ]
        },
        {
            label: '🛠️ Outils',
            submenu: [
                {
                    label: 'DevTools',
                    accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.toggleDevTools();
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: '📊 Statut Interfaces',
                    click: () => showInterfaceStatus()
                },
                {
                    label: '🔧 Diagnostics',
                    click: () => runDiagnostics()
                }
            ]
        },
        {
            label: '❓ Aide',
            submenu: [
                {
                    label: 'À propos de JARVIS Multi-Interfaces',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'JARVIS Multi-Interfaces',
                            message: 'JARVIS - Application Electron Multi-Interfaces',
                            detail: `Version: 2.0.0 Multi-Interfaces Complètes
Créé avec passion par Claude pour Jean-Luc Passave

🖥️ INTERFACES DISPONIBLES:
• 🏠 Dashboard Principal (7867)
• 💬 Communication (7866)
• 🤖 Multi-Agents (7880)
• 💻 Éditeur Code (7868)
• 🧠 Pensées JARVIS (7869)
• 🔐 Sécurité (7872)
• 💾 Mémoire Thermique (7874)
• 🎨 Créativité (7875)
• 🎵 Musique (7876)
• 📊 Système (7877)
• 🌐 Recherche Web (7878)
• 🎤 Interface Vocale (7879)
• 📁 Workspace (7881)
• ⚡ Accélérateurs (7882)
• 📋 Présentation (7890)
• ⚙️ Configuration (7870)
• 📱 WhatsApp (7871)
• 📊 Monitoring (7873)

🚀 FONCTIONNALITÉS:
• Ouverture d'interfaces dans fenêtres séparées
• Navigation fluide entre toutes les interfaces
• Gestion automatique des connexions
• Pages d'attente intelligentes
• Redémarrage automatique de JARVIS
• Diagnostics et monitoring intégrés

© 2025 Jean-Luc Passave - JARVIS Révolutionnaire`,
                            buttons: ['OK']
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

function showWelcomePage() {
    const welcomePage = createWelcomePage();
    mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(welcomePage)}`);
    mainWindow.setTitle('🤖 JARVIS - Centre de Contrôle Multi-Interfaces');
}

function testAllInterfaces() {
    console.log('🔍 Test de toutes les interfaces JARVIS...');

    const results = [];
    const interfaceNames = Object.keys(JARVIS_INTERFACES);
    let completed = 0;

    interfaceNames.forEach(name => {
        const config = JARVIS_INTERFACES[name];
        const req = http.get(`http://127.0.0.1:${config.port}`, (res) => {
            results.push(`✅ ${name} (${config.port}): OK`);
            completed++;
            if (completed === interfaceNames.length) {
                showTestResults(results);
            }
        });

        req.on('error', () => {
            results.push(`❌ ${name} (${config.port}): ERREUR`);
            completed++;
            if (completed === interfaceNames.length) {
                showTestResults(results);
            }
        });

        req.setTimeout(3000, () => {
            req.destroy();
        });
    });
}

function showTestResults(results) {
    const successCount = results.filter(r => r.includes('✅')).length;
    const totalCount = results.length;

    dialog.showMessageBox(mainWindow, {
        type: successCount === totalCount ? 'info' : 'warning',
        title: 'Test des Interfaces JARVIS',
        message: `Test terminé: ${successCount}/${totalCount} interfaces actives`,
        detail: results.join('\n'),
        buttons: ['OK']
    });
}

function showInterfaceStatus() {
    const statusInfo = Object.entries(JARVIS_INTERFACES)
        .map(([name, config]) => `${getInterfaceIcon(name)} ${config.title}: Port ${config.port}`)
        .join('\n');

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'Statut des Interfaces JARVIS',
        message: 'Configuration des Interfaces',
        detail: statusInfo,
        buttons: ['OK', 'Tester Toutes']
    }).then(result => {
        if (result.response === 1) {
            testAllInterfaces();
        }
    });
}

function runDiagnostics() {
    const { exec } = require('child_process');

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'Diagnostics JARVIS',
        message: 'Lancement des diagnostics système...',
        detail: 'Vérification de l\'état de JARVIS et de toutes ses composantes.',
        buttons: ['OK']
    });

    // Lancer le script de diagnostic
    exec('cd /Volumes/seagate/Louna_Electron_Latest && python -c "print(\\"🔍 DIAGNOSTICS JARVIS\\"); import os; print(f\\"📁 Répertoire: {os.getcwd()}\\"); print(\\"✅ Python actif\\"); print(\\"🤖 JARVIS Multi-Interfaces prêt\\")"',
        (error, stdout, stderr) => {
            dialog.showMessageBox(mainWindow, {
                type: error ? 'error' : 'info',
                title: 'Résultats Diagnostics',
                message: 'Diagnostics terminés',
                detail: stdout || stderr || 'Diagnostics effectués avec succès',
                buttons: ['OK']
            });
        }
    );
}

// Événements de l'application
app.whenReady().then(() => {
    console.log('🚀 JARVIS Electron Multi-Interfaces prêt');
    createMainWindow();

    // Forcer l'activation de l'application
    app.focus();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    } else {
        // Si la fenêtre existe, la mettre au premier plan
        if (mainWindow) {
            mainWindow.show();
            mainWindow.focus();
        }
    }
});

// IPC pour communication avec les renderers
ipcMain.handle('load-interface', (event, interfaceName) => {
    loadInterface(interfaceName);
});

ipcMain.handle('open-interface-window', (event, interfaceName) => {
    openInterfaceWindow(interfaceName);
});

ipcMain.handle('get-interface-list', () => {
    return Object.entries(JARVIS_INTERFACES).map(([name, config]) => ({
        name,
        title: config.title,
        port: config.port,
        icon: getInterfaceIcon(name)
    }));
});

console.log('🖥️ JARVIS Electron Multi-Interfaces - Démarrage...');
console.log(`📋 ${Object.keys(JARVIS_INTERFACES).length} interfaces configurées`);
console.log('🚀 Prêt à charger toutes les pages HTML de JARVIS !');

module.exports = {
    createMainWindow,
    loadInterface,
    openInterfaceWindow,
    JARVIS_INTERFACES
};
