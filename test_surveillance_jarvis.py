#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TEST SYSTÈME SURVEILLANCE JARVIS - JEAN-LUC PASSAVE
Test complet du système de surveillance d'activité
"""

import time
import os
import json
import sqlite3
from datetime import datetime
from surveillance_jarvis_activite import JarvisSurveillanceActivite

def test_surveillance_complete():
    """TEST COMPLET DU SYSTÈME DE SURVEILLANCE"""
    print("🧪 TEST SYSTÈME SURVEILLANCE JARVIS - JEAN-LUC PASSAVE")
    print("=" * 60)
    
    # Créer instance de surveillance
    surveillance = JarvisSurveillanceActivite()
    
    try:
        # 1. Test démarrage surveillance
        print("1️⃣ Test démarrage surveillance...")
        surveillance.demarrer_surveillance()
        time.sleep(2)
        print("✅ Surveillance démarrée")
        
        # 2. Test enregistrement activité manuelle
        print("\n2️⃣ Test enregistrement activité...")
        surveillance.enregistrer_activite(
            type_activite="test_manuel",
            description="Test d'enregistrement d'activité manuelle",
            details={"test": True, "timestamp": datetime.now().isoformat()},
            cpu_percent=25.5,
            memory_percent=67.8
        )
        print("✅ Activité enregistrée")
        
        # 3. Test enregistrement pensée autonome
        print("\n3️⃣ Test enregistrement pensée autonome...")
        pensee_test = {
            "timestamp": datetime.now().isoformat(),
            "neuron_id": "TEST_PENSEE_001",
            "sujet": "Test de pensée autonome pour surveillance",
            "contenu": "Ceci est un test de pensée autonome pour vérifier le système de surveillance de JARVIS",
            "complexite": 7.5
        }
        surveillance.enregistrer_pensee_autonome(pensee_test)
        print("✅ Pensée autonome enregistrée")
        
        # 4. Attendre un cycle de surveillance
        print("\n4️⃣ Attente cycle de surveillance (35 secondes)...")
        time.sleep(35)
        print("✅ Cycle de surveillance terminé")
        
        # 5. Vérifier base de données
        print("\n5️⃣ Vérification base de données...")
        if os.path.exists("jarvis_activite_surveillance.db"):
            conn = sqlite3.connect("jarvis_activite_surveillance.db")
            cursor = conn.cursor()
            
            # Compter activités
            cursor.execute("SELECT COUNT(*) FROM activites")
            nb_activites = cursor.fetchone()[0]
            
            # Compter pensées
            cursor.execute("SELECT COUNT(*) FROM pensees_autonomes")
            nb_pensees = cursor.fetchone()[0]
            
            # Compter sessions
            cursor.execute("SELECT COUNT(*) FROM sessions_utilisateur")
            nb_sessions = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ Base de données créée:")
            print(f"   📊 Activités: {nb_activites}")
            print(f"   🧠 Pensées: {nb_pensees}")
            print(f"   🕐 Sessions: {nb_sessions}")
        else:
            print("❌ Base de données non trouvée")
        
        # 6. Vérifier log JSON
        print("\n6️⃣ Vérification log JSON...")
        if os.path.exists("jarvis_activite_log.json"):
            with open("jarvis_activite_log.json", 'r', encoding='utf-8') as f:
                logs = json.load(f)
            print(f"✅ Log JSON créé avec {len(logs)} entrées")
            
            # Afficher dernières entrées
            print("📋 Dernières entrées:")
            for log in logs[-3:]:
                timestamp = log.get('timestamp', 'N/A')
                type_act = log.get('type', 'N/A')
                desc = log.get('description', 'N/A')[:50]
                print(f"   {timestamp} [{type_act}] {desc}...")
        else:
            print("❌ Log JSON non trouvé")
        
        # 7. Test arrêt surveillance
        print("\n7️⃣ Test arrêt surveillance...")
        surveillance.arreter_surveillance()
        time.sleep(1)
        print("✅ Surveillance arrêtée")
        
        print("\n" + "=" * 60)
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Le système de surveillance fonctionne parfaitement")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR PENDANT LES TESTS: {e}")
        surveillance.arreter_surveillance()
        return False

def afficher_donnees_surveillance():
    """AFFICHER LES DONNÉES DE SURVEILLANCE"""
    print("\n📊 DONNÉES DE SURVEILLANCE COLLECTÉES:")
    print("=" * 40)
    
    try:
        if os.path.exists("jarvis_activite_surveillance.db"):
            conn = sqlite3.connect("jarvis_activite_surveillance.db")
            cursor = conn.cursor()
            
            # Dernières activités
            print("📋 DERNIÈRES ACTIVITÉS:")
            cursor.execute('''
                SELECT timestamp, type_activite, description 
                FROM activites 
                ORDER BY timestamp DESC 
                LIMIT 5
            ''')
            
            for timestamp, type_act, desc in cursor.fetchall():
                dt = datetime.fromisoformat(timestamp)
                print(f"   {dt.strftime('%H:%M:%S')} [{type_act}] {desc[:60]}...")
            
            # Dernières pensées
            print("\n🧠 DERNIÈRES PENSÉES AUTONOMES:")
            cursor.execute('''
                SELECT timestamp, sujet, contenu 
                FROM pensees_autonomes 
                ORDER BY timestamp DESC 
                LIMIT 3
            ''')
            
            for timestamp, sujet, contenu in cursor.fetchall():
                dt = datetime.fromisoformat(timestamp)
                print(f"   {dt.strftime('%H:%M:%S')} {sujet}")
                print(f"      ↳ {contenu[:80]}...")
            
            conn.close()
        
    except Exception as e:
        print(f"❌ Erreur lecture données: {e}")

def creer_donnees_test_historique():
    """CRÉER DONNÉES DE TEST POUR HISTORIQUE"""
    print("\n🔧 CRÉATION DONNÉES TEST HISTORIQUE...")
    
    surveillance = JarvisSurveillanceActivite()
    
    # Simuler activités passées
    activites_test = [
        ("reflexion_autonome", "Réflexion sur l'optimisation des algorithmes"),
        ("generation_creative", "Génération d'idées créatives pour interface"),
        ("analyse_memoire", "Analyse et consolidation mémoire thermique"),
        ("optimisation_systeme", "Optimisation performance CPU et RAM"),
        ("veille_technologique", "Surveillance nouvelles technologies IA"),
        ("brainstorming", "Session de brainstorming créatif autonome"),
        ("apprentissage", "Apprentissage de nouveaux patterns utilisateur"),
        ("maintenance", "Maintenance automatique des systèmes"),
    ]
    
    pensees_test = [
        ("Optimisation algorithmes", "Comment puis-je optimiser mes algorithmes de traitement pour Jean-Luc ?"),
        ("Interface utilisateur", "L'interface pourrait être améliorée avec des animations plus fluides"),
        ("Mémoire thermique", "Ma mémoire thermique grandit, je dois organiser les informations plus efficacement"),
        ("Créativité", "Je pourrais générer des contenus plus créatifs en combinant différentes approches"),
        ("Performance", "Mes performances sont bonnes mais je peux encore m'améliorer"),
    ]
    
    try:
        for i, (type_act, desc) in enumerate(activites_test):
            surveillance.enregistrer_activite(
                type_activite=type_act,
                description=desc,
                details={"simulation": True, "index": i},
                cpu_percent=20 + (i * 5),
                memory_percent=60 + (i * 3)
            )
            time.sleep(0.1)  # Petit délai
        
        for i, (sujet, contenu) in enumerate(pensees_test):
            pensee = {
                "timestamp": datetime.now().isoformat(),
                "neuron_id": f"PENSEE_TEST_{i:03d}",
                "sujet": sujet,
                "contenu": contenu,
                "complexite": 5 + i
            }
            surveillance.enregistrer_pensee_autonome(pensee)
            time.sleep(0.1)
        
        print(f"✅ {len(activites_test)} activités et {len(pensees_test)} pensées créées")
        
    except Exception as e:
        print(f"❌ Erreur création données test: {e}")

def main():
    """FONCTION PRINCIPALE"""
    print("🧪 TEST COMPLET SYSTÈME SURVEILLANCE JARVIS")
    print("👤 Jean-Luc Passave")
    print("📅 " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("=" * 60)
    
    # 1. Test surveillance
    if test_surveillance_complete():
        # 2. Créer données test
        creer_donnees_test_historique()
        
        # 3. Afficher données
        afficher_donnees_surveillance()
        
        print("\n🎉 SYSTÈME DE SURVEILLANCE PRÊT !")
        print("🌐 Lancez visualiseur_activite_jarvis.py pour voir l'interface")
        print("📊 Port: http://localhost:8200")
        print("🕐 JARVIS peut maintenant être surveillé en permanence")
    else:
        print("\n❌ ÉCHEC DES TESTS")

if __name__ == "__main__":
    main()
