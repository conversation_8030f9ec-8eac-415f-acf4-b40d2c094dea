#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS CONFIGURATION SYSTÈME - JEAN-LUC PASSAVE
Interface de configuration avec contrôles audio et navigation
"""

import gradio as gr
import requests
import datetime

def test_deepseek_connection():
    """Test de connexion DeepSeek R1 8B"""
    try:
        response = requests.post("http://localhost:8000/v1/chat/completions", 
                               json={"messages": [{"role": "user", "content": "Test"}], "max_tokens": 5}, 
                               timeout=5)
        if response.status_code == 200:
            return True, "✅ DeepSeek R1 8B opérationnel"
        else:
            return False, f"❌ Erreur: {response.status_code}"
    except:
        return False, "❌ DeepSeek R1 8B non accessible"

def create_config_status(message):
    """Crée l'affichage de statut pour la configuration"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    
    return f"""
    <div style="background: linear-gradient(135deg, #e8f5e8, #d4edda); padding: 20px; border-radius: 15px; border-left: 6px solid #28a745; margin: 10px 0;">
        <div style="text-align: center; margin-bottom: 15px;">
            <h3 style="color: #28a745; margin: 0; font-size: 1.4em;">⚙️ CONFIGURATION SYSTÈME JARVIS</h3>
            <p style="margin: 5px 0; color: #666;">Contrôles Audio et Navigation - Jean-Luc Passave</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin: 10px 0; border-left: 3px solid #28a745;">
            <strong style="color: #28a745; font-size: 1.1em;">📊 Statut [{current_time}]:</strong>
            <div style="margin-top: 5px;">{message}</div>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #ffc107;">
            <h4 style="color: #856404; margin: 0 0 10px 0;">🎹 Raccourcis Clavier Audio</h4>
            <div style="font-size: 0.9em; line-height: 1.4;">
                <div style="margin: 5px 0;"><strong>Ctrl + R</strong> : Écouter dernière réponse JARVIS</div>
                <div style="margin: 5px 0;"><strong>Ctrl + S</strong> : Arrêter lecture audio</div>
                <div style="margin: 5px 0;"><strong>Ctrl + A</strong> : Test audio rapide</div>
                <div style="margin: 5px 0;"><strong>Ctrl + H</strong> : Retour accueil</div>
            </div>
        </div>
        
        <div style="background: #d1ecf1; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #17a2b8;">
            <h4 style="color: #0c5460; margin: 0 0 10px 0;">🏠 Navigation Aller-Retour</h4>
            <div style="font-size: 0.9em; line-height: 1.4;">
                <div style="margin: 5px 0;">🏠 <strong>Accueil</strong> : Interface principale (localhost:7866)</div>
                <div style="margin: 5px 0;">↩️ <strong>Dernier Onglet</strong> : Dernière interface visitée</div>
                <div style="margin: 5px 0;">🔊 <strong>Audio Complet</strong> : Interface audio (localhost:8111)</div>
                <div style="margin: 5px 0;">⚙️ <strong>Configuration</strong> : Cette interface</div>
            </div>
        </div>
    </div>
    
    <script>
    // CONTRÔLES AUDIO JARVIS - JEAN-LUC PASSAVE
    function speakText(text) {{
        if ('speechSynthesis' in window) {{
            window.speechSynthesis.cancel();
            
            const savedVolume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
            const savedSpeed = parseFloat(localStorage.getItem('jarvis_speed') || '0.85');
            const savedPitch = parseFloat(localStorage.getItem('jarvis_pitch') || '0.8');
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = savedSpeed;
            utterance.pitch = savedPitch;
            utterance.volume = savedVolume;
            
            const voices = window.speechSynthesis.getVoices();
            const frenchVoice = voices.find(voice => 
                voice.lang.includes('fr') || 
                voice.name.toLowerCase().includes('french')
            );
            
            if (frenchVoice) {{
                utterance.voice = frenchVoice;
            }}
            
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée');
        }}
    }}
    
    // NAVIGATION ALLER-RETOUR
    function goToHome() {{
        localStorage.setItem('jarvis_last_url', window.location.href);
        window.location.href = 'http://localhost:7866';
    }}

    function goToLastTab() {{
        const lastUrl = localStorage.getItem('jarvis_last_url');
        if (lastUrl && lastUrl !== window.location.href) {{
            window.location.href = lastUrl;
        }} else {{
            window.location.href = 'http://localhost:7866';
        }}
    }}
    
    // RACCOURCIS CLAVIER
    document.addEventListener('keydown', function(event) {{
        if (event.ctrlKey && event.key === 'r') {{
            event.preventDefault();
            speakText('Test lecture dernière réponse JARVIS depuis configuration système.');
        }}
        if (event.ctrlKey && event.key === 's') {{
            event.preventDefault();
            window.speechSynthesis.cancel();
        }}
        if (event.ctrlKey && event.key === 'a') {{
            event.preventDefault();
            speakText('Test audio depuis configuration système JARVIS. Tous les contrôles sont opérationnels.');
        }}
        if (event.ctrlKey && event.key === 'h') {{
            event.preventDefault();
            goToHome();
        }}
    }});
    
    console.log('⚙️ Configuration Système JARVIS initialisée');
    </script>
    """

def create_jarvis_config_interface():
    """Interface de configuration système JARVIS"""
    
    with gr.Blocks(
        title="⚙️ JARVIS Configuration Système",
        theme=gr.themes.Soft(),
        css="""
        .config-btn {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.2em !important;
            padding: 15px 25px !important;
            border-radius: 20px !important;
            box-shadow: 0 6px 15px rgba(40, 167, 69, 0.3) !important;
            transition: all 0.3s ease !important;
        }
        .config-btn:hover {
            background: linear-gradient(45deg, #20c997, #17a2b8) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4) !important;
        }
        """
    ) as interface:
        
        # Header avec Navigation
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <button onclick="goToHome()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold;">
                    🏠 ACCUEIL
                </button>
                <div style="text-align: center;">
                    <h1 style="margin: 0; font-size: 2.2em;">⚙️ Configuration Système</h1>
                </div>
                <button onclick="goToLastTab()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold;">
                    ↩️ DERNIER ONGLET
                </button>
            </div>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Contrôles Audio et Navigation - Jean-Luc Passave</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                # CONTRÔLES AUDIO
                gr.HTML("<h3 style='color: #28a745; text-align: center;'>🔊 CONTRÔLES AUDIO JARVIS</h3>")
                
                with gr.Row():
                    volume_slider = gr.Slider(
                        minimum=0.1,
                        maximum=1.0,
                        value=1.0,
                        step=0.1,
                        label="🔊 Volume JARVIS",
                        scale=2
                    )
                    speed_slider = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        value=0.85,
                        step=0.05,
                        label="⚡ Vitesse",
                        scale=1
                    )
                
                with gr.Row():
                    pitch_slider = gr.Slider(
                        minimum=0.5,
                        maximum=2.0,
                        value=0.8,
                        step=0.1,
                        label="🎵 Tonalité",
                        scale=2
                    )
                    save_settings_btn = gr.Button("💾 Sauvegarder", variant="primary", scale=1)
                
                # BOUTONS AUDIO
                with gr.Row():
                    test_audio_btn = gr.Button("🎧 TEST AUDIO COMPLET", elem_classes=["config-btn"])
                    stop_audio_btn = gr.Button("⏹️ ARRÊTER AUDIO", elem_classes=["config-btn"])
                
                # NAVIGATION
                gr.HTML("<h3 style='color: #17a2b8; text-align: center; margin-top: 30px;'>🏠 NAVIGATION ALLER-RETOUR</h3>")
                
                with gr.Row():
                    home_btn = gr.Button("🏠 INTERFACE PRINCIPALE", variant="primary")
                    audio_btn = gr.Button("🔊 AUDIO COMPLET", variant="secondary")
                    last_tab_btn = gr.Button("↩️ DERNIER ONGLET", variant="secondary")
            
            with gr.Column(scale=1):
                # Zone de statut
                status_display = gr.HTML(create_config_status("Configuration système JARVIS prête"))
                
                # Diagnostic
                gr.HTML("""
                <div style="background: #f8d7da; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin: 0 0 10px 0;">🔧 Diagnostic Système</h4>
                    <div style="font-size: 0.9em; line-height: 1.4;">
                        <div style="margin: 5px 0;">🤖 <strong>Agent DeepSeek</strong> : localhost:8000</div>
                        <div style="margin: 5px 0;">🔊 <strong>Audio Principal</strong> : localhost:7866</div>
                        <div style="margin: 5px 0;">🎛️ <strong>Audio Complet</strong> : localhost:8111</div>
                        <div style="margin: 5px 0;">⚙️ <strong>Configuration</strong> : Cette interface</div>
                    </div>
                </div>
                """)
        
        # Fonctions
        def save_audio_settings(volume, speed, pitch):
            return create_config_status(f"Paramètres audio sauvegardés - Volume: {int(volume*100)}%, Vitesse: {speed:.2f}, Tonalité: {pitch:.1f}")
        
        def test_audio_complete():
            return create_config_status("Test audio complet lancé depuis configuration système")
        
        def stop_audio():
            return create_config_status("Audio JARVIS arrêté depuis configuration système")

        # Connexions
        save_settings_btn.click(
            fn=save_audio_settings,
            inputs=[volume_slider, speed_slider, pitch_slider],
            outputs=[status_display],
            js="(volume, speed, pitch) => { localStorage.setItem('jarvis_volume', volume); localStorage.setItem('jarvis_speed', speed); localStorage.setItem('jarvis_pitch', pitch); console.log('💾 Paramètres sauvegardés:', volume, speed, pitch); }"
        )
        
        test_audio_btn.click(
            fn=test_audio_complete,
            outputs=[status_display],
            js="() => speakText('Test audio complet depuis configuration système JARVIS. Bonjour Jean-Luc, tous les contrôles audio sont parfaitement opérationnels. Volume, vitesse et tonalité configurables.')"
        )
        
        stop_audio_btn.click(
            fn=stop_audio,
            outputs=[status_display],
            js="() => { window.speechSynthesis.cancel(); console.log('⏹️ Audio arrêté depuis config'); }"
        )
        
        home_btn.click(
            fn=lambda: create_config_status("Redirection vers interface principale"),
            outputs=[status_display],
            js="() => { localStorage.setItem('jarvis_last_url', window.location.href); window.location.href = 'http://localhost:7866'; }"
        )
        
        audio_btn.click(
            fn=lambda: create_config_status("Ouverture interface audio complète"),
            outputs=[status_display],
            js="() => { localStorage.setItem('jarvis_last_url', window.location.href); window.location.href = 'http://localhost:8111'; }"
        )
        
        last_tab_btn.click(
            fn=lambda: create_config_status("Ouverture dernier onglet visité"),
            outputs=[status_display],
            js="() => goToLastTab()"
        )
    
    return interface

if __name__ == "__main__":
    print("⚙️ JARVIS CONFIGURATION SYSTÈME - JEAN-LUC PASSAVE")
    print("=" * 60)
    print("🔊 Contrôles audio: Volume, vitesse, tonalité")
    print("🏠 Navigation: Boutons aller-retour")
    print("🎹 Raccourcis: Ctrl+R, Ctrl+S, Ctrl+A, Ctrl+H")
    print("🔧 Diagnostic: Statut connexions")
    print("🌐 Interface disponible sur localhost:8114")
    print("=" * 60)
    
    interface = create_jarvis_config_interface()
    interface.launch(
        server_name="localhost",
        server_port=8114,
        share=False,
        debug=True,
        show_error=True
    )
