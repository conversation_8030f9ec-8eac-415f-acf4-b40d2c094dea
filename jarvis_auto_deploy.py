#!/usr/bin/env python3
"""
🚀 JARVIS AUTO DEPLOY - JEAN-LUC PASSAVE
Système de déploiement automatique avec validation et solidification
"""

import os
import sys
import time
import subprocess
from pathlib import Path
from jarvis_auto_validator import JarvisAutoValidator
from jarvis_code_solidifier import JarvisCodeSolidifier

class JarvisAutoDeployer:
    """Déployeur automatique JARVIS"""
    
    def __init__(self, base_path="/Volumes/seagate/Louna_Electron_Latest"):
        self.base_path = Path(base_path)
        self.validator = JarvisAutoValidator(base_path)
        self.solidifier = JarvisCodeSolidifier(base_path)
        
        self.deployment_log = self.base_path / "deployment.log"
    
    def log(self, message):
        """Logger les messages"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        with open(self.deployment_log, 'a') as f:
            f.write(log_message + "\n")
    
    def stop_running_processes(self):
        """Arrêter les processus JARVIS en cours"""
        self.log("🛑 Arrêt des processus JARVIS en cours...")
        
        try:
            # Arrêter les processus Python JARVIS
            result = subprocess.run(
                ["pkill", "-f", "jarvis_architecture_multi_fenetres.py"],
                capture_output=True, text=True
            )
            
            # Arrêter les processus Electron JARVIS
            result = subprocess.run(
                ["pkill", "-f", "jarvis_electron_nouveau.js"],
                capture_output=True, text=True
            )
            
            # Libérer les ports
            for port in [7867, 7866, 8765]:
                subprocess.run(
                    ["lsof", "-ti", f":{port}"],
                    capture_output=True, text=True
                )
                subprocess.run(
                    f"lsof -ti:{port} | xargs kill -9",
                    shell=True, capture_output=True
                )
            
            self.log("✅ Processus arrêtés et ports libérés")
            time.sleep(2)  # Attendre que tout se termine
            
        except Exception as e:
            self.log(f"⚠️ Erreur arrêt processus: {e}")
    
    def validate_code(self):
        """Valider le code avant déploiement"""
        self.log("🔍 Validation du code...")
        
        validation_success = self.validator.run_full_validation()
        
        if validation_success:
            self.log("✅ Validation réussie")
            return True
        else:
            self.log("❌ Validation échouée")
            return False
    
    def solidify_code(self):
        """Solidifier le code validé"""
        self.log("🔒 Solidification du code...")
        
        try:
            backup_dir = self.solidifier.create_solid_backup("DEPLOYED_VERSION")
            self.log(f"✅ Code solidifié: {backup_dir}")
            return True
        except Exception as e:
            self.log(f"❌ Erreur solidification: {e}")
            return False
    
    def start_python_backend(self):
        """Démarrer le backend Python"""
        self.log("🐍 Démarrage backend Python...")
        
        try:
            # Changer vers le répertoire de travail
            os.chdir(self.base_path)
            
            # Démarrer le backend Python en arrière-plan
            python_process = subprocess.Popen(
                [sys.executable, "jarvis_architecture_multi_fenetres.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.base_path
            )
            
            # Attendre un peu pour voir si ça démarre
            time.sleep(5)
            
            if python_process.poll() is None:
                self.log("✅ Backend Python démarré")
                return python_process
            else:
                stdout, stderr = python_process.communicate()
                self.log(f"❌ Backend Python échoué: {stderr.decode()}")
                return None
                
        except Exception as e:
            self.log(f"❌ Erreur démarrage Python: {e}")
            return None
    
    def start_electron_frontend(self):
        """Démarrer le frontend Electron"""
        self.log("⚡ Démarrage frontend Electron...")
        
        try:
            # Vérifier que npm est disponible
            subprocess.run(["npm", "--version"], check=True, capture_output=True)
            
            # Démarrer Electron
            electron_process = subprocess.Popen(
                ["npm", "start"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.base_path
            )
            
            # Attendre un peu pour voir si ça démarre
            time.sleep(3)
            
            if electron_process.poll() is None:
                self.log("✅ Frontend Electron démarré")
                return electron_process
            else:
                stdout, stderr = electron_process.communicate()
                self.log(f"❌ Frontend Electron échoué: {stderr.decode()}")
                return None
                
        except Exception as e:
            self.log(f"❌ Erreur démarrage Electron: {e}")
            return None
    
    def verify_deployment(self):
        """Vérifier que le déploiement fonctionne"""
        self.log("🔍 Vérification du déploiement...")
        
        import requests
        
        # Tester les endpoints
        endpoints = [
            "http://localhost:7867",
            "http://localhost:7866"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, timeout=5)
                if response.status_code == 200:
                    self.log(f"✅ {endpoint} répond")
                else:
                    self.log(f"⚠️ {endpoint} code {response.status_code}")
            except Exception as e:
                self.log(f"❌ {endpoint} inaccessible: {e}")
                return False
        
        self.log("✅ Déploiement vérifié")
        return True
    
    def deploy(self):
        """Déploiement complet automatique"""
        self.log("🚀 DÉMARRAGE DÉPLOIEMENT AUTOMATIQUE JARVIS")
        self.log("=" * 50)
        
        # Étape 1: Arrêter les processus existants
        self.stop_running_processes()
        
        # Étape 2: Valider le code
        if not self.validate_code():
            self.log("❌ DÉPLOIEMENT ANNULÉ - Validation échouée")
            return False
        
        # Étape 3: Solidifier le code
        if not self.solidify_code():
            self.log("❌ DÉPLOIEMENT ANNULÉ - Solidification échouée")
            return False
        
        # Étape 4: Démarrer le backend Python
        python_process = self.start_python_backend()
        if not python_process:
            self.log("❌ DÉPLOIEMENT ANNULÉ - Backend Python échoué")
            return False
        
        # Étape 5: Démarrer le frontend Electron
        electron_process = self.start_electron_frontend()
        if not electron_process:
            self.log("❌ DÉPLOIEMENT ANNULÉ - Frontend Electron échoué")
            # Arrêter le backend Python
            python_process.terminate()
            return False
        
        # Étape 6: Vérifier le déploiement
        time.sleep(10)  # Attendre que tout soit prêt
        if not self.verify_deployment():
            self.log("⚠️ Déploiement partiellement réussi")
        
        self.log("🎉 DÉPLOIEMENT AUTOMATIQUE RÉUSSI !")
        self.log("🔒 Code validé, solidifié et déployé")
        self.log(f"🐍 Backend Python PID: {python_process.pid}")
        self.log(f"⚡ Frontend Electron PID: {electron_process.pid}")
        
        return True
    
    def rollback(self):
        """Rollback vers la dernière version solidifiée"""
        self.log("🔄 ROLLBACK vers version solidifiée...")
        
        # Arrêter les processus
        self.stop_running_processes()
        
        # Restaurer la dernière version solidifiée
        if self.solidifier.restore_solid_version():
            self.log("✅ Version solidifiée restaurée")
            
            # Redémarrer
            return self.deploy()
        else:
            self.log("❌ Rollback échoué")
            return False

def main():
    """Fonction principale"""
    deployer = JarvisAutoDeployer()
    
    print("🚀 JARVIS AUTO DEPLOY - JEAN-LUC PASSAVE")
    print("Déploiement automatique avec validation et solidification")
    print("=" * 60)
    
    # Déploiement automatique
    success = deployer.deploy()
    
    if success:
        print("\n🎉 DÉPLOIEMENT AUTOMATIQUE RÉUSSI !")
        print("✅ JARVIS est maintenant opérationnel")
    else:
        print("\n❌ DÉPLOIEMENT ÉCHOUÉ")
        print("🔄 Tentative de rollback...")
        deployer.rollback()

if __name__ == "__main__":
    main()
