{"name": "jarvis-r1-8b-complete-m4", "version": "3.2.0", "description": "JARVIS R1 8B COMPLET M4 + V2 PRO + ACCÉLÉRATEURS TURBO - Mémoire Thermique + API FastAPI + IA Locale Autonome + Collaboration ChatGPT + Zéro Timeout - <PERSON><PERSON><PERSON>", "main": "jarvis_electron_nouveau.js", "scripts": {"start": "electron jarvis_electron_nouveau.js", "dev": "NODE_ENV=development electron jarvis_electron_final_complet.js", "multi": "electron jarvis_electron_multi_interfaces.js", "complete": "electron jarvis_electron_final_complet.js", "final": "electron jarvis_electron_final_complet.js", "force": "electron jarvis_electron_force.js", "launcher": "electron jarvis_electron_launcher.js", "simple": "electron jarvis_electron_simple.js", "jarvis": "./demarrer_jarvis_optimise.sh", "v2pro": "cd JARVIS_V2_PRO && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000", "turbo": "python accelerateurs_turbo_simple.py", "ia-locale": "python jarvis_ia_locale_autonome.py", "turbo-complet": "./demarrer_jarvis_turbo_complet.sh", "thermal": "python memoire_thermique_turbo_adaptatif.py", "multimedia": "python jarvis_interface_multimedia_complete.py", "formation": "python jarvis_formation_complete.py", "test-complete": "python test_code_complet_final.py", "test-v2pro": "curl http://localhost:8000/health", "test-turbo": "python accelerateurs_turbo_simple.py", "famille-ia": "python famille_ia_autonome_complete.py", "famille-complete": "./demarrer_famille_ia_complete.sh", "build": "electron-builder", "test": "echo \"JARVIS R1 8B M4 Complete Test Suite\" && npm run test-complete"}, "keywords": ["jarvis", "r1", "8b", "deepseek", "ai", "assistant", "electron", "claude", "thermal-memory", "mcp", "model-context-protocol", "internet-access", "jean-luc", "apple-silicon", "m4-optimized", "multimedia-generation", "evolutionary-analysis", "complete-formation", "neural-engine", "unified-memory", "accelerateurs-turbo", "zero-timeout", "ia-locale-autonome", "collaboration-chatgpt", "performance-maximale", "fallback-automatique", "cache-intelligent"], "author": {"name": "Claude & Jean-Luc <PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "express": "^4.21.2", "http-proxy": "^1.18.1", "http-proxy-middleware": "^3.0.5", "node-fetch": "^2.7.0", "qrcode-terminal": "^0.12.0", "whatsapp-web.js": "^1.30.0", "ws": "^8.18.2"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0"}, "build": {"appId": "com.jeanluc.jarvis-r1-8b", "productName": "JARVIS R1 8B", "directories": {"output": "dist"}, "files": ["jarvis_electron_final_complet.js", "jarvis_architecture_multi_fenetres.py", "jarvis_nouvelles_fenetres_simple.py", "JARVIS_V2_PRO/**/*", "jarvis_accelerateurs_turbo.py", "accelerateurs_turbo_simple.py", "jarvis_ia_locale_autonome.py", "chatgpt_claude_collaboration.py", "demarrer_jarvis_turbo_complet.sh", "installer_ia_locale_autonome.sh", "verification_mise_a_jour_electron.py", "memoire_thermique_turbo_adaptatif.py", "jarvis_optimisation_m4_apple_silicon.py", "jarvis_generateur_multimedia_complet.py", "jarvis_formation_complete.py", "jarvis_analyse_evolutive_complete.py", "jarvis_interface_multimedia_complete.py", "thermal_memory_persistent.json", "conversations_permanentes.json", "cache_turbo/**/*", "jarvis_creations/**/*", "models/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns"}, "win": {"target": "nsis", "icon": "assets/icon.ico"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "repository": {"type": "git", "url": "https://github.com/jean-luc-passave/jarvis-r1-8b.git"}, "bugs": {"url": "https://github.com/jean-luc-passave/jarvis-r1-8b/issues"}, "homepage": "https://github.com/jean-luc-passave/jarvis-r1-8b#readme"}