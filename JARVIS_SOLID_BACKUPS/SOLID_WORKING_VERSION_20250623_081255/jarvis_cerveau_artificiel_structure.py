#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 JARVIS - CERVEAU ARTIFICIEL STRUCTURÉ
Système de mémoire hiérarchique et intelligence proactive
Créé avec amour par Claude pour <PERSON><PERSON>
"""

import json
import os
import time
import uuid
from datetime import datetime, timedelta
import calendar
import re
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional, Tuple

# ============================================================================
# CONFIGURATION DU CERVEAU ARTIFICIEL
# ============================================================================

MEMORY_FILE = "thermal_memory_persistent.json"
BRAIN_CONFIG_FILE = "jarvis_brain_config.json"
USER_PROFILE_FILE = "jarvis_user_profile.json"
REMINDERS_FILE = "jarvis_reminders.json"

# Configuration par défaut du cerveau
DEFAULT_BRAIN_CONFIG = {
    "memory_structure": {
        "max_entries_per_day": 1000,
        "compression_threshold": 500,
        "auto_cleanup_days": 30,
        "priority_levels": ["urgent", "high", "normal", "low", "archive"]
    },
    "learning_parameters": {
        "habit_detection_threshold": 3,
        "topic_relevance_score": 0.7,
        "proactive_suggestion_frequency": 24,  # heures
        "context_window_size": 10
    },
    "user_interaction": {
        "notification_types": ["audio", "visual", "text"],
        "proactive_mode": True,
        "learning_mode": True,
        "personality_adaptation": True
    }
}

class JarvisBrainStructure:
    """Cerveau artificiel structuré pour JARVIS"""
    
    def __init__(self):
        self.config = self.load_brain_config()
        self.user_profile = self.load_user_profile()
        self.memory_hierarchy = {}
        self.active_reminders = []
        self.learning_patterns = {}
        
    def load_brain_config(self) -> Dict:
        """Charger la configuration du cerveau"""
        try:
            if os.path.exists(BRAIN_CONFIG_FILE):
                with open(BRAIN_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.save_brain_config(DEFAULT_BRAIN_CONFIG)
                return DEFAULT_BRAIN_CONFIG
        except Exception as e:
            print(f"❌ Erreur chargement config cerveau: {e}")
            return DEFAULT_BRAIN_CONFIG
    
    def save_brain_config(self, config: Dict):
        """Sauvegarder la configuration du cerveau"""
        try:
            with open(BRAIN_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ Erreur sauvegarde config cerveau: {e}")
    
    def load_user_profile(self) -> Dict:
        """Charger le profil utilisateur Jean-Luc Passave"""
        try:
            if os.path.exists(USER_PROFILE_FILE):
                with open(USER_PROFILE_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                default_profile = {
                    "name": "Jean-Luc Passave",
                    "preferences": {
                        "language": "français",
                        "communication_style": "technique_et_precis",
                        "work_hours": {"start": "09:00", "end": "18:00"},
                        "preferred_topics": ["intelligence_artificielle", "programmation", "deepseek"]
                    },
                    "habits": {},
                    "learning_data": {
                        "frequent_queries": [],
                        "response_preferences": {},
                        "interaction_patterns": {}
                    },
                    "created": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                }
                self.save_user_profile(default_profile)
                return default_profile
        except Exception as e:
            print(f"❌ Erreur chargement profil utilisateur: {e}")
    
    def save_user_profile(self, profile: Dict):
        """Sauvegarder le profil utilisateur"""
        try:
            profile["last_updated"] = datetime.now().isoformat()
            with open(USER_PROFILE_FILE, 'w', encoding='utf-8') as f:
                json.dump(profile, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ Erreur sauvegarde profil utilisateur: {e}")

    # ========================================================================
    # ORGANISATION HIÉRARCHIQUE MÉMOIRE
    # ========================================================================
    
    def organize_memory_hierarchically(self) -> Dict:
        """Organiser la mémoire en hiérarchie temporelle: Année > Mois > Jour > Heure"""
        try:
            if not os.path.exists(MEMORY_FILE):
                return {}

            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conversations = data.get('conversations', [])
            hierarchy = defaultdict(lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(list))))
            
            for conv in conversations:
                timestamp = conv.get('timestamp', '')
                if timestamp:
                    try:
                        # Parser le timestamp ISO
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        year = str(dt.year)
                        month = f"{dt.month:02d}"
                        day = f"{dt.day:02d}"
                        hour = f"{dt.hour:02d}"
                        
                        # Organiser hiérarchiquement
                        hierarchy[year][month][day][hour].append({
                            'id': conv.get('id'),
                            'timestamp': timestamp,
                            'content': conv.get('content', ''),
                            'sender': conv.get('sender', ''),
                            'keywords': conv.get('keywords', []),
                            'priority': self.calculate_priority(conv),
                            'thermal_zone': conv.get('thermal_zone', 'general')
                        })
                        
                    except Exception as e:
                        print(f"⚠️ Erreur parsing timestamp {timestamp}: {e}")
                        continue
            
            # Convertir en dict normal pour sérialisation
            self.memory_hierarchy = {
                year: {
                    month: {
                        day: {
                            hour: entries for hour, entries in hours.items()
                        } for day, hours in days.items()
                    } for month, days in months.items()
                } for year, months in hierarchy.items()
            }
            
            return self.memory_hierarchy
            
        except Exception as e:
            print(f"❌ Erreur organisation hiérarchique: {e}")
    
    def calculate_priority(self, conversation: Dict) -> str:
        """Calculer la priorité d'une conversation"""
        content = conversation.get('content', '').lower()
        keywords = conversation.get('keywords', [])
        
        # Mots-clés urgents
        urgent_keywords = ['urgent', 'important', 'critique', 'erreur', 'problème', 'bug']
        high_keywords = ['projet', 'deadline', 'meeting', 'réunion', 'code', 'jarvis']
        
        if any(keyword in content for keyword in urgent_keywords):
            return 'urgent'
        elif any(keyword in content for keyword in high_keywords):
            return 'high'
        elif len(content) > 200:  # Messages longs = plus importants
            return 'normal'
        else:
            return 'low'
    
    # ========================================================================
    # INDEXATION AUTOMATIQUE DES DONNÉES
    # ========================================================================
    
    def auto_index_data(self, conversation: Dict) -> Dict:
        """Indexation automatique avec tags intelligents"""
        content = conversation.get('content', '').lower()
        
        # Détection automatique de tags
        tags = set()
        
        # Tags par mots-clés
        if any(word in content for word in ['code', 'python', 'javascript', 'programme']):
            tags.add('programmation')
        
        if any(word in content for word in ['jarvis', 'ia', 'intelligence', 'agent']):
            tags.add('intelligence_artificielle')
        
        if any(word in content for word in ['projet', 'travail', 'tâche', 'deadline']):
            tags.add('projet')
        
        if any(word in content for word in ['personnel', 'privé', 'famille']):
            tags.add('personnel')
        
        if any(word in content for word in ['urgent', 'important', 'critique']):
            tags.add('urgent')
        
        if any(word in content for word in ['question', 'comment', 'pourquoi', 'aide']):
            tags.add('question')
        
        if any(word in content for word in ['mémoire', 'souvenir', 'rappel']):
            tags.add('memoire')
        
        # Ajouter les tags à la conversation
        conversation['auto_tags'] = list(tags)
        conversation['indexed_at'] = datetime.now().isoformat()
        
        return conversation
    
    # ========================================================================
    # GESTION DE RAPPELS ET NOTIFICATIONS
    # ========================================================================
    
    def create_reminder(self, content: str, datetime_str: str, priority: str = "normal") -> str:
        """Créer un rappel avec notification"""
        try:
            reminder_id = str(uuid.uuid4())
            reminder = {
                'id': reminder_id,
                'content': content,
                'datetime': datetime_str,
                'priority': priority,
                'created_at': datetime.now().isoformat(),
                'status': 'active',
                'user': 'Jean-Luc Passave',
                'notification_sent': False
            }
            
            # Charger les rappels existants
            reminders = self.load_reminders()
            reminders.append(reminder)
            
            # Sauvegarder
            self.save_reminders(reminders)
            
            print(f"✅ Rappel créé: {content} pour {datetime_str}")
            return reminder_id
            
        except Exception as e:
            print(f"❌ Erreur création rappel: {e}")
            return ""
    
    def load_reminders(self) -> List[Dict]:
        """Charger les rappels"""
        try:
            if os.path.exists(REMINDERS_FILE):
                with open(REMINDERS_FILE, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"❌ Erreur chargement rappels: {e}")
            return []
    
    def save_reminders(self, reminders: List[Dict]):
        """Sauvegarder les rappels"""
        try:
            with open(REMINDERS_FILE, 'w', encoding='utf-8') as f:
                json.dump(reminders, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ Erreur sauvegarde rappels: {e}")
    
    def check_active_reminders(self) -> List[Dict]:
        """Vérifier les rappels actifs"""
        try:
            reminders = self.load_reminders()
            active_reminders = []
            current_time = datetime.now()
            
            for reminder in reminders:
                if reminder.get('status') == 'active':
                    reminder_time = datetime.fromisoformat(reminder.get('datetime', ''))
                    
                    # Rappel dans les 5 prochaines minutes
                    if current_time <= reminder_time <= current_time + timedelta(minutes=5):
                        if not reminder.get('notification_sent', False):
                            active_reminders.append(reminder)
            
            return active_reminders
            
        except Exception as e:
            print(f"❌ Erreur vérification rappels: {e}")
            return []
    
    # ========================================================================
    # ANALYSE DES HABITUDES ET APPRENTISSAGE
    # ========================================================================
    
    def analyze_user_habits(self) -> Dict:
        """Analyser les habitudes de Jean-Luc Passave"""
        try:
            hierarchy = self.organize_memory_hierarchically()
            habits = {
                'time_patterns': {},
                'topic_frequency': {},
                'interaction_style': {},
                'work_patterns': {},
                'preferences': {}
            }
            
            # Analyser les patterns temporels
            for year, months in hierarchy.items():
                for month, days in months.items():
                    for day, hours in days.items():
                        for hour, conversations in hours.items():
                            hour_key = f"{hour}:00"
                            habits['time_patterns'][hour_key] = habits['time_patterns'].get(hour_key, 0) + len(conversations)
            
            # Analyser la fréquence des sujets
            all_conversations = []
            for year, months in hierarchy.items():
                for month, days in months.items():
                    for day, hours in days.items():
                        for hour, conversations in hours.items():
                            all_conversations.extend(conversations)
            
            # Compter les mots-clés fréquents
            word_counter = Counter()
            for conv in all_conversations:
                keywords = conv.get('keywords', [])
                word_counter.update(keywords)
            
            habits['topic_frequency'] = dict(word_counter.most_common(20))
            
            # Mettre à jour le profil utilisateur
            self.user_profile['habits'] = habits
            self.user_profile['last_analysis'] = datetime.now().isoformat()
            self.save_user_profile(self.user_profile)
            
            return habits
            
        except Exception as e:
            print(f"❌ Erreur analyse habitudes: {e}")
    
    def generate_proactive_suggestions(self) -> List[str]:
        """Générer des suggestions proactives basées sur l'analyse"""
        try:
            habits = self.analyze_user_habits()
            suggestions = []
            
            # Suggestions basées sur les patterns temporels
            current_hour = datetime.now().hour
            hour_key = f"{current_hour:02d}:00"
            
            if hour_key in habits.get('time_patterns', {}):
                suggestions.append(f"🕐 C'est votre heure habituelle d'activité ({hour_key}). Que puis-je faire pour vous ?")
            
            # Suggestions basées sur les sujets fréquents
            top_topics = list(habits.get('topic_frequency', {}).keys())[:3]
            if top_topics:
                suggestions.append(f"💡 Vos sujets récurrents: {', '.join(top_topics)}. Voulez-vous en discuter ?")
            
            # Suggestions contextuelles
            if 'code' in top_topics or 'programmation' in top_topics:
                suggestions.append("💻 Voulez-vous que je révise votre code récent ou que je vous aide avec un nouveau projet ?")
            
            if 'jarvis' in top_topics or 'ia' in top_topics:
                suggestions.append("🤖 Souhaitez-vous améliorer mes capacités ou discuter de nouvelles fonctionnalités ?")
            
            return suggestions[:3]  # Maximum 3 suggestions
            
        except Exception as e:
            print(f"❌ Erreur génération suggestions: {e}")
            return ["💡 Comment puis-je vous aider aujourd'hui, Jean-Luc ?"]

# ============================================================================
# FONCTIONS UTILITAIRES
# ============================================================================

def initialize_brain_structure():
    """Initialiser la structure du cerveau artificiel"""
    brain = JarvisBrainStructure()
    
    print("🧠 Initialisation du cerveau artificiel JARVIS...")
    
    # Organiser la mémoire
    hierarchy = brain.organize_memory_hierarchically()
    print(f"📊 Mémoire organisée: {len(hierarchy)} années")
    
    # Analyser les habitudes
    habits = brain.analyze_user_habits()
    print(f"📈 Habitudes analysées: {len(habits.get('topic_frequency', {}))} sujets")
    
    # Générer des suggestions
    suggestions = brain.generate_proactive_suggestions()
    print(f"💡 Suggestions générées: {len(suggestions)}")
    
    return brain

if __name__ == "__main__":
    # Test du système
    brain = initialize_brain_structure()
    
    print("\n🎯 CERVEAU ARTIFICIEL JARVIS INITIALISÉ")
    print("✅ Mémoire hiérarchique organisée")
    print("✅ Profil utilisateur chargé")
    print("✅ Système de rappels actif")
    print("✅ Analyse des habitudes effectuée")
    print("✅ Suggestions proactives générées")
