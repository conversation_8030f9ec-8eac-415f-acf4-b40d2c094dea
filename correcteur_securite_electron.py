#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔒 CORRECTEUR SÉCURITÉ ELECTRON - JEAN-LUC PASSAVE
Correction des warnings de sécurité Electron pour JARVIS
"""

import os
import re
import json
import shutil
from datetime import datetime

class CorrecteurSecuriteElectron:
    """CORRECTEUR SÉCURITÉ ELECTRON POUR JARVIS"""
    
    def __init__(self):
        self.fichiers_electron = [
            "jarvis_electron_final_complet.js",
            "jarvis_electron_multi_interfaces.js"
        ]
        self.corrections_appliquees = []
        
    def corriger_websecurity(self, contenu):
        """CORRIGER LES PROBLÈMES DE WEBSECURITY"""
        corrections = []
        
        # Remplacer webSecurity: false par webSecurity: true avec exceptions
        if 'webSecurity: false' in contenu:
            contenu = contenu.replace(
                'webSecurity: false',
                '''webSecurity: true,
                // Sécurité renforcée - <PERSON>-<PERSON> Passave
                contextIsolation: true,
                enableRemoteModule: false,
                nodeIntegration: false'''
            )
            corrections.append("✅ webSecurity activé avec isolation de contexte")
        
        # Corriger allowRunningInsecureContent
        if 'allowRunningInsecureContent: true' in contenu:
            contenu = contenu.replace(
                'allowRunningInsecureContent: true',
                '''allowRunningInsecureContent: false,
                // Contenu sécurisé uniquement - Jean-Luc Passave'''
            )
            corrections.append("✅ allowRunningInsecureContent désactivé")
        
        return contenu, corrections
    
    def ajouter_csp_securise(self, contenu):
        """AJOUTER UNE POLITIQUE DE SÉCURITÉ DU CONTENU"""
        corrections = []
        
        # CSP sécurisé pour JARVIS
        csp_securise = """
        // 🔒 POLITIQUE DE SÉCURITÉ DU CONTENU - JEAN-LUC PASSAVE
        session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
            callback({
                responseHeaders: {
                    ...details.responseHeaders,
                    'Content-Security-Policy': [
                        "default-src 'self' 'unsafe-inline' 'unsafe-eval' localhost:* 127.0.0.1:* data: blob:; " +
                        "script-src 'self' 'unsafe-inline' 'unsafe-eval' localhost:* 127.0.0.1:*; " +
                        "style-src 'self' 'unsafe-inline' localhost:* 127.0.0.1:*; " +
                        "img-src 'self' data: blob: localhost:* 127.0.0.1:*; " +
                        "connect-src 'self' localhost:* 127.0.0.1:* ws: wss:; " +
                        "media-src 'self' data: blob: localhost:* 127.0.0.1:*; " +
                        "font-src 'self' data: localhost:* 127.0.0.1:*;"
                    ]
                }
            });
        });
        """
        
        # Injecter le CSP après les imports
        if "const { app, BrowserWindow" in contenu and "Content-Security-Policy" not in contenu:
            contenu = contenu.replace(
                "const { app, BrowserWindow",
                csp_securise + "\nconst { app, BrowserWindow"
            )
            corrections.append("✅ Politique de sécurité du contenu ajoutée")
        
        return contenu, corrections
    
    def corriger_experimental_features(self, contenu):
        """CORRIGER LES FONCTIONNALITÉS EXPÉRIMENTALES"""
        corrections = []
        
        # Désactiver les fonctionnalités expérimentales dangereuses
        if 'experimentalFeatures: true' in contenu:
            contenu = contenu.replace(
                'experimentalFeatures: true',
                '''experimentalFeatures: false,
                // Fonctionnalités expérimentales désactivées pour la sécurité - Jean-Luc Passave'''
            )
            corrections.append("✅ Fonctionnalités expérimentales désactivées")
        
        return contenu, corrections
    
    def ajouter_preload_securise(self, contenu):
        """AJOUTER UN SCRIPT PRELOAD SÉCURISÉ"""
        corrections = []
        
        # Créer un script preload sécurisé
        preload_script = '''
// 🔒 SCRIPT PRELOAD SÉCURISÉ JARVIS - JEAN-LUC PASSAVE
const { contextBridge, ipcRenderer } = require('electron');

// Exposer uniquement les APIs nécessaires de manière sécurisée
contextBridge.exposeInMainWorld('jarvisAPI', {
    // Communication sécurisée avec le processus principal
    sendMessage: (channel, data) => {
        const validChannels = ['jarvis-message', 'jarvis-command', 'jarvis-status'];
        if (validChannels.includes(channel)) {
            ipcRenderer.invoke(channel, data);
        }
    },
    
    // Réception sécurisée des messages
    onMessage: (channel, callback) => {
        const validChannels = ['jarvis-response', 'jarvis-update', 'jarvis-notification'];
        if (validChannels.includes(channel)) {
            ipcRenderer.on(channel, callback);
        }
    },
    
    // Informations système sécurisées
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
    
    // Navigation sécurisée
    openWindow: (url) => {
        if (url.startsWith('http://localhost:') || url.startsWith('https://')) {
            ipcRenderer.invoke('open-secure-window', url);
        }
    }
});

// Bloquer l'accès direct à Node.js
delete window.require;
delete window.exports;
delete window.module;
'''
        
        # Sauvegarder le script preload
        with open('jarvis_preload_secure.js', 'w', encoding='utf-8') as f:
            f.write(preload_script)
        
        # Ajouter la référence au preload dans le fichier principal
        if 'preload:' not in contenu and 'webPreferences:' in contenu:
            contenu = contenu.replace(
                'webPreferences: {',
                '''webPreferences: {
                preload: path.join(__dirname, 'jarvis_preload_secure.js'),'''
            )
            corrections.append("✅ Script preload sécurisé ajouté")
        
        return contenu, corrections
    
    def corriger_permissions_securisees(self, contenu):
        """CORRIGER LES PERMISSIONS POUR PLUS DE SÉCURITÉ"""
        corrections = []
        
        # Permissions sécurisées
        permissions_securisees = {
            'nodeIntegration': 'false',
            'contextIsolation': 'true',
            'enableRemoteModule': 'false',
            'sandbox': 'false',  # Gardé à false pour JARVIS mais avec preload sécurisé
            'webSecurity': 'true'
        }
        
        for permission, valeur in permissions_securisees.items():
            pattern_ancien = f'{permission}: true' if valeur == 'false' else f'{permission}: false'
            pattern_nouveau = f'{permission}: {valeur}'
            
            if pattern_ancien in contenu:
                contenu = contenu.replace(pattern_ancien, pattern_nouveau)
                corrections.append(f"✅ {permission} configuré à {valeur}")
        
        return contenu, corrections
    
    def ajouter_gestion_erreurs_securisee(self, contenu):
        """AJOUTER UNE GESTION D'ERREURS SÉCURISÉE"""
        corrections = []
        
        gestion_erreurs = '''
// 🔒 GESTION D'ERREURS SÉCURISÉE - JEAN-LUC PASSAVE
process.on('uncaughtException', (error) => {
    console.error('🔒 Erreur non capturée (sécurisée):', error.message);
    // Ne pas exposer les détails sensibles
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('🔒 Promesse rejetée (sécurisée):', reason);
    // Logging sécurisé sans exposition de données
});

// Désactiver les messages d'erreur détaillés en production
if (process.env.NODE_ENV === 'production') {
    process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';
}
'''
        
        if 'uncaughtException' not in contenu:
            # Ajouter après les imports
            if "const { app, BrowserWindow" in contenu:
                contenu = contenu.replace(
                    "const { app, BrowserWindow",
                    gestion_erreurs + "\nconst { app, BrowserWindow"
                )
                corrections.append("✅ Gestion d'erreurs sécurisée ajoutée")
        
        return contenu, corrections
    
    def corriger_fichier_electron(self, fichier):
        """CORRIGER UN FICHIER ELECTRON SPÉCIFIQUE"""
        if not os.path.exists(fichier):
            print(f"⚠️ Fichier non trouvé: {fichier}")
            return False
        
        print(f"🔒 Correction sécurité: {fichier}")
        
        # Backup du fichier original
        backup_file = f"{fichier}.backup_security_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(fichier, backup_file)
        print(f"💾 Backup créé: {backup_file}")
        
        # Lire le contenu
        with open(fichier, 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        corrections_fichier = []
        
        # Appliquer toutes les corrections
        contenu, corr = self.corriger_websecurity(contenu)
        corrections_fichier.extend(corr)
        
        contenu, corr = self.ajouter_csp_securise(contenu)
        corrections_fichier.extend(corr)
        
        contenu, corr = self.corriger_experimental_features(contenu)
        corrections_fichier.extend(corr)
        
        contenu, corr = self.ajouter_preload_securise(contenu)
        corrections_fichier.extend(corr)
        
        contenu, corr = self.corriger_permissions_securisees(contenu)
        corrections_fichier.extend(corr)
        
        contenu, corr = self.ajouter_gestion_erreurs_securisee(contenu)
        corrections_fichier.extend(corr)
        
        # Sauvegarder le fichier corrigé
        with open(fichier, 'w', encoding='utf-8') as f:
            f.write(contenu)
        
        print(f"✅ {len(corrections_fichier)} corrections appliquées à {fichier}")
        for correction in corrections_fichier:
            print(f"   {correction}")
        
        self.corrections_appliquees.extend(corrections_fichier)
        return True
    
    def corriger_tous_fichiers(self):
        """CORRIGER TOUS LES FICHIERS ELECTRON"""
        print("🔒 DÉMARRAGE CORRECTION SÉCURITÉ ELECTRON - JEAN-LUC PASSAVE")
        print("=" * 70)
        
        fichiers_corriges = 0
        
        for fichier in self.fichiers_electron:
            if self.corriger_fichier_electron(fichier):
                fichiers_corriges += 1
        
        print("\n" + "=" * 70)
        print(f"✅ CORRECTION TERMINÉE: {fichiers_corriges} fichiers corrigés")
        print(f"🔒 {len(self.corrections_appliquees)} corrections de sécurité appliquées")
        print("\n🛡️ SÉCURITÉ ELECTRON RENFORCÉE POUR JARVIS")
        
        # Créer un rapport de sécurité
        self.creer_rapport_securite()
    
    def creer_rapport_securite(self):
        """CRÉER UN RAPPORT DE SÉCURITÉ"""
        rapport = {
            "timestamp": datetime.now().isoformat(),
            "corrections_appliquees": self.corrections_appliquees,
            "fichiers_corriges": self.fichiers_electron,
            "niveau_securite": "RENFORCE",
            "recommandations": [
                "Vérifier régulièrement les mises à jour Electron",
                "Tester l'application après les corrections",
                "Surveiller les logs pour détecter les problèmes",
                "Maintenir les scripts preload à jour"
            ]
        }
        
        with open('rapport_securite_electron.json', 'w', encoding='utf-8') as f:
            json.dump(rapport, f, ensure_ascii=False, indent=2)
        
        print("📊 Rapport de sécurité créé: rapport_securite_electron.json")

def main():
    """FONCTION PRINCIPALE"""
    correcteur = CorrecteurSecuriteElectron()
    correcteur.corriger_tous_fichiers()

if __name__ == "__main__":
    main()
