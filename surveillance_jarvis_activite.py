#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🕐 SYSTÈME DE SURVEILLANCE ACTIVITÉ JARVIS - JEAN-LUC PASSAVE
Enregistrement complet de toutes les activités de JARVIS en votre absence
"""

import json
import os
import time
import threading
import psutil
from datetime import datetime, timedelta
import sqlite3

class JarvisSurveillanceActivite:
    """SURVEILLANCE COMPLÈTE ACTIVITÉ JARVIS - JEAN-LUC PASSAVE"""
    
    def __init__(self):
        self.db_file = "jarvis_activite_surveillance.db"
        self.log_file = "jarvis_activite_log.json"
        self.surveillance_active = False
        self.thread_surveillance = None
        self.init_database()
        
    def init_database(self):
        """INITIALISER BASE DE DONNÉES SURVEILLANCE"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Table activités générales
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS activites (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    type_activite TEXT NOT NULL,
                    description TEXT NOT NULL,
                    details TEXT,
                    cpu_percent REAL,
                    memory_percent REAL
                )
            ''')
            
            # Table pensées autonomes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS pensees_autonomes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    sujet TEXT NOT NULL,
                    contenu TEXT NOT NULL,
                    complexite REAL
                )
            ''')
            
            # Table sessions utilisateur
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions_utilisateur (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    debut_session TEXT NOT NULL,
                    fin_session TEXT,
                    duree_minutes REAL,
                    activites_pendant_session INTEGER,
                    statut TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ Base de données surveillance initialisée")
            
        except Exception as e:
            print(f"❌ Erreur initialisation DB: {e}")
    
    def demarrer_surveillance(self):
        """DÉMARRER SURVEILLANCE ACTIVITÉ"""
        if self.surveillance_active:
            print("⚠️ Surveillance déjà active")
            return
        
        self.surveillance_active = True
        self.thread_surveillance = threading.Thread(target=self._boucle_surveillance, daemon=True)
        self.thread_surveillance.start()
        
        # Enregistrer début de session
        self.enregistrer_session_debut()
        
        print("🚀 SURVEILLANCE ACTIVITÉ JARVIS DÉMARRÉE")
        print(f"📊 Logs: {self.log_file}")
        print(f"🗄️ Base de données: {self.db_file}")
    
    def arreter_surveillance(self):
        """ARRÊTER SURVEILLANCE"""
        self.surveillance_active = False
        if self.thread_surveillance:
            self.thread_surveillance.join(timeout=5)
        
        # Enregistrer fin de session
        self.enregistrer_session_fin()
        
        print("⏹️ SURVEILLANCE ACTIVITÉ ARRÊTÉE")
    
    def _boucle_surveillance(self):
        """BOUCLE PRINCIPALE DE SURVEILLANCE"""
        while self.surveillance_active:
            try:
                # Surveiller toutes les 30 secondes
                time.sleep(30)
                
                # Collecter données système
                cpu_percent = psutil.cpu_percent()
                memory_percent = psutil.virtual_memory().percent
                
                # Analyser mémoire thermique
                activite_memoire = self.analyser_memoire_thermique()
                
                # Détecter pensées autonomes
                pensees = self.detecter_pensees_autonomes()
                
                # Surveiller processus JARVIS
                processus_jarvis = self.surveiller_processus_jarvis()
                
                # Enregistrer activité
                self.enregistrer_activite(
                    type_activite="surveillance_periodique",
                    description=f"Surveillance automatique - CPU: {cpu_percent}%, RAM: {memory_percent}%",
                    details={
                        "cpu_percent": cpu_percent,
                        "memory_percent": memory_percent,
                        "activite_memoire": activite_memoire,
                        "pensees_detectees": len(pensees),
                        "processus_jarvis": len(processus_jarvis)
                    },
                    cpu_percent=cpu_percent,
                    memory_percent=memory_percent
                )
                
                # Enregistrer pensées détectées
                for pensee in pensees:
                    self.enregistrer_pensee_autonome(pensee)
                
            except Exception as e:
                print(f"❌ Erreur surveillance: {e}")
                time.sleep(60)
    
    def analyser_memoire_thermique(self):
        """ANALYSER ACTIVITÉ MÉMOIRE THERMIQUE"""
        try:
            if os.path.exists("thermal_memory_persistent.json"):
                with open("thermal_memory_persistent.json", 'r', encoding='utf-8') as f:
                    thermal_data = json.load(f)
                
                neuron_memories = thermal_data.get('neuron_memories', [])
                
                # Analyser les neurones récents (dernières 5 minutes)
                now = datetime.now()
                recent_neurons = []
                
                for neuron in neuron_memories[-50:]:
                    if 'activation_timestamp' in neuron:
                        try:
                            timestamp_str = neuron['activation_timestamp']
                            if 'T' in timestamp_str:
                                if '+' in timestamp_str:
                                    timestamp = datetime.fromisoformat(timestamp_str.split('+')[0])
                                else:
                                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', ''))
                            else:
                                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            
                            # Si c'est récent (dernières 5 minutes)
                            if (now - timestamp).total_seconds() < 300:
                                recent_neurons.append(neuron)
                        except:
                            continue
                
                return {
                    "neurones_totaux": len(neuron_memories),
                    "neurones_recents": len(recent_neurons),
                    "derniere_activite": recent_neurons[-1] if recent_neurons else None
                }
            
            return {"neurones_totaux": 0, "neurones_recents": 0}
            
        except Exception as e:
            return {"erreur": str(e)}
    
    def detecter_pensees_autonomes(self):
        """DÉTECTER NOUVELLES PENSÉES AUTONOMES"""
        try:
            if os.path.exists("thermal_memory_persistent.json"):
                with open("thermal_memory_persistent.json", 'r', encoding='utf-8') as f:
                    thermal_data = json.load(f)
                
                neuron_memories = thermal_data.get('neuron_memories', [])
                
                # Chercher pensées autonomes récentes
                pensees_autonomes = []
                now = datetime.now()
                
                for neuron in neuron_memories[-20:]:
                    if 'REFLEXION_AUTO' in neuron.get('neuron_id', '') or \
                       'JARVIS_AUTONOME' in neuron.get('memory_content', {}).get('user_name', ''):
                        
                        try:
                            timestamp_str = neuron['activation_timestamp']
                            if 'T' in timestamp_str:
                                timestamp = datetime.fromisoformat(timestamp_str.split('+')[0])
                            else:
                                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            
                            # Si c'est très récent (dernière minute)
                            if (now - timestamp).total_seconds() < 60:
                                pensees_autonomes.append({
                                    "timestamp": timestamp.isoformat(),
                                    "neuron_id": neuron.get('neuron_id'),
                                    "sujet": neuron.get('memory_content', {}).get('user_message', ''),
                                    "contenu": neuron.get('memory_content', {}).get('agent_response', ''),
                                    "complexite": neuron.get('neuron_metadata', {}).get('complexity', 0)
                                })
                        except:
                            continue
                
                return pensees_autonomes
            
            return []
            
        except Exception as e:
            return []
    
    def surveiller_processus_jarvis(self):
        """SURVEILLER PROCESSUS JARVIS ACTIFS"""
        try:
            processus_jarvis = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline']).lower()
                    if 'jarvis' in cmdline and 'python' in cmdline:
                        processus_jarvis.append({
                            "pid": proc.info['pid'],
                            "name": proc.info['name'],
                            "cmdline": cmdline[:100]
                        })
                except:
                    continue
            
            return processus_jarvis
            
        except Exception as e:
            return []
    
    def enregistrer_activite(self, type_activite, description, details=None, cpu_percent=None, memory_percent=None):
        """ENREGISTRER UNE ACTIVITÉ"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO activites (timestamp, type_activite, description, details, cpu_percent, memory_percent)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                type_activite,
                description,
                json.dumps(details) if details else None,
                cpu_percent,
                memory_percent
            ))
            
            conn.commit()
            conn.close()
            
            # Aussi enregistrer dans le log JSON
            self.enregistrer_log_json({
                "timestamp": datetime.now().isoformat(),
                "type": type_activite,
                "description": description,
                "details": details
            })
            
        except Exception as e:
            print(f"❌ Erreur enregistrement activité: {e}")
    
    def enregistrer_pensee_autonome(self, pensee):
        """ENREGISTRER UNE PENSÉE AUTONOME"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO pensees_autonomes (timestamp, sujet, contenu, complexite)
                VALUES (?, ?, ?, ?)
            ''', (
                pensee.get('timestamp'),
                pensee.get('sujet', '')[:200],
                pensee.get('contenu', '')[:500],
                pensee.get('complexite', 0)
            ))
            
            conn.commit()
            conn.close()
            
            print(f"🧠 PENSÉE AUTONOME DÉTECTÉE: {pensee.get('sujet', '')[:50]}...")
            
        except Exception as e:
            print(f"❌ Erreur enregistrement pensée: {e}")
    
    def enregistrer_session_debut(self):
        """ENREGISTRER DÉBUT DE SESSION"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO sessions_utilisateur (debut_session, statut)
                VALUES (?, ?)
            ''', (datetime.now().isoformat(), "active"))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Erreur enregistrement session: {e}")
    
    def enregistrer_session_fin(self):
        """ENREGISTRER FIN DE SESSION"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Trouver la dernière session active
            cursor.execute('''
                SELECT id, debut_session FROM sessions_utilisateur 
                WHERE statut = "active" 
                ORDER BY id DESC LIMIT 1
            ''')
            
            result = cursor.fetchone()
            if result:
                session_id, debut_session = result
                debut = datetime.fromisoformat(debut_session)
                fin = datetime.now()
                duree_minutes = (fin - debut).total_seconds() / 60
                
                # Compter activités pendant la session
                cursor.execute('''
                    SELECT COUNT(*) FROM activites 
                    WHERE timestamp >= ? AND timestamp <= ?
                ''', (debut_session, fin.isoformat()))
                
                activites_count = cursor.fetchone()[0]
                
                # Mettre à jour la session
                cursor.execute('''
                    UPDATE sessions_utilisateur 
                    SET fin_session = ?, duree_minutes = ?, activites_pendant_session = ?, statut = ?
                    WHERE id = ?
                ''', (fin.isoformat(), duree_minutes, activites_count, "terminee", session_id))
                
                conn.commit()
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Erreur fin session: {e}")
    
    def enregistrer_log_json(self, entry):
        """ENREGISTRER DANS LOG JSON"""
        try:
            logs = []
            if os.path.exists(self.log_file):
                with open(self.log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            
            logs.append(entry)
            
            # Garder seulement les 1000 dernières entrées
            if len(logs) > 1000:
                logs = logs[-1000:]
            
            with open(self.log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ Erreur log JSON: {e}")

def main():
    """FONCTION PRINCIPALE"""
    print("🕐 SYSTÈME DE SURVEILLANCE ACTIVITÉ JARVIS - JEAN-LUC PASSAVE")
    print("=" * 70)
    
    surveillance = JarvisSurveillanceActivite()
    
    try:
        surveillance.demarrer_surveillance()
        
        print("🚀 SURVEILLANCE ACTIVE - JARVIS EST MAINTENANT SURVEILLÉ")
        print("💡 Utilisez Ctrl+C pour arrêter")
        
        # Boucle principale
        while True:
            time.sleep(60)
            
    except KeyboardInterrupt:
        print("\n🛑 ARRÊT DEMANDÉ PAR L'UTILISATEUR")
        surveillance.arreter_surveillance()
        print("✅ SURVEILLANCE ARRÊTÉE PROPREMENT")

if __name__ == "__main__":
    main()
