
# 🎤 SYSTÈME VOCAL RÉEL CORRIGÉ - JEAN-LUC PASSAVE

def activer_reconnaissance_vocale_reelle():
    """RECONNAISSANCE VOCALE RÉELLE - PLUS DE SIMULATION"""
    try:
        import speech_recognition as sr
        import threading
        
        r = sr.Recognizer()
        m = sr.Microphone()
        
        # Calibrage
        with m as source:
            r.adjust_for_ambient_noise(source)
        
        def ecoute_continue():
            while True:
                try:
                    with m as source:
                        audio = r.listen(source, timeout=1, phrase_time_limit=5)
                    
                    text = r.recognize_google(audio, language='fr-FR')
                    
                    if "jarvis" in text.lower():
                        print(f"🎤 COMMANDE DÉTECTÉE: {text}")
                        # Traiter la commande avec JARVIS
                        
                except:
                    pass
        
        # Lancer en arrière-plan
        thread = threading.Thread(target=ecoute_continue, daemon=True)
        thread.start()
        
        return "✅ RECONNAISSANCE VOCALE RÉELLE ACTIVÉE"
        
    except Exception as e:
        return f"❌ Erreur: {e}"

def activer_synthese_vocale_reelle(texte):
    """SYNTHÈSE VOCALE RÉELLE - PLUS DE SIMULATION"""
    try:
        import pyttsx3
        
        engine = pyttsx3.init()
        
        # Voix française
        voices = engine.getProperty('voices')
        for voice in voices:
            if 'french' in voice.name.lower() or 'fr' in voice.id.lower():
                engine.setProperty('voice', voice.id)
                break
        
        engine.setProperty('rate', 200)
        engine.setProperty('volume', 0.9)
        
        # PARLER VRAIMENT
        engine.say(texte)
        engine.runAndWait()
        
        return "✅ JARVIS A PARLÉ RÉELLEMENT"
        
    except Exception as e:
        return f"❌ Erreur: {e}"
