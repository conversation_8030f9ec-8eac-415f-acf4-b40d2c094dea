#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🕐 INTERFACE SURVEILLANCE ACTIVITÉ JARVIS - JEAN-LUC PASSAVE
Interface web pour visualiser l'activité de JARVIS en votre absence
"""

import gradio as gr
import json
import sqlite3
import os
from datetime import datetime, timedelta

# Imports optionnels avec fallbacks
try:
    import pandas as pd
except ImportError:
    pd = None

try:
    import plotly.graph_objects as go
    import plotly.express as px
except ImportError:
    go = None
    px = None

try:
    from surveillance_jarvis_activite import JarvisSurveillanceActivite
except ImportError:
    # Fallback simple si le module n'existe pas
    class JarvisSurveillanceActivite:
        def __init__(self):
            pass
        def demarrer_surveillance(self):
            return "Surveillance démarrée (mode fallback)"
        def arreter_surveillance(self):
            return "Surveillance arrêtée (mode fallback)"

class InterfaceSurveillanceJarvis:
    """INTERFACE WEB SURVEILLANCE JARVIS - JEAN-LUC PASSAVE"""
    
    def __init__(self):
        self.surveillance = JarvisSurveillanceActivite()
        self.db_file = "jarvis_activite_surveillance.db"
        
    def obtenir_activites_recentes(self, limite=50):
        """OBTENIR LES ACTIVITÉS RÉCENTES"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT timestamp, type_activite, description, details, cpu_percent, memory_percent
                FROM activites 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (limite,))
            
            activites = cursor.fetchall()
            conn.close()
            
            if not activites:
                return "📭 Aucune activité enregistrée pour le moment"
            
            html_content = """
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; color: white; margin-bottom: 20px;">
                <h2 style="margin: 0 0 10px 0;">🕐 ACTIVITÉS RÉCENTES DE JARVIS</h2>
                <p style="margin: 0; opacity: 0.9;">Surveillance en temps réel de votre assistant IA</p>
            </div>
            """
            
            for activite in activites:
                timestamp, type_act, description, details, cpu, memory = activite
                
                # Formater le timestamp
                try:
                    dt = datetime.fromisoformat(timestamp)
                    time_str = dt.strftime("%d/%m/%Y %H:%M:%S")
                except:
                    time_str = timestamp
                
                # Couleur selon le type d'activité
                if "surveillance" in type_act:
                    color = "#4CAF50"
                    icon = "🔍"
                elif "demarrage" in type_act:
                    color = "#2196F3"
                    icon = "🚀"
                elif "arret" in type_act:
                    color = "#FF9800"
                    icon = "⏹️"
                else:
                    color = "#9C27B0"
                    icon = "⚡"
                
                html_content += f"""
                <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {color};">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="margin: 0; color: #333;">{icon} {type_act.upper()}</h4>
                        <small style="color: #666;">{time_str}</small>
                    </div>
                    <p style="margin: 10px 0 0 0; color: #555;">{description}</p>
                    {f'<div style="margin-top: 10px;"><small style="color: #888;">CPU: {cpu}% | RAM: {memory}%</small></div>' if cpu and memory else ''}
                </div>
                """
            
            return html_content
            
        except Exception as e:
            return f"❌ Erreur lors de la récupération des activités: {e}"
    
    def obtenir_pensees_autonomes(self, limite=20):
        """OBTENIR LES PENSÉES AUTONOMES"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT timestamp, sujet, contenu, complexite
                FROM pensees_autonomes 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (limite,))
            
            pensees = cursor.fetchall()
            conn.close()
            
            if not pensees:
                return "🧠 Aucune pensée autonome détectée récemment"
            
            html_content = """
            <div style="background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%); padding: 20px; border-radius: 15px; color: white; margin-bottom: 20px;">
                <h2 style="margin: 0 0 10px 0;">🧠 PENSÉES AUTONOMES DE JARVIS</h2>
                <p style="margin: 0; opacity: 0.9;">Réflexions spontanées de votre assistant IA</p>
            </div>
            """
            
            for pensee in pensees:
                timestamp, sujet, contenu, complexite = pensee
                
                try:
                    dt = datetime.fromisoformat(timestamp)
                    time_str = dt.strftime("%d/%m/%Y %H:%M:%S")
                except:
                    time_str = timestamp
                
                # Couleur selon la complexité
                if complexite > 0.8:
                    complexity_color = "#E91E63"
                    complexity_label = "🔥 Très complexe"
                elif complexite > 0.5:
                    complexity_color = "#FF9800"
                    complexity_label = "⚡ Complexe"
                else:
                    complexity_color = "#4CAF50"
                    complexity_label = "💡 Simple"
                
                html_content += f"""
                <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {complexity_color};">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="margin: 0; color: #333;">🧠 {sujet[:50]}...</h4>
                        <div style="text-align: right;">
                            <small style="color: #666;">{time_str}</small><br>
                            <small style="color: {complexity_color}; font-weight: bold;">{complexity_label}</small>
                        </div>
                    </div>
                    <p style="margin: 10px 0 0 0; color: #555; font-style: italic;">"{contenu[:200]}..."</p>
                </div>
                """
            
            return html_content
            
        except Exception as e:
            return f"❌ Erreur lors de la récupération des pensées: {e}"
    
    def obtenir_statistiques_sessions(self):
        """OBTENIR LES STATISTIQUES DES SESSIONS"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Sessions récentes
            cursor.execute('''
                SELECT debut_session, fin_session, duree_minutes, activites_pendant_session, statut
                FROM sessions_utilisateur 
                ORDER BY debut_session DESC 
                LIMIT 10
            ''')
            
            sessions = cursor.fetchall()
            conn.close()
            
            if not sessions:
                return "📊 Aucune session enregistrée"
            
            html_content = """
            <div style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); padding: 20px; border-radius: 15px; color: white; margin-bottom: 20px;">
                <h2 style="margin: 0 0 10px 0;">📊 HISTORIQUE DES SESSIONS</h2>
                <p style="margin: 0; opacity: 0.9;">Suivi de vos interactions avec JARVIS</p>
            </div>
            """
            
            total_duree = 0
            total_activites = 0
            
            for session in sessions:
                debut, fin, duree, activites, statut = session
                
                try:
                    debut_dt = datetime.fromisoformat(debut)
                    debut_str = debut_dt.strftime("%d/%m/%Y %H:%M")
                except:
                    debut_str = debut
                
                if fin:
                    try:
                        fin_dt = datetime.fromisoformat(fin)
                        fin_str = fin_dt.strftime("%H:%M")
                    except:
                        fin_str = fin
                else:
                    fin_str = "En cours"
                
                if duree:
                    total_duree += duree
                    duree_str = f"{int(duree//60)}h {int(duree%60)}min"
                else:
                    duree_str = "En cours"
                
                if activites:
                    total_activites += activites
                
                # Couleur selon le statut
                if statut == "active":
                    status_color = "#4CAF50"
                    status_icon = "🟢"
                else:
                    status_color = "#9E9E9E"
                    status_icon = "⚪"
                
                html_content += f"""
                <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {status_color};">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4 style="margin: 0; color: #333;">{status_icon} Session du {debut_str}</h4>
                        <small style="color: #666;">Fin: {fin_str}</small>
                    </div>
                    <div style="margin-top: 10px; display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div><strong>Durée:</strong> {duree_str}</div>
                        <div><strong>Activités:</strong> {activites or 0}</div>
                    </div>
                </div>
                """
            
            # Statistiques globales
            html_content += f"""
            <div style="background: #f5f5f5; padding: 15px; border-radius: 10px; margin-top: 20px;">
                <h4 style="margin: 0 0 10px 0; color: #333;">📈 STATISTIQUES GLOBALES</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #2196F3;">{int(total_duree//60)}h {int(total_duree%60)}min</div>
                        <div style="color: #666;">Temps total</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 1.5em; font-weight: bold; color: #4CAF50;">{total_activites}</div>
                        <div style="color: #666;">Activités totales</div>
                    </div>
                </div>
            </div>
            """
            
            return html_content
            
        except Exception as e:
            return f"❌ Erreur lors de la récupération des statistiques: {e}"
    
    def demarrer_surveillance_interface(self):
        """DÉMARRER LA SURVEILLANCE DEPUIS L'INTERFACE"""
        try:
            self.surveillance.demarrer_surveillance()
            return """
            <div style='background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #4CAF50;'>
                <h5 style='color: #2e7d32; margin: 0 0 10px 0;'>✅ SURVEILLANCE DÉMARRÉE</h5>
                <p style='margin: 0; font-size: 0.9em;'>
                    🚀 La surveillance d'activité JARVIS est maintenant active.<br>
                    📊 Toutes les actions seront enregistrées automatiquement.<br>
                    🔄 Actualisez cette page pour voir les nouvelles activités.
                </p>
            </div>
            """
        except Exception as e:
            return f"""
            <div style='background: #ffebee; padding: 15px; border-radius: 10px; border-left: 4px solid #f44336;'>
                <h5 style='color: #d32f2f; margin: 0 0 10px 0;'>❌ ERREUR DÉMARRAGE</h5>
                <p style='margin: 0; font-size: 0.9em;'>Erreur: {e}</p>
            </div>
            """
    
    def arreter_surveillance_interface(self):
        """ARRÊTER LA SURVEILLANCE DEPUIS L'INTERFACE"""
        try:
            self.surveillance.arreter_surveillance()
            return """
            <div style='background: #fff3e0; padding: 15px; border-radius: 10px; border-left: 4px solid #FF9800;'>
                <h5 style='color: #f57c00; margin: 0 0 10px 0;'>⏹️ SURVEILLANCE ARRÊTÉE</h5>
                <p style='margin: 0; font-size: 0.9em;'>
                    🛑 La surveillance d'activité a été arrêtée.<br>
                    📝 Les données existantes sont conservées.<br>
                    🔄 Vous pouvez redémarrer la surveillance à tout moment.
                </p>
            </div>
            """
        except Exception as e:
            return f"""
            <div style='background: #ffebee; padding: 15px; border-radius: 10px; border-left: 4px solid #f44336;'>
                <h5 style='color: #d32f2f; margin: 0 0 10px 0;'>❌ ERREUR ARRÊT</h5>
                <p style='margin: 0; font-size: 0.9em;'>Erreur: {e}</p>
            </div>
            """

def create_surveillance_interface():
    """CRÉER L'INTERFACE DE SURVEILLANCE JARVIS"""
    
    surveillance_interface = InterfaceSurveillanceJarvis()
    
    with gr.Blocks(
        title="🕐 JARVIS - Surveillance Activité",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
            margin: 0 auto !important;
        }
        .refresh-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049) !important;
            color: white !important;
        }
        .control-btn {
            background: linear-gradient(45deg, #2196F3, #1976D2) !important;
            color: white !important;
        }
        """
    ) as interface:
        
        # HEADER
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; margin: -20px -20px 20px -20px; border-radius: 0 0 20px 20px;">
            <h1 style="margin: 0; font-size: 2.5em;">🕐 SURVEILLANCE ACTIVITÉ JARVIS</h1>
            <p style="margin: 15px 0 0 0; font-size: 1.2em; opacity: 0.9;">Monitoring complet de votre assistant IA - Jean-Luc Passave</p>
        </div>
        """)
        
        # CONTRÔLES DE SURVEILLANCE
        with gr.Row():
            with gr.Column(scale=1):
                start_surveillance_btn = gr.Button("🚀 Démarrer Surveillance", variant="primary", size="lg", elem_classes=["control-btn"])
            with gr.Column(scale=1):
                stop_surveillance_btn = gr.Button("⏹️ Arrêter Surveillance", variant="secondary", size="lg")
            with gr.Column(scale=1):
                refresh_btn = gr.Button("🔄 Actualiser", variant="primary", size="lg", elem_classes=["refresh-btn"])
            with gr.Column(scale=1):
                home_btn = gr.Button("🏠 Retour Dashboard", variant="secondary", size="lg")
        
        # STATUT DE LA SURVEILLANCE
        surveillance_status = gr.HTML()
        
        # ONGLETS PRINCIPAUX
        with gr.Tabs():
            with gr.TabItem("🔍 Activités Récentes"):
                activites_display = gr.HTML()
                
            with gr.TabItem("🧠 Pensées Autonomes"):
                pensees_display = gr.HTML()
                
            with gr.TabItem("📊 Sessions & Statistiques"):
                sessions_display = gr.HTML()
        
        # NAVIGATION VERS MONITORING
        with gr.Row():
            gr.HTML("""
            <div style="background: linear-gradient(45deg, #1976D2, #1565C0); padding: 20px; border-radius: 15px; text-align: center; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: white;">📊 MONITORING SYSTÈME</h3>
                <p style="margin: 0; color: white; font-size: 0.9em;">Accéder au monitoring 24h/24 des performances</p>
            </div>
            """)
        
        with gr.Row():
            monitoring_btn = gr.Button("📊 Aller au Monitoring", variant="primary", size="lg")
        
        # CONNEXIONS DES BOUTONS
        start_surveillance_btn.click(
            fn=surveillance_interface.demarrer_surveillance_interface,
            outputs=[surveillance_status]
        )
        
        stop_surveillance_btn.click(
            fn=surveillance_interface.arreter_surveillance_interface,
            outputs=[surveillance_status]
        )
        
        refresh_btn.click(
            fn=lambda: [
                surveillance_interface.obtenir_activites_recentes(),
                surveillance_interface.obtenir_pensees_autonomes(),
                surveillance_interface.obtenir_statistiques_sessions()
            ],
            outputs=[activites_display, pensees_display, sessions_display]
        )
        
        # Navigation
        home_btn.click(fn=lambda: open_window_surveillance("main"), outputs=[])
        monitoring_btn.click(fn=lambda: open_window_surveillance("monitoring"), outputs=[])
        
        # CHARGEMENT INITIAL
        interface.load(
            fn=lambda: [
                surveillance_interface.obtenir_activites_recentes(),
                surveillance_interface.obtenir_pensees_autonomes(),
                surveillance_interface.obtenir_statistiques_sessions()
            ],
            outputs=[activites_display, pensees_display, sessions_display]
        )
    
    return interface

def open_window_surveillance(window_type):
    """OUVRIR UNE FENÊTRE DEPUIS L'INTERFACE DE SURVEILLANCE"""
    import webbrowser

    ports = {
        "main": 8101,  # Dashboard principal CORRIGÉ
        "monitoring": 8107,  # Interface monitoring
        "communication": 8100,  # Interface communication
    }

    if window_type in ports:
        url = f"http://localhost:{ports[window_type]}"
        webbrowser.open(url)
        print(f"🌐 Ouverture de {window_type}: {url}")
    else:
        print(f"❌ Type de fenêtre inconnu: {window_type}")

if __name__ == "__main__":
    # Lancer l'interface de surveillance sur le port 8260
    interface = create_surveillance_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=8260,
        share=False,
        show_error=True,
        quiet=False
    )
    print("🕐 Interface de surveillance JARVIS lancée sur http://localhost:8260")
