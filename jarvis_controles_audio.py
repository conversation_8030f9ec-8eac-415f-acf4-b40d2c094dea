#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS CONTRÔLES AUDIO - JEAN-LUC PASSAVE
Interface dédiée aux contrôles audio avancés
"""

import gradio as gr
import datetime

def create_audio_controls_interface():
    """Interface de contrôles audio avancés pour JARVIS"""
    
    with gr.<PERSON>s(
        title="🎛️ JARVIS Contrôles Audio",
        theme=gr.themes.Soft(),
        css="""
        .control-btn {
            background: linear-gradient(45deg, #9C27B0, #673AB7) !important;
            color: white !important;
            font-weight: bold !important;
            padding: 15px 25px !important;
            border-radius: 20px !important;
            box-shadow: 0 6px 15px rgba(156, 39, 176, 0.3) !important;
            transition: all 0.3s ease !important;
        }
        .control-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(156, 39, 176, 0.4) !important;
        }
        """
    ) as interface:
        
        # Header
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #9C27B0, #673AB7); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 2.2em;">🎛️ JARVIS CONTRÔLES AUDIO</h1>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Réglages avancés du haut-parleur - Jean-Luc Passave</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column():
                gr.HTML("<h3 style='color: #9C27B0;'>🔊 Contrôles de Volume</h3>")
                
                volume_slider = gr.Slider(
                    minimum=0.1,
                    maximum=1.0,
                    value=1.0,
                    step=0.1,
                    label="Volume JARVIS",
                    info="Ajustez le volume de la voix de JARVIS"
                )
                
                with gr.Row():
                    volume_test_btn = gr.Button("🔊 TESTER VOLUME", elem_classes=["control-btn"])
                    volume_max_btn = gr.Button("📢 VOLUME MAX", elem_classes=["control-btn"])
            
            with gr.Column():
                gr.HTML("<h3 style='color: #9C27B0;'>⚡ Contrôles de Vitesse</h3>")
                
                speed_slider = gr.Slider(
                    minimum=0.5,
                    maximum=2.0,
                    value=0.85,
                    step=0.05,
                    label="Vitesse de lecture",
                    info="Ajustez la vitesse de parole de JARVIS"
                )
                
                with gr.Row():
                    speed_test_btn = gr.Button("⚡ TESTER VITESSE", elem_classes=["control-btn"])
                    speed_normal_btn = gr.Button("🎯 VITESSE NORMALE", elem_classes=["control-btn"])
        
        with gr.Row():
            with gr.Column():
                gr.HTML("<h3 style='color: #9C27B0;'>🎵 Contrôles de Tonalité</h3>")
                
                pitch_slider = gr.Slider(
                    minimum=0.5,
                    maximum=2.0,
                    value=0.8,
                    step=0.1,
                    label="Tonalité de la voix",
                    info="Ajustez la tonalité de la voix de JARVIS"
                )
                
                with gr.Row():
                    pitch_test_btn = gr.Button("🎵 TESTER TONALITÉ", elem_classes=["control-btn"])
                    pitch_deep_btn = gr.Button("🎭 VOIX GRAVE", elem_classes=["control-btn"])
            
            with gr.Column():
                gr.HTML("<h3 style='color: #9C27B0;'>🤖 Modes de Lecture</h3>")
                
                auto_read_checkbox = gr.Checkbox(
                    label="Lecture automatique des réponses",
                    value=False,
                    info="JARVIS lit automatiquement ses réponses"
                )
                
                thoughts_read_checkbox = gr.Checkbox(
                    label="Lecture automatique des pensées",
                    value=False,
                    info="JARVIS lit automatiquement ses pensées"
                )
                
                with gr.Row():
                    mode_test_btn = gr.Button("🤖 TESTER MODE", elem_classes=["control-btn"])
                    mode_reset_btn = gr.Button("🔄 RESET MODES", elem_classes=["control-btn"])
        
        # Zone de statut
        status_audio = gr.HTML("""
        <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #2196f3;">
            <h4 style="color: #2196f3; margin: 0 0 10px 0;">📊 Statut Audio JARVIS</h4>
            <p style="margin: 5px 0;">✅ Synthèse vocale activée</p>
            <p style="margin: 5px 0;">🔊 Volume: 100%</p>
            <p style="margin: 5px 0;">⚡ Vitesse: Normale (0.85)</p>
            <p style="margin: 5px 0;">🎵 Tonalité: Grave (0.8)</p>
        </div>
        """)
        
        # Raccourcis clavier
        gr.HTML("""
        <div style="background: #fff0f5; padding: 15px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #e91e63;">
            <h4 style="color: #e91e63; margin: 0 0 10px 0;">🎹 Raccourcis Clavier</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-family: monospace;">
                <div><strong>Ctrl + R</strong> : Écouter dernière réponse</div>
                <div><strong>Ctrl + T</strong> : Écouter pensées</div>
                <div><strong>Ctrl + S</strong> : Arrêter la lecture</div>
                <div><strong>Ctrl + A</strong> : Test audio rapide</div>
                <div><strong>Ctrl + +</strong> : Augmenter volume</div>
                <div><strong>Ctrl + -</strong> : Diminuer volume</div>
            </div>
        </div>
        """)
        
        # Tests audio complets
        with gr.Row():
            test_complete_btn = gr.Button("🎧 TEST AUDIO COMPLET", variant="primary", size="lg")
            emergency_stop_btn = gr.Button("🚨 ARRÊT D'URGENCE", variant="stop", size="lg")
        
        # JavaScript pour les contrôles audio
        gr.HTML("""
        <script>
        function updateAudioSettings(volume, speed, pitch) {
            localStorage.setItem('jarvis_volume', volume);
            localStorage.setItem('jarvis_speed', speed);
            localStorage.setItem('jarvis_pitch', pitch);
            
            // Test avec les nouveaux paramètres
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance('Paramètres audio JARVIS mis à jour. Volume ' + Math.round(volume * 100) + ' pourcent, vitesse ' + speed + ', tonalité ' + pitch);
                utterance.volume = volume;
                utterance.rate = speed;
                utterance.pitch = pitch;
                
                const voices = window.speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice => voice.lang.includes('fr'));
                if (frenchVoice) utterance.voice = frenchVoice;
                
                window.speechSynthesis.speak(utterance);
            }
        }
        
        function testCompleteAudio() {
            const volume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
            const speed = parseFloat(localStorage.getItem('jarvis_speed') || '0.85');
            const pitch = parseFloat(localStorage.getItem('jarvis_pitch') || '0.8');
            
            const testMessage = 'Bonjour Jean-Luc. Ceci est un test complet de l\\'interface audio JARVIS. Tous les paramètres sont opérationnels. Volume à ' + Math.round(volume * 100) + ' pourcent, vitesse optimisée, tonalité grave. Interface audio parfaitement fonctionnelle.';
            
            if ('speechSynthesis' in window) {
                window.speechSynthesis.cancel();
                const utterance = new SpeechSynthesisUtterance(testMessage);
                utterance.volume = volume;
                utterance.rate = speed;
                utterance.pitch = pitch;
                
                const voices = window.speechSynthesis.getVoices();
                const frenchVoice = voices.find(voice => voice.lang.includes('fr'));
                if (frenchVoice) utterance.voice = frenchVoice;
                
                utterance.onstart = () => console.log('🎧 Test audio complet démarré');
                utterance.onend = () => console.log('✅ Test audio complet terminé');
                
                window.speechSynthesis.speak(utterance);
            }
        }
        
        function emergencyStop() {
            window.speechSynthesis.cancel();
            console.log('🚨 Arrêt d\\'urgence audio JARVIS');
        }
        
        // Raccourcis clavier avancés
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === '=') {
                event.preventDefault();
                const currentVolume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
                const newVolume = Math.min(1.0, currentVolume + 0.1);
                localStorage.setItem('jarvis_volume', newVolume);
                console.log('🔊 Volume augmenté:', newVolume);
            }
            
            if (event.ctrlKey && event.key === '-') {
                event.preventDefault();
                const currentVolume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
                const newVolume = Math.max(0.1, currentVolume - 0.1);
                localStorage.setItem('jarvis_volume', newVolume);
                console.log('🔉 Volume diminué:', newVolume);
            }
        });
        </script>
        """)
        
        # Fonctions
        def update_volume(volume):
            return f"""
            <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #27ae60;">
                <h4 style="color: #27ae60; margin: 0 0 10px 0;">🔊 Volume Mis à Jour</h4>
                <p style="margin: 5px 0;">Volume JARVIS: {int(volume * 100)}%</p>
                <p style="margin: 5px 0;">⏰ {datetime.datetime.now().strftime('%H:%M:%S')}</p>
            </div>
            """
        
        def update_speed(speed):
            return f"""
            <div style="background: #fff3e0; padding: 15px; border-radius: 10px; border-left: 4px solid #ff9800;">
                <h4 style="color: #ff9800; margin: 0 0 10px 0;">⚡ Vitesse Mise à Jour</h4>
                <p style="margin: 5px 0;">Vitesse JARVIS: {speed}</p>
                <p style="margin: 5px 0;">⏰ {datetime.datetime.now().strftime('%H:%M:%S')}</p>
            </div>
            """
        
        def update_pitch(pitch):
            return f"""
            <div style="background: #f3e5f5; padding: 15px; border-radius: 10px; border-left: 4px solid #9c27b0;">
                <h4 style="color: #9c27b0; margin: 0 0 10px 0;">🎵 Tonalité Mise à Jour</h4>
                <p style="margin: 5px 0;">Tonalité JARVIS: {pitch}</p>
                <p style="margin: 5px 0;">⏰ {datetime.datetime.now().strftime('%H:%M:%S')}</p>
            </div>
            """
        
        def test_complete_audio():
            return """
            <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; border-left: 4px solid #2196f3;">
                <h4 style="color: #2196f3; margin: 0 0 10px 0;">🎧 Test Audio Complet Lancé</h4>
                <p style="margin: 5px 0;">✅ Test de tous les paramètres audio</p>
                <p style="margin: 5px 0;">🔊 Vérification volume, vitesse, tonalité</p>
                <p style="margin: 5px 0;">⏰ """ + datetime.datetime.now().strftime('%H:%M:%S') + """</p>
            </div>
            """
        
        # Connexions
        volume_slider.change(
            fn=update_volume,
            inputs=[volume_slider],
            outputs=[status_audio]
        )
        
        speed_slider.change(
            fn=update_speed,
            inputs=[speed_slider],
            outputs=[status_audio]
        )
        
        pitch_slider.change(
            fn=update_pitch,
            inputs=[pitch_slider],
            outputs=[status_audio]
        )
        
        volume_test_btn.click(
            fn=update_volume,
            inputs=[volume_slider],
            outputs=[status_audio],
            js="(volume) => updateAudioSettings(volume, 0.85, 0.8)"
        )
        
        speed_test_btn.click(
            fn=update_speed,
            inputs=[speed_slider],
            outputs=[status_audio],
            js="(speed) => updateAudioSettings(1.0, speed, 0.8)"
        )
        
        pitch_test_btn.click(
            fn=update_pitch,
            inputs=[pitch_slider],
            outputs=[status_audio],
            js="(pitch) => updateAudioSettings(1.0, 0.85, pitch)"
        )
        
        test_complete_btn.click(
            fn=test_complete_audio,
            outputs=[status_audio],
            js="testCompleteAudio"
        )
        
        emergency_stop_btn.click(
            fn=lambda: """
            <div style="background: #ffebee; padding: 15px; border-radius: 10px; border-left: 4px solid #f44336;">
                <h4 style="color: #f44336; margin: 0 0 10px 0;">🚨 Arrêt d'Urgence Activé</h4>
                <p style="margin: 5px 0;">⏹️ Tous les audios JARVIS arrêtés</p>
                <p style="margin: 5px 0;">⏰ """ + datetime.datetime.now().strftime('%H:%M:%S') + """</p>
            </div>
            """,
            outputs=[status_audio],
            js="emergencyStop"
        )
    
    return interface

if __name__ == "__main__":
    print("🎛️ JARVIS CONTRÔLES AUDIO - JEAN-LUC PASSAVE")
    print("=" * 60)
    print("🔊 Interface de contrôles audio avancés")
    print("⚡ Réglages volume, vitesse, tonalité")
    print("🎹 Raccourcis clavier étendus")
    print("🌐 Interface disponible sur localhost:8112")
    print("=" * 60)
    
    interface = create_audio_controls_interface()
    interface.launch(
        server_name="localhost",
        server_port=8112,
        share=False,
        debug=True,
        show_error=True
    )
