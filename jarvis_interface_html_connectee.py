#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS INTERFACE HTML CONNECTÉE - JEAN-LUC PASSAVE
Interface basée sur votre HTML avec connexion au vrai agent
"""

import gradio as gr
import requests
import time

# Configuration agent
AGENT_URL = "http://localhost:8000/v1/chat/completions"

def test_agent_connection():
    """Test de connexion à l'agent"""
    try:
        response = requests.post(AGENT_URL, json={
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 10
        }, timeout=5)
        return response.status_code == 200
    except:
        return False

def send_message_to_agent(message):
    """Envoie un message à l'agent et récupère la réponse"""
    try:
        response = requests.post(AGENT_URL, json={
            "messages": [{"role": "user", "content": message}],
            "max_tokens": 200
        }, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            return True, content
        else:
            return False, f"Erreur agent: {response.status_code}"
    except Exception as e:
        return False, f"Erreur connexion: {str(e)}"

def process_message(message, history):
    """Traite un message et met à jour l'historique"""
    if not message.strip():
        return history, ""
    
    # Test de connexion
    if not test_agent_connection():
        error_msg = "❌ Agent JARVIS non connecté"
        history.append([message, error_msg])
        return history, ""
    
    # Envoi à l'agent
    success, response = send_message_to_agent(message)
    
    if success:
        history.append([message, response])
    else:
        history.append([message, response])
    
    return history, ""

# Interface HTML personnalisée
html_interface = """
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS - Communication Principale</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #4a4a4a 0%, #8e44ad 100%);
            color: white;
            padding: 15px;
            text-align: center;
            position: relative;
        }

        .nav-buttons {
            position: absolute;
            top: 10px;
            right: 15px;
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
        }

        .main-chat {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .conversation-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .conversation-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .conversation-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .message-area {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 400px;
            border-left: 4px solid #667eea;
            overflow-y: auto;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }

        .user-message {
            background: #e3f2fd;
            text-align: right;
        }

        .agent-message {
            background: #f3e5f5;
        }

        .input-section {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .message-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sidebar-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .thoughts-section {
            background: linear-gradient(135deg, #e91e63, #ad1457);
            color: white;
        }

        .status-indicator {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            color: #2e7d32;
            font-size: 12px;
        }

        .error-indicator {
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            color: #c62828;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="nav-buttons">
            <button class="nav-btn" onclick="goToInterface('8111')">AUDIO</button>
            <button class="nav-btn" onclick="goToInterface('7866')">MAIN</button>
            <button class="nav-btn" onclick="goToInterface('8114')">CONFIG</button>
            <button class="nav-btn" onclick="goToInterface('8260')">SURVEILLANCE</button>
        </div>
        <h1>💬 JARVIS - Communication Principale</h1>
        <p>JARVIS ACTIF - Prêt à communiquer</p>
    </div>

    <div class="container">
        <div class="main-chat">
            <div class="conversation-header">
                <div class="conversation-title">💬 Conversation avec JARVIS</div>
                <div class="conversation-subtitle">Agent connecté sur localhost:8000</div>
            </div>

            <div class="message-area" id="messageArea">
                <div class="status-indicator" id="statusIndicator">
                    ✅ Agent JARVIS connecté et opérationnel
                </div>
            </div>

            <div class="input-section">
                <input type="text" class="message-input" id="messageInput" placeholder="💬 Votre message à JARVIS">
                <button class="send-btn" onclick="sendMessage()">➤ Envoyer</button>
            </div>
        </div>

        <div class="sidebar">
            <div class="sidebar-card thoughts-section">
                <div style="font-size: 16px; font-weight: bold; margin-bottom: 15px;">
                    🧠 Pensées JARVIS
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; margin-bottom: 10px; font-size: 13px;">
                    🧠 En attente de votre message...
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; margin-bottom: 10px; font-size: 13px;">
                    🔄 Système prêt à répondre
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px; font-size: 13px;" id="timeDisplay">
                    ⏰ [--:--:--]
                </div>
            </div>

            <div class="sidebar-card">
                <h3 style="color: #667eea; margin-bottom: 15px;">🚀 Accès Rapide</h3>
                <button onclick="goToInterface('8111')" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; width: 100%; margin-bottom: 10px;">
                    🔊 Interface Audio
                </button>
                <button onclick="goToInterface('8114')" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; width: 100%; margin-bottom: 10px;">
                    ⚙️ Configuration
                </button>
                <button onclick="goToInterface('8260')" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; width: 100%;">
                    📊 Surveillance
                </button>
            </div>
        </div>
    </div>

    <script>
        // Navigation entre interfaces
        function goToInterface(port) {
            localStorage.setItem('jarvis_last_url', window.location.href);
            window.location.href = `http://localhost:${port}`;
        }

        // Envoi de message
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Afficher le message utilisateur
            addMessage(message, 'user');
            input.value = '';
            
            // Envoyer à l'agent via Gradio
            if (window.gradio_app) {
                window.gradio_app.submit();
            }
        }

        // Ajouter un message à l'affichage
        function addMessage(content, type) {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = content;
            messageArea.appendChild(messageDiv);
            messageArea.scrollTop = messageArea.scrollHeight;
        }

        // Entrée pour envoyer
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Mise à jour de l'heure
        setInterval(() => {
            const now = new Date();
            const timeStr = `⏰ [${now.getHours().toString().padStart(2,'0')}:${now.getMinutes().toString().padStart(2,'0')}:${now.getSeconds().toString().padStart(2,'0')}]`;
            document.getElementById('timeDisplay').textContent = timeStr;
        }, 1000);

        // Test de connexion agent
        async function testAgentConnection() {
            try {
                const response = await fetch('http://localhost:8000/v1/chat/completions', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        messages: [{role: 'user', content: 'Test'}],
                        max_tokens: 5
                    })
                });
                
                const indicator = document.getElementById('statusIndicator');
                if (response.ok) {
                    indicator.className = 'status-indicator';
                    indicator.textContent = '✅ Agent JARVIS connecté et opérationnel';
                } else {
                    indicator.className = 'error-indicator';
                    indicator.textContent = '❌ Agent JARVIS non accessible';
                }
            } catch (e) {
                const indicator = document.getElementById('statusIndicator');
                indicator.className = 'error-indicator';
                indicator.textContent = '❌ Agent JARVIS déconnecté';
            }
        }

        // Test initial et périodique
        testAgentConnection();
        setInterval(testAgentConnection, 10000);
    </script>
</body>
</html>
"""

def create_interface():
    """Crée l'interface Gradio avec votre HTML"""
    
    with gr.Blocks(
        title="JARVIS - Communication Principale",
        css="""
        .gradio-container { max-width: none !important; }
        .main { padding: 0 !important; }
        """,
        head="""
        <style>
        body { margin: 0; padding: 0; }
        </style>
        """
    ) as interface:
        
        # HTML principal
        gr.HTML(html_interface)
        
        # Interface cachée pour la communication
        with gr.Row(visible=False):
            message_input = gr.Textbox()
            message_output = gr.Textbox()
            chat_history = gr.State([])
        
        # Fonction de traitement
        message_input.submit(
            fn=process_message,
            inputs=[message_input, chat_history],
            outputs=[chat_history, message_input]
        )
    
    return interface

def main():
    """Fonction principale"""
    print("💬 JARVIS INTERFACE HTML CONNECTÉE - JEAN-LUC PASSAVE")
    print("=" * 60)
    
    # Test de connexion agent
    if test_agent_connection():
        print("✅ Agent JARVIS connecté sur localhost:8000")
    else:
        print("❌ Agent JARVIS non accessible sur localhost:8000")
    
    print("🌐 Interface disponible sur localhost:7866")
    print("🎨 Design basé sur votre HTML")
    print("🔗 Navigation vers autres interfaces")
    print("=" * 60)
    
    # Lancement de l'interface
    interface = create_interface()
    interface.launch(
        server_name="localhost",
        server_port=7866,
        share=False,
        show_error=True
    )

if __name__ == "__main__":
    main()
