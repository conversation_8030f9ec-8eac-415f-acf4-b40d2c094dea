#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 JARVIS SURVEILLANCE ACTIVITÉ - VERSION SIMPLE
Interface de surveillance uniquement - Jean-<PERSON> Passave
Port 8101 - Dashboard Principal avec navigation
"""

import gradio as gr
import json
import os
import time
from datetime import datetime, timedelta
import webbrowser

# ============================================================================
# CONFIGURATION
# ============================================================================

SURVEILLANCE_PORT = 8260
DASHBOARD_URL = "http://localhost:8101"

# ============================================================================
# FONCTIONS DE SURVEILLANCE
# ============================================================================

def charger_donnees_surveillance():
    """Charge les données de surveillance depuis les fichiers"""
    try:
        # Données simulées pour l'exemple
        return {
            "pensees_autonomes": 5,
            "taches_accomplies": 12,
            "sessions": 2,
            "activites": [
                {"heure": "17:38:17", "type": "surveillance_intelligente", "description": "Surveillance intelligente automatique - CPU 11.8%, RAM 81.6%"},
                {"heure": "17:38:18", "type": "surveillance_intelligente", "description": "Surveillance intelligente automatique - CPU 25.2%, RAM 79.5%"},
                {"heure": "17:38:19", "type": "surveillance_intelligente", "description": "Surveillance intelligente automatique - CPU 42.8%, RAM 81.1%"},
                {"heure": "17:38:20", "type": "surveillance_intelligente", "description": "Surveillance intelligente automatique - CPU 30.9%, RAM 86.3%"},
                {"heure": "17:38:21", "type": "test_diamond", "description": "Test diamond: 1 fois"},
                {"heure": "17:38:22", "type": "optimisation_systeme", "description": "Optimisation système: 1 fois"},
                {"heure": "17:38:23", "type": "apprentissage", "description": "Apprentissage: 1 fois"},
                {"heure": "17:38:24", "type": "maintenance", "description": "Maintenance: 1 fois"},
                {"heure": "17:38:25", "type": "generation_creative", "description": "Génération créative: 1 fois"},
                {"heure": "17:38:26", "type": "brainstorming", "description": "Brainstorming: 1 fois"},
                {"heure": "17:38:27", "type": "apprentissage", "description": "Apprentissage: 1 fois"},
                {"heure": "17:38:28", "type": "analyse_memoire", "description": "Analyse mémoire: 1 fois"}
            ],
            "dernieres_activites": [
                "17:38:17 [surveillance_intelligente] Surveillance intelligente automatique - CPU 11.8%, RAM 81.6%",
                "17:38:18 [surveillance_intelligente] Surveillance intelligente automatique - CPU 25.2%, RAM 79.5%", 
                "17:38:19 [surveillance_intelligente] Surveillance intelligente automatique - CPU 42.8%, RAM 81.1%",
                "17:38:20 [surveillance_intelligente] Surveillance intelligente automatique - CPU 30.9%, RAM 86.3%",
                "17:38:21 [test_diamond] Test diamond: 1 fois",
                "17:38:22 [optimisation_systeme] Optimisation système: 1 fois",
                "17:38:23 [apprentissage] Apprentissage: 1 fois",
                "17:38:24 [maintenance] Maintenance: 1 fois",
                "17:38:25 [generation_creative] Génération créative: 1 fois",
                "17:38:26 [brainstorming] Brainstorming: 1 fois"
            ]
        }
    except Exception as e:
        return {
            "pensees_autonomes": 0,
            "taches_accomplies": 0, 
            "sessions": 0,
            "activites": [],
            "dernieres_activites": [f"Erreur chargement: {str(e)}"]
        }

def generer_rapport_activite():
    """Génère le rapport d'activité"""
    donnees = charger_donnees_surveillance()
    
    # Compter les activités par type
    activites_par_type = {}
    for activite in donnees["activites"]:
        type_act = activite["type"]
        if type_act in activites_par_type:
            activites_par_type[type_act] += 1
        else:
            activites_par_type[type_act] = 1
    
    # Générer le HTML du rapport
    html_rapport = f"""
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 15px; color: white; margin: 10px 0;">
        <h2 style="margin: 0 0 15px 0; color: white;">📊 RAPPORT D'ACTIVITÉ JARVIS - JEAN-LUC PASSAVE</h2>
        <p style="margin: 5px 0; font-size: 14px;">Période: Dernières 24 heures</p>
        
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 20px 0;">
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; text-align: center;">
                <h3 style="margin: 0; color: #4CAF50;">🧠 Pensées autonomes</h3>
                <p style="font-size: 24px; margin: 5px 0; font-weight: bold;">{donnees['pensees_autonomes']}</p>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; text-align: center;">
                <h3 style="margin: 0; color: #2196F3;">✅ Tâches accomplies</h3>
                <p style="font-size: 24px; margin: 5px 0; font-weight: bold;">{donnees['taches_accomplies']}</p>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; text-align: center;">
                <h3 style="margin: 0; color: #FF9800;">🔄 Sessions</h3>
                <p style="font-size: 24px; margin: 5px 0; font-weight: bold;">{donnees['sessions']}</p>
            </div>
        </div>
        
        <h3 style="color: white; margin: 20px 0 10px 0;">🎯 ACTIVITÉS PAR TYPE:</h3>
        <ul style="list-style: none; padding: 0;">
    """
    
    for type_act, count in activites_par_type.items():
        html_rapport += f'<li style="margin: 5px 0; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 5px;">🔹 {type_act}: {count} fois</li>'
    
    html_rapport += """
        </ul>
        
        <h3 style="color: white; margin: 20px 0 10px 0;">⏰ SESSIONS UTILISATEUR:</h3>
        <ul style="list-style: none; padding: 0;">
            <li style="margin: 5px 0; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 5px;">📅 2025-06-24 17:45:34 et 17:46:10 - 0 min - 3 activités</li>
            <li style="margin: 5px 0; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 5px;">📅 2025-06-24 17:41:33 (en cours) - 0 min - Aucune activité</li>
        </ul>
    </div>
    """
    
    return html_rapport

def generer_liste_activites():
    """Génère la liste des dernières activités"""
    donnees = charger_donnees_surveillance()
    
    html_activites = """
    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; max-height: 400px; overflow-y: auto;">
        <h3 style="margin: 0 0 15px 0; color: #333;">🕐 DERNIÈRES ACTIVITÉS:</h3>
        <ul style="list-style: none; padding: 0; margin: 0;">
    """
    
    for activite in donnees["dernieres_activites"]:
        html_activites += f'<li style="margin: 5px 0; padding: 8px; background: white; border-radius: 5px; border-left: 3px solid #667eea; font-family: monospace; font-size: 12px;">{activite}</li>'
    
    html_activites += """
        </ul>
    </div>
    """
    
    return html_activites

# ============================================================================
# INTERFACE SURVEILLANCE
# ============================================================================

def creer_interface_surveillance():
    """Crée l'interface de surveillance avec navigation"""
    
    with gr.Blocks(
        title="🎯 JARVIS Surveillance Activité - Jean-Luc Passave",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .gr-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-weight: bold;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .gr-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .home-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049) !important;
            color: white !important;
            font-size: 16px !important;
            font-weight: bold !important;
            padding: 12px 25px !important;
            border-radius: 12px !important;
            border: 2px solid #4CAF50 !important;
            box-shadow: 0 3px 12px rgba(76, 175, 80, 0.4) !important;
            min-height: 50px !important;
        }
        .home-btn:hover {
            background: linear-gradient(45deg, #45a049, #388e3c) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6) !important;
        }
        .refresh-btn {
            background: linear-gradient(45deg, #2196F3, #1976D2) !important;
            color: white !important;
            font-size: 14px !important;
            font-weight: bold !important;
            padding: 10px 20px !important;
            border-radius: 10px !important;
            border: 2px solid #2196F3 !important;
            box-shadow: 0 3px 12px rgba(33, 150, 243, 0.4) !important;
            min-height: 45px !important;
        }
        .refresh-btn:hover {
            background: linear-gradient(45deg, #1976D2, #1565C0) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6) !important;
        }
        """
    ) as interface:
        
        # En-tête avec navigation - BOUTONS TRÈS VISIBLES
        with gr.Row():
            with gr.Column(scale=1):
                home_btn = gr.Button(
                    "🏠 Dashboard Principal",
                    variant="primary",
                    size="lg",
                    elem_classes=["home-btn"]
                )
            with gr.Column(scale=3):
                gr.HTML("""
                <div style="text-align: center; padding: 10px;">
                    <h1 style="color: white; margin: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        🎯 SURVEILLANCE ACTIVITÉ JARVIS
                    </h1>
                    <p style="color: rgba(255,255,255,0.9); margin: 5px 0;">
                        Jean-Luc Passave - Voir tout ce que JARVIS fait quand vous n'êtes pas là
                    </p>
                </div>
                """)
            with gr.Column(scale=1):
                refresh_btn = gr.Button(
                    "🔄 Actualiser",
                    variant="secondary",
                    size="lg",
                    elem_classes=["refresh-btn"]
                )
        
        # Contenu principal
        with gr.Row():
            with gr.Column():
                # Rapport d'activité
                rapport_html = gr.HTML(value=generer_rapport_activite())
                
                # Liste des activités
                activites_html = gr.HTML(value=generer_liste_activites())
        
        # Fonctions de navigation
        def aller_dashboard():
            """Redirige vers le dashboard principal"""
            webbrowser.open("http://localhost:8101")
            return "🏠 Redirection vers Dashboard Principal..."
        
        def actualiser_donnees():
            """Actualise les données de surveillance"""
            return generer_rapport_activite(), generer_liste_activites()
        
        # Connexions
        home_btn.click(fn=aller_dashboard, outputs=[])
        refresh_btn.click(
            fn=actualiser_donnees,
            outputs=[rapport_html, activites_html]
        )
    
    return interface

# ============================================================================
# LANCEMENT
# ============================================================================

if __name__ == "__main__":
    print("🎯 ================================")
    print("🎯 JARVIS SURVEILLANCE ACTIVITÉ")
    print("🎯 ================================")
    print(f"🌐 Interface: http://localhost:{SURVEILLANCE_PORT}")
    print("👤 Utilisateur: Jean-Luc Passave")
    print("📊 Surveillance des activités JARVIS")
    print("🔄 Actualisation automatique")
    print("=" * 50)
    
    # Créer et lancer l'interface
    interface = creer_interface_surveillance()
    interface.launch(
        server_name="0.0.0.0",
        server_port=SURVEILLANCE_PORT,
        share=False,
        show_error=True,
        quiet=False
    )
