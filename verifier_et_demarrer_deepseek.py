#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 VÉRIFICATEUR ET DÉMARREUR DEEPSEEK R1 - JEAN-LUC PASSAVE
Vérifie si DeepSeek R1 est actif et le démarre si nécessaire
"""

import subprocess
import requests
import time
import json
import os
from datetime import datetime

def verifier_deepseek_actif():
    """VÉRIFIER SI DEEPSEEK R1 EST ACTIF"""
    try:
        # Test de connexion simple
        response = requests.get("http://localhost:8000/v1/models", timeout=5)
        if response.status_code == 200:
            print("✅ DEEPSEEK R1 EST ACTIF")
            return True
        else:
            print(f"❌ DEEPSEEK R1 RÉPOND MAIS ERREUR: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ DEEPSEEK R1 NON ACCESSIBLE - Port 8000 fermé")
        return False
    except requests.exceptions.Timeout:
        print("❌ DEEPSEEK R1 TIMEOUT - Trop lent à répondre")
        return False
    except Exception as e:
        print(f"❌ ERREUR VÉRIFICATION DEEPSEEK: {e}")
        return False

def tester_deepseek_complet():
    """TESTER DEEPSEEK AVEC UNE VRAIE REQUÊTE"""
    try:
        test_data = {
            "model": "deepseek-r1:8b",
            "messages": [
                {
                    "role": "user",
                    "content": "Dis juste 'TEST_OK' pour confirmer que tu fonctionnes."
                }
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=test_data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            if 'TEST_OK' in content or 'OK' in content:
                print("✅ DEEPSEEK R1 FONCTIONNE PARFAITEMENT")
                return True
            else:
                print(f"⚠️ DEEPSEEK R1 RÉPOND MAIS CONTENU INATTENDU: {content}")
                return True  # Il fonctionne quand même
        else:
            print(f"❌ DEEPSEEK R1 ERREUR REQUÊTE: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR TEST DEEPSEEK: {e}")
        return False

def chercher_processus_deepseek():
    """CHERCHER PROCESSUS DEEPSEEK ACTIFS"""
    try:
        # Chercher processus avec 'ollama' ou 'deepseek'
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        processus_deepseek = []
        for line in lines:
            if 'ollama' in line.lower() or 'deepseek' in line.lower():
                processus_deepseek.append(line.strip())
        
        if processus_deepseek:
            print("🔍 PROCESSUS DEEPSEEK TROUVÉS:")
            for proc in processus_deepseek:
                print(f"   {proc}")
            return True
        else:
            print("❌ AUCUN PROCESSUS DEEPSEEK TROUVÉ")
            return False
            
    except Exception as e:
        print(f"❌ ERREUR RECHERCHE PROCESSUS: {e}")
        return False

def demarrer_deepseek_ollama():
    """DÉMARRER DEEPSEEK VIA OLLAMA"""
    try:
        print("🚀 TENTATIVE DÉMARRAGE DEEPSEEK R1 VIA OLLAMA...")
        
        # Vérifier si ollama est installé
        result = subprocess.run(['which', 'ollama'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ OLLAMA NON INSTALLÉ")
            return False
        
        print("✅ OLLAMA TROUVÉ")
        
        # Démarrer ollama serve en arrière-plan
        print("🚀 Démarrage ollama serve...")
        ollama_process = subprocess.Popen(
            ['ollama', 'serve'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Attendre un peu
        time.sleep(5)
        
        # Vérifier si le processus est toujours actif
        if ollama_process.poll() is None:
            print("✅ OLLAMA SERVE DÉMARRÉ")
            
            # Attendre encore un peu pour que le serveur soit prêt
            time.sleep(3)
            
            # Essayer de charger le modèle deepseek-r1:8b
            print("📥 Chargement modèle deepseek-r1:8b...")
            load_result = subprocess.run(
                ['ollama', 'run', 'deepseek-r1:8b', 'test'],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if load_result.returncode == 0:
                print("✅ MODÈLE DEEPSEEK-R1:8B CHARGÉ")
                return True
            else:
                print(f"❌ ERREUR CHARGEMENT MODÈLE: {load_result.stderr}")
                return False
        else:
            print("❌ OLLAMA SERVE N'A PAS DÉMARRÉ")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ TIMEOUT CHARGEMENT MODÈLE - Mais ollama peut être actif")
        return True  # Ollama peut être actif même si le test timeout
    except Exception as e:
        print(f"❌ ERREUR DÉMARRAGE OLLAMA: {e}")
        return False

def afficher_instructions_manuelles():
    """AFFICHER INSTRUCTIONS POUR DÉMARRAGE MANUEL"""
    print("\n" + "=" * 60)
    print("📋 INSTRUCTIONS DÉMARRAGE MANUEL DEEPSEEK R1")
    print("=" * 60)
    print("1️⃣ Ouvrir un terminal")
    print("2️⃣ Exécuter: ollama serve")
    print("3️⃣ Dans un autre terminal: ollama run deepseek-r1:8b")
    print("4️⃣ Attendre que le modèle se charge")
    print("5️⃣ Relancer ce script pour vérifier")
    print("=" * 60)
    print("💡 OU installer Ollama si pas installé:")
    print("   curl -fsSL https://ollama.ai/install.sh | sh")
    print("=" * 60)

def main():
    """FONCTION PRINCIPALE"""
    print("🚀 VÉRIFICATEUR ET DÉMARREUR DEEPSEEK R1 - JEAN-LUC PASSAVE")
    print("=" * 60)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. Vérifier si DeepSeek est déjà actif
    print("1️⃣ VÉRIFICATION DEEPSEEK R1...")
    if verifier_deepseek_actif():
        # Test complet
        if tester_deepseek_complet():
            print("\n🎉 DEEPSEEK R1 EST PARFAITEMENT OPÉRATIONNEL !")
            print("✅ Vous pouvez maintenant lancer JARVIS")
            return True
    
    # 2. Chercher processus existants
    print("\n2️⃣ RECHERCHE PROCESSUS DEEPSEEK...")
    chercher_processus_deepseek()
    
    # 3. Tentative de démarrage automatique
    print("\n3️⃣ TENTATIVE DÉMARRAGE AUTOMATIQUE...")
    if demarrer_deepseek_ollama():
        # Attendre un peu et re-vérifier
        print("⏰ Attente stabilisation (10 secondes)...")
        time.sleep(10)
        
        if verifier_deepseek_actif() and tester_deepseek_complet():
            print("\n🎉 DEEPSEEK R1 DÉMARRÉ AVEC SUCCÈS !")
            print("✅ Vous pouvez maintenant lancer JARVIS")
            return True
    
    # 4. Échec - Instructions manuelles
    print("\n❌ DÉMARRAGE AUTOMATIQUE ÉCHOUÉ")
    afficher_instructions_manuelles()
    
    return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Relancez ce script après avoir démarré DeepSeek manuellement")
        exit(1)
    else:
        print("\n🚀 PRÊT POUR JARVIS !")
        exit(0)
