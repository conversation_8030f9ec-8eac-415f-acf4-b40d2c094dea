#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS TEST SIMPLE - JEAN-LUC PASSAVE
Test rapide pour vérifier la communication DeepSeek R1 8B
"""

import gradio as gr
import requests
import datetime
import json

# Configuration simple
SERVER_URL = "http://localhost:8000/v1/chat/completions"
MODEL_NAME = "jan-nano"

def test_deepseek_simple(message):
    """Test simple de communication avec DeepSeek R1 8B"""
    try:
        if not message.strip():
            return "Veuillez saisir un message."

        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "system",
                    "content": "Tu es JARVIS, l'assistant <PERSON><PERSON> <PERSON><PERSON>. Réponds en français de manière naturelle et utile."
                },
                {"role": "user", "content": message}
            ],
            "max_tokens": 300,
            "temperature": 0.8,
            "stream": False
        }

        response = requests.post(SERVER_URL, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            return f"❌ Erreur serveur: {response.status_code}"

    except requests.exceptions.ConnectionError:
        return "❌ Impossible de se connecter au serveur DeepSeek R1 8B (localhost:8000)"
    except requests.exceptions.Timeout:
        return "⏱️ Timeout - Le serveur met trop de temps à répondre"
    except Exception as e:
        return f"❌ Erreur: {str(e)}"

def process_message(message, history):
    """Traite un message simple"""
    if not message.strip():
        return history, ""
    
    response = test_deepseek_simple(message)
    
    # Ajouter à l'historique
    history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
    history.append({"role": "assistant", "content": f"🤖 JARVIS: {response}"})
    
    return history, ""

def create_simple_interface():
    """Interface de test simple"""
    
    with gr.Blocks(
        title="🧪 JARVIS Test Simple",
        theme=gr.themes.Soft()
    ) as interface:
        
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 20px; text-align: center; border-radius: 10px; margin-bottom: 20px;">
            <h1>🧪 JARVIS Test Simple</h1>
            <p>Test rapide de communication avec DeepSeek R1 8B</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column():
                chat = gr.Chatbot(
                    value=[],
                    height=400,
                    label="💬 Test Communication JARVIS",
                    show_copy_button=True,
                    avatar_images=("👨‍💻", "🤖"),
                    type="messages"
                )
                
                with gr.Row():
                    user_input = gr.Textbox(
                        value="",
                        label="💬 Votre message de test",
                        scale=4,
                        lines=1,
                        placeholder="Tapez votre message..."
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)
                
                # Boutons de test rapide
                with gr.Row():
                    test1_btn = gr.Button("🧪 Test 1: Salut", variant="secondary")
                    test2_btn = gr.Button("🧪 Test 2: Comment ça va ?", variant="secondary")
                    test3_btn = gr.Button("🧪 Test 3: Qui es-tu ?", variant="secondary")
                
                # Statut
                status_display = gr.HTML("""
                <div style="background: #f0f0f0; padding: 10px; border-radius: 5px; margin-top: 10px;">
                    <strong>📊 Statut:</strong> Interface de test prête
                </div>
                """)
        
        # Fonctions de test
        def test_message_1():
            return process_message("Salut JARVIS", [])
        
        def test_message_2():
            return process_message("Comment ça va ?", [])
        
        def test_message_3():
            return process_message("Qui es-tu ?", [])
        
        def clear_chat():
            return []
        
        def update_status(message):
            current_time = datetime.datetime.now().strftime("%H:%M:%S")
            return f"""
            <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <strong>📊 Statut [{current_time}]:</strong> Message envoyé: "{message[:30]}..."
            </div>
            """
        
        # Connexions
        send_btn.click(
            fn=process_message,
            inputs=[user_input, chat],
            outputs=[chat, user_input]
        ).then(
            fn=update_status,
            inputs=[user_input],
            outputs=[status_display]
        )
        
        user_input.submit(
            fn=process_message,
            inputs=[user_input, chat],
            outputs=[chat, user_input]
        )
        
        test1_btn.click(
            fn=test_message_1,
            outputs=[chat, user_input]
        )
        
        test2_btn.click(
            fn=test_message_2,
            outputs=[chat, user_input]
        )
        
        test3_btn.click(
            fn=test_message_3,
            outputs=[chat, user_input]
        )
    
    return interface

if __name__ == "__main__":
    print("🧪 JARVIS TEST SIMPLE - JEAN-LUC PASSAVE")
    print("=" * 50)
    print("🔧 Test rapide DeepSeek R1 8B")
    print("🌐 Interface: localhost:8110")
    print("=" * 50)
    
    interface = create_simple_interface()
    interface.launch(
        server_name="localhost",
        server_port=8110,
        share=False,
        debug=True,
        show_error=True
    )
