
# 🔧 FALLBACK DIFFUSERS - JEAN-LUC PASSAVE
"""
Fallback pour diffusers quand le module n'est pas disponible
Utilise des images par défaut et des messages informatifs
"""

import os
import json
from datetime import datetime

class StableDiffusionPipeline:
    """Fallback pour StableDiffusionPipeline"""
    
    def __init__(self, *args, **kwargs):
        self.model_id = kwargs.get('model_id', 'fallback-model')
        print(f"⚠️ Diffusers non disponible - Mode fallback activé")
    
    @classmethod
    def from_pretrained(cls, model_id, **kwargs):
        return cls(model_id=model_id, **kwargs)
    
    def to(self, device):
        return self
    
    def __call__(self, prompt, **kwargs):
        """Simuler la génération d'image"""
        print(f"🎨 Génération d'image simulée pour: {prompt[:50]}...")
        
        # Créer un résultat simulé
        class ImageResult:
            def __init__(self, prompt):
                self.prompt = prompt
                self.images = [self.create_placeholder()]
            
            def create_placeholder(self):
                """Créer une image placeholder"""
                class PlaceholderImage:
                    def save(self, path):
                        # Créer un fichier texte au lieu d'une image
                        info = {
                            "type": "image_placeholder",
                            "prompt": self.prompt,
                            "timestamp": datetime.now().isoformat(),
                            "note": "Image générée en mode fallback - diffusers non disponible"
                        }
                        
                        txt_path = path.replace('.png', '.txt').replace('.jpg', '.txt')
                        with open(txt_path, 'w', encoding='utf-8') as f:
                            json.dump(info, f, ensure_ascii=False, indent=2)
                        
                        print(f"💾 Placeholder sauvegardé: {txt_path}")
                
                placeholder = PlaceholderImage()
                placeholder.prompt = self.prompt
                return placeholder
        
        return ImageResult(prompt)

def DiffusionPipeline():
    """Fallback pour DiffusionPipeline"""
    return StableDiffusionPipeline()

print("✅ Diffusers Fallback initialisé - Mode simulation activé")
