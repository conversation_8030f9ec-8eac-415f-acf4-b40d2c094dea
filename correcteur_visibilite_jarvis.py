import time
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTEUR VISIBILITÉ JARVIS - JEAN-LUC PASSAVE
Corrige automatiquement tous les problèmes de contraste et visibilité
"""

import re
import os
from datetime import datetime

def corriger_elements_blancs(contenu):
    """Corrige tous les éléments avec background blanc"""
    
    # Patterns de correction
    corrections = [
        # Éléments avec background: white
        (
            r'background:\s*white;([^}]*?)(?=color:\s*[^;]*;|$)',
            r'background: #2c3e50; color: #ecf0f1; border: 1px solid #3498db;'
        ),
        
        # Divs avec background: white
        (
            r'<div style="([^"]*?)background:\s*white([^"]*?)"',
            r'<div style="\1background: #2c3e50; color: #ecf0f1; border: 1px solid #3498db\2"'
        ),
        
        # Éléments de recherche blancs
        (
            r'<div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">',
            r'<div style="margin: 10px 0; padding: 10px; background: #34495e; color: #ecf0f1; border-radius: 5px; border: 1px solid #3498db;">'
        ),
        
        # Historique blanc
        (
            r'<div style="margin: 10px 0; padding: 10px; background: white; border-radius: 5px;">',
            r'<div style="margin: 10px 0; padding: 10px; background: #2c3e50; color: #ecf0f1; border-radius: 5px; border: 1px solid #27ae60;">'
        ),
        
        # Résultats de recherche blancs
        (
            r'<div style="margin: 15px 0; padding: 10px; background: white; border-radius: 5px;">',
            r'<div style="margin: 15px 0; padding: 10px; background: #34495e; color: #ecf0f1; border-radius: 5px; border: 1px solid #f39c12;">'
        ),
        
        # Corrections spécifiques pour les violations
        (
            r'<div style=\'background: white; padding: 10px; border-radius: 5px; margin: 10px 0;\'>',
            r'<div style=\'background: #2c3e50; color: #ecf0f1; padding: 10px; border-radius: 5px; margin: 10px 0; border: 1px solid #e74c3c;\'>'
        ),
        
        # Corrections pour les projets
        (
            r'background: #f9f9f9;([^}]*?)color: #666;',
            r'background: #2c3e50; color: #ecf0f1; border: 1px solid #3498db;'
        ),
        
        # Strong tags dans éléments blancs
        (
            r'<strong>([^<]*?)</strong>',
            lambda m: f'<strong style="color: #3498db;">{m.group(1)}</strong>'
        ),
        
        # Small tags dans éléments blancs
        (
            r'<small>([^<]*?)</small>',
            lambda m: f'<small style="color: #bdc3c7;">{m.group(1)}</small>'
        )
    ]
    
    contenu_corrige = contenu
    corrections_appliquees = 0
    
    for pattern, remplacement in corrections:
        if callable(remplacement):
            # Pour les fonctions lambda
            matches = re.findall(pattern, contenu_corrige)
            if matches:
                contenu_corrige = re.sub(pattern, remplacement, contenu_corrige)
                corrections_appliquees += len(matches)
        else:
            # Pour les remplacements simples
            matches = re.findall(pattern, contenu_corrige)
            if matches:
                contenu_corrige = re.sub(pattern, remplacement, contenu_corrige)
                corrections_appliquees += len(matches)
    
    return contenu_corrige, corrections_appliquees

def corriger_css_vides(contenu):
    """Corrige les sections CSS vides"""
    
    # Patterns CSS vides à corriger
    css_corrections = [
        # Sections CSS complètement vides
        (
            r'p, span, div, label, td, th, li, h1, h2, h3, h4, h5, h6 \{\s*\}',
            '''p, span, div, label, td, th, li, h1, h2, h3, h4, h5, h6 {
    color: #ecf0f1 !important;
    font-weight: 500 !important;
}'''
        ),
        
        # Boutons vides
        (
            r'button, \.btn, \.gr-button \{\s*\}',
            '''button, .btn, .gr-button {
    background: linear-gradient(45deg, #3498db, #2980b9) !important;
    color: #ffffff !important;
    border: none !important;
    font-weight: bold !important;
}'''
        ),
        
        # Conteneurs vides
        (
            r'\.container, \.card, \.panel, \.window-card \{\s*\}',
            '''.container, .card, .panel, .window-card {
    background: #2c3e50 !important;
    color: #ecf0f1 !important;
    border: 1px solid #3498db !important;
    padding: 15px !important;
    border-radius: 8px !important;
}'''
        ),
        
        # Inputs vides
        (
            r'input, textarea, select \{\s*\}',
            '''input, textarea, select {
    background: #34495e !important;
    color: #ecf0f1 !important;
    border: 1px solid #3498db !important;
    padding: 8px !important;
    border-radius: 5px !important;
}'''
        )
    ]
    
    contenu_corrige = contenu
    corrections_css = 0
    
    for pattern, remplacement in css_corrections:
        matches = re.findall(pattern, contenu_corrige, re.MULTILINE | re.DOTALL)
        if matches:
            contenu_corrige = re.sub(pattern, remplacement, contenu_corrige, flags=re.MULTILINE | re.DOTALL)
            corrections_css += len(matches)
    
    return contenu_corrige, corrections_css

def analyser_problemes_visibilite(fichier_path):
    """Analyse les problèmes de visibilité dans le fichier"""
    
    print(f"🔍 ANALYSE VISIBILITÉ: {fichier_path}")
    
    with open(fichier_path, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    problemes = []
    
    # 1. Éléments avec background blanc
    elements_blancs = re.findall(r'background:\s*white', contenu)
    if elements_blancs:
        problemes.append(f"BACKGROUND_BLANC: {len(elements_blancs)} éléments")
    
    # 2. CSS vides
    css_vides = re.findall(r'\{[\s\n]*\}', contenu)
    if css_vides:
        problemes.append(f"CSS_VIDES: {len(css_vides)} sections")
    
    # 3. Couleurs problématiques
    couleurs_problematiques = re.findall(r'color:\s*#666', contenu)
    if couleurs_problematiques:
        problemes.append(f"COULEURS_GRISES: {len(couleurs_problematiques)} éléments")
    
    # 4. Texte transparent
    texte_transparent = re.findall(r'color:\s*transparent', contenu)
    if texte_transparent:
        problemes.append(f"TEXTE_TRANSPARENT: {len(texte_transparent)} éléments")
    
    return problemes

def corriger_fichier_jarvis():
    """Corrige le fichier JARVIS principal"""
    
    fichier_principal = "jarvis_architecture_multi_fenetres.py"
    
    if not os.path.exists(fichier_principal):
        print(f"❌ Fichier {fichier_principal} non trouvé")
        return False
    
    print("🔧 CORRECTEUR VISIBILITÉ JARVIS - JEAN-LUC PASSAVE")
    print("=" * 60)
    
    # Analyse initiale
    problemes_initiaux = analyser_problemes_visibilite(fichier_principal)
    print(f"📊 PROBLÈMES DÉTECTÉS: {len(problemes_initiaux)}")
    for probleme in problemes_initiaux:
        print(f"   • {probleme}")
    
    # Sauvegarde
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    fichier_backup = f"{fichier_principal}.backup_visibilite_{timestamp}"
    
    with open(fichier_principal, 'r', encoding='utf-8') as f:
        contenu_original = f.read()
    
    with open(fichier_backup, 'w', encoding='utf-8') as f:
        f.write(contenu_original)
    
    print(f"💾 Sauvegarde créée: {fichier_backup}")
    
    # Corrections
    print("\n🔧 APPLICATION DES CORRECTIONS...")
    
    contenu_corrige = contenu_original
    total_corrections = 0
    
    # 1. Corriger éléments blancs
    contenu_corrige, corrections_blancs = corriger_elements_blancs(contenu_corrige)
    total_corrections += corrections_blancs
    print(f"   ✅ Éléments blancs corrigés: {corrections_blancs}")
    
    # 2. Corriger CSS vides
    contenu_corrige, corrections_css = corriger_css_vides(contenu_corrige)
    total_corrections += corrections_css
    print(f"   ✅ Sections CSS corrigées: {corrections_css}")
    
    # Écriture du fichier corrigé
    with open(fichier_principal, 'w', encoding='utf-8') as f:
        f.write(contenu_corrige)
    
    print(f"\n🎯 CORRECTIONS TERMINÉES")
    print(f"   📊 Total corrections appliquées: {total_corrections}")
    print(f"   📁 Fichier corrigé: {fichier_principal}")
    print(f"   💾 Sauvegarde: {fichier_backup}")
    
    # Analyse finale
    problemes_finaux = analyser_problemes_visibilite(fichier_principal)
    print(f"\n📈 RÉSULTATS:")
    print(f"   • Problèmes initiaux: {len(problemes_initiaux)}")
    print(f"   • Problèmes restants: {len(problemes_finaux)}")
    print(f"   • Amélioration: {len(problemes_initiaux) - len(problemes_finaux)} problèmes résolus")
    
    if len(problemes_finaux) == 0:
        print("🏆 PARFAIT! Tous les problèmes de visibilité ont été corrigés!")
    else:
        print("⚠️ Problèmes restants:")
        for probleme in problemes_finaux:
            print(f"     • {probleme}")
    
    return True

if __name__ == "__main__":
    success = corriger_fichier_jarvis()
    if success:
        print("\n✅ CORRECTION TERMINÉE AVEC SUCCÈS")
        print("🚀 Relancez JARVIS pour voir les améliorations!")
    else:
        print("\n❌ ERREUR LORS DE LA CORRECTION")
