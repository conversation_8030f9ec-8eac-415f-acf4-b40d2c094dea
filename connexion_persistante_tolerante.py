#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CONNEXION PERSISTANTE TOLÉRANTE - JEAN-LUC PASSAVE
Version tolérante aux timeouts et lenteurs de DeepSeek
"""

import threading
import time
import requests
import json
from datetime import datetime

class ConnexionPersistanteTolerant:
    """CONNEXION PERSISTANTE TOLÉRANTE AUX TIMEOUTS"""
    
    def __init__(self):
        self.connexion_active = False
        self.thread_keepalive = None
        self.derniere_activite = datetime.now()
        self.url_deepseek = "http://localhost:8000/v1/chat/completions"
        self.intervalle_keepalive = 60  # 1 minute (plus long)
        self.timeout_ping = 45  # 45 secondes timeout
        self.echecs_consecutifs = 0
        self.max_echecs = 3
        
    def demarrer_keepalive(self):
        """DÉMARRER KEEP-ALIVE TOLÉRANT"""
        if self.connexion_active:
            return
        
        self.connexion_active = True
        self.thread_keepalive = threading.Thread(target=self._boucle_keepalive, daemon=True)
        self.thread_keepalive.start()
        
        print("🚀 CONNEXION PERSISTANTE TOLÉRANTE DÉMARRÉE")
        print(f"⏰ Vérification toutes les {self.intervalle_keepalive} secondes")
        print(f"🕐 Timeout ping: {self.timeout_ping} secondes")
    
    def _boucle_keepalive(self):
        """BOUCLE KEEP-ALIVE TOLÉRANTE"""
        while self.connexion_active:
            try:
                time.sleep(self.intervalle_keepalive)
                
                # Ping tolérant
                if self._ping_tolerant():
                    self.echecs_consecutifs = 0
                    print(f"✅ Ping OK ({datetime.now().strftime('%H:%M:%S')})")
                else:
                    self.echecs_consecutifs += 1
                    print(f"⚠️ Ping échoué ({self.echecs_consecutifs}/{self.max_echecs})")
                    
                    if self.echecs_consecutifs >= self.max_echecs:
                        print("❌ Trop d'échecs - Pause longue")
                        time.sleep(300)  # Pause 5 minutes
                        self.echecs_consecutifs = 0
                
            except Exception as e:
                print(f"❌ Erreur keep-alive: {e}")
                time.sleep(120)  # Pause 2 minutes
    
    def _ping_tolerant(self):
        """PING TOLÉRANT AUX TIMEOUTS"""
        try:
            # Test connexion simple d'abord
            response = requests.get("http://localhost:8000/v1/models", timeout=5)
            if response.status_code != 200:
                return False
            
            # Ping minimal
            ping_data = {
                "model": "deepseek-r1:8b",
                "messages": [{"role": "user", "content": "ping"}],
                "max_tokens": 3,
                "temperature": 0.1
            }
            
            response = requests.post(
                self.url_deepseek,
                json=ping_data,
                timeout=self.timeout_ping
            )
            
            return response.status_code == 200
            
        except requests.exceptions.Timeout:
            print("⏰ Ping timeout - DeepSeek lent mais probablement OK")
            return True  # On tolère les timeouts
        except Exception as e:
            print(f"❌ Ping erreur: {e}")
            return False

# Instance globale
connexion_tolerante = ConnexionPersistanteTolerant()

def demarrer_connexion_tolerante():
    """DÉMARRER CONNEXION TOLÉRANTE"""
    connexion_tolerante.demarrer_keepalive()
    return True

if __name__ == "__main__":
    print("🔧 TEST CONNEXION PERSISTANTE TOLÉRANTE")
    demarrer_connexion_tolerante()
    
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        print("\n✅ ARRÊT CONNEXION TOLÉRANTE")
