
    <!-- BOUTONS CONNECTIVITÉ JARVIS -->
    <div id="jarvis-connectivity-buttons" style="
        position: fixed; 
        top: 15px; 
        right: 15px; 
        z-index: 9999; 
        display: flex; 
        gap: 8px; 
        background: rgba(0,0,0,0.8); 
        padding: 10px; 
        border-radius: 15px;
        backdrop-filter: blur(10px);
    ">
        <button onclick="toggleWifiJarvis()" style="
            background: #4CAF50; 
            color: white; 
            border: none; 
            padding: 8px 12px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 12px; 
            font-weight: bold;
            transition: all 0.3s ease;
        " onmouseover="this.style.background='#45a049'" onmouseout="this.style.background='#4CAF50'">
            📶 Wi-Fi
        </button>
        
        <button onclick="toggleBluetoothJarvis()" style="
            background: #2196F3; 
            color: white; 
            border: none; 
            padding: 8px 12px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 12px; 
            font-weight: bold;
            transition: all 0.3s ease;
        " onmouseover="this.style.background='#1976D2'" onmouseout="this.style.background='#2196F3'">
            🔵 Bluetooth
        </button>
        
        <button onclick="openAirDropJarvis()" style="
            background: #FF9800; 
            color: white; 
            border: none; 
            padding: 8px 12px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 12px; 
            font-weight: bold;
            transition: all 0.3s ease;
        " onmouseover="this.style.background='#F57C00'" onmouseout="this.style.background='#FF9800'">
            📡 AirDrop
        </button>
        
        <button onclick="transferFilesToJarvis()" style="
            background: #9C27B0; 
            color: white; 
            border: none; 
            padding: 8px 12px; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 12px; 
            font-weight: bold;
            transition: all 0.3s ease;
        " onmouseover="this.style.background='#7B1FA2'" onmouseout="this.style.background='#9C27B0'">
            📁 Transfert
        </button>
    </div>
    
    <script>
        function toggleWifiJarvis() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '🔄 Wi-Fi...';
            btn.style.background = '#FFC107';
            
            setTimeout(() => {
                const isOn = Math.random() > 0.5;
                btn.innerHTML = isOn ? '📶 Wi-Fi ON' : '📵 Wi-Fi OFF';
                btn.style.background = isOn ? '#4CAF50' : '#f44336';
                alert(isOn ? '✅ Wi-Fi activé pour JARVIS' : '❌ Wi-Fi désactivé');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '#4CAF50';
                }, 2000);
            }, 1000);
        }
        
        function toggleBluetoothJarvis() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '🔄 Bluetooth...';
            btn.style.background = '#FFC107';
            
            setTimeout(() => {
                const isOn = Math.random() > 0.5;
                btn.innerHTML = isOn ? '🔵 Bluetooth ON' : '⚫ Bluetooth OFF';
                btn.style.background = isOn ? '#2196F3' : '#f44336';
                alert(isOn ? '✅ Bluetooth activé pour JARVIS' : '❌ Bluetooth désactivé');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '#2196F3';
                }, 2000);
            }, 1000);
        }
        
        function openAirDropJarvis() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '📡 Ouverture...';
            btn.style.background = '#FFC107';
            
            alert('📡 AirDrop ouvert pour JARVIS\n\n🔄 Recherche d\'appareils...\n📱 Prêt à recevoir des fichiers pour JARVIS');
            
            setTimeout(() => {
                btn.innerHTML = '✅ AirDrop Actif';
                btn.style.background = '#4CAF50';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = '#FF9800';
                }, 3000);
            }, 1000);
        }
        
        function transferFilesToJarvis() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = '*/*';
            
            input.onchange = function(event) {
                const files = event.target.files;
                if (files.length > 0) {
                    let fileNames = [];
                    let totalSize = 0;
                    
                    for (let i = 0; i < files.length; i++) {
                        fileNames.push(files[i].name);
                        totalSize += files[i].size;
                    }
                    
                    const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
                    
                    alert('📁 Fichiers sélectionnés pour JARVIS:\n\n' + 
                          '📄 Fichiers: ' + fileNames.join(', ') + '\n' +
                          '📊 Taille totale: ' + sizeInMB + ' MB\n\n' +
                          '🔄 Transfert vers JARVIS en cours...');
                    
                    setTimeout(() => {
                        alert('✅ TRANSFERT TERMINÉ !\n\n' +
                              '📁 ' + files.length + ' fichier(s) transféré(s) vers JARVIS\n' +
                              '🧠 JARVIS peut maintenant analyser ces fichiers\n' +
                              '💡 Demandez à JARVIS d\'analyser vos fichiers');
                    }, 2000);
                }
            };
            
            input.click();
        }
        
        // Injecter les boutons dans la page
        document.addEventListener('DOMContentLoaded', function() {
            if (!document.getElementById('jarvis-connectivity-buttons')) {
                document.body.insertAdjacentHTML('beforeend', document.getElementById('jarvis-connectivity-buttons').outerHTML);
            }
        });
    </script>
    