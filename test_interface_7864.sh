#!/bin/bash

# 🧪 TEST RAPIDE INTERFACE JARVIS PORT 7864
# Démarrage rapide pour vérifier que l'interface fonctionne

echo "🧪 TEST RAPIDE INTERFACE JARVIS PORT 7864"
echo "========================================="

# Aller dans le bon répertoire
cd "$(dirname "$0")"

# Nettoyer les anciens processus
echo "🧹 Nettoyage des processus sur port 7864..."
pkill -f "jarvis_interface_communication_principale.py" 2>/dev/null || true
pkill -f "port.*7864" 2>/dev/null || true
sleep 2

echo "🚀 Démarrage de l'interface JARVIS Dashboard..."

# Démarrer l'interface
python3 jarvis_interface_communication_principale.py &
INTERFACE_PID=$!

echo "✅ Interface démarrée (PID: $INTERFACE_PID)"
echo "⏳ Attente du démarrage (10 secondes)..."

# Attendre le démarrage
sleep 10

# Vérifier que l'interface est accessible
echo "🔍 Vérification de l'interface..."
if curl -s http://localhost:7864 > /dev/null; then
    echo "✅ Interface JARVIS accessible sur http://localhost:7864"
    echo ""
    echo "🎉 SUCCÈS ! Votre interface JARVIS Dashboard fonctionne !"
    echo "🌐 Ouvrez votre navigateur sur: http://localhost:7864"
    echo ""
    echo "Appuyez sur Ctrl+C pour arrêter l'interface"
    
    # Garder l'interface active
    wait $INTERFACE_PID
else
    echo "❌ Interface non accessible"
    echo "🔧 Vérification des logs..."
    
    # Arrêter le processus
    kill $INTERFACE_PID 2>/dev/null || true
fi

echo "🔄 Interface arrêtée"
