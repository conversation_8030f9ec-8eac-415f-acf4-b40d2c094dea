import gradio
import threading
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 JARVIS CODE ANALYZER & AUTO-REPAIR SYSTEM - JEAN-LUC PASSAVE
Système d'analyse statique et dynamique avec auto-réparation intelligente
"""

import os
import re
import ast
import json
import time
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class JarvisCodeAnalyzer:
    def __init__(self, project_root: str = "/Volumes/seagate/Louna_Electron_Latest"):
        self.project_root = Path(project_root)
        self.issues = []
        self.fixes_applied = []
        self.scan_results = {
            "python_files": [],
            "js_files": [],
            "html_content": [],
            "missing_functions": [],
            "undefined_variables": [],
            "broken_references": [],
            "incomplete_code": []
        }
        
    def analyze_all(self) -> Dict:
        """ANALYSE COMPLÈTE DU PROJET JARVIS"""
        print("🔍 DÉMARRAGE ANALYSE COMPLÈTE JARVIS")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. Analyser les fichiers Python
        self.analyze_python_files()
        
        # 2. Analyser les fichiers JavaScript
        self.analyze_javascript_files()
        
        # 3. Analyser le contenu HTML dynamique
        self.analyze_html_content()
        
        # 4. Vérifier les références croisées
        self.verify_cross_references()
        
        # 5. Détecter les fonctions incomplètes
        self.detect_incomplete_functions()
        
        # 6. Proposer des corrections automatiques
        self.suggest_auto_fixes()
        
        analysis_time = time.time() - start_time
        
        # 7. Générer le rapport final
        report = self.generate_report(analysis_time)
        
        return report
    
    def analyze_python_files(self):
        """ANALYSE DES FICHIERS PYTHON"""
        print("🐍 Analyse des fichiers Python...")
        
        python_files = list(self.project_root.glob("*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Vérifier la syntaxe Python
                try:
                    ast.parse(content)
                    syntax_ok = True
                except SyntaxError as e:
                    syntax_ok = False
                    self.issues.append({
                        "type": "SYNTAX_ERROR",
                        "file": str(py_file),
                        "line": e.lineno,
                        "message": str(e),
                        "severity": "CRITICAL"
                    })
                
                # Analyser les imports manquants
                self.check_missing_imports(py_file, content)
                
                # Détecter les fonctions non définies
                self.check_undefined_functions(py_file, content)
                
                # Vérifier les variables non définies
                self.check_undefined_variables(py_file, content)
                
                self.scan_results["python_files"].append({
                    "file": str(py_file),
                    "syntax_ok": syntax_ok,
                    "lines": len(content.split('\n')),
                    "functions": len(re.findall(r'def\s+\w+', content)),
                    "classes": len(re.findall(r'class\s+\w+', content))
                })
                
            except Exception as e:
                self.issues.append({
                    "type": "FILE_READ_ERROR",
                    "file": str(py_file),
                    "message": f"Impossible de lire le fichier: {e}",
                    "severity": "HIGH"
                })
    
    def analyze_javascript_files(self):
        """ANALYSE DES FICHIERS JAVASCRIPT"""
        print("📜 Analyse des fichiers JavaScript...")
        
        js_files = list(self.project_root.glob("*.js"))
        
        for js_file in js_files:
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Détecter les fonctions incomplètes
                incomplete_functions = re.findall(r'function\s+(\w+)\s*\([^)]*\)\s*{[^}]*$', content, re.MULTILINE)
                for func in incomplete_functions:
                    self.issues.append({
                        "type": "INCOMPLETE_FUNCTION",
                        "file": str(js_file),
                        "function": func,
                        "message": f"Fonction {func} semble incomplète (accolade fermante manquante)",
                        "severity": "HIGH"
                    })
                
                # Vérifier les références DOM
                dom_refs = re.findall(r'getElementById\([\'"]([^\'"]+)[\'"]\)', content)
                for dom_id in dom_refs:
                    self.scan_results["html_content"].append({
                        "required_id": dom_id,
                        "file": str(js_file)
                    })
                
                # Détecter les variables non déclarées
                self.check_js_undefined_variables(js_file, content)
                
                self.scan_results["js_files"].append({
                    "file": str(js_file),
                    "lines": len(content.split('\n')),
                    "functions": len(re.findall(r'function\s+\w+', content)),
                    "dom_references": len(dom_refs)
                })
                
            except Exception as e:
                self.issues.append({
                    "type": "JS_READ_ERROR",
                    "file": str(js_file),
                    "message": f"Erreur lecture JS: {e}",
                    "severity": "MEDIUM"
                })
    
    def analyze_html_content(self):
        """ANALYSE DU CONTENU HTML DYNAMIQUE"""
        print("🌐 Analyse du contenu HTML dynamique...")
        
        # Rechercher les chaînes HTML dans les fichiers Python
        python_files = list(self.project_root.glob("*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Extraire les blocs HTML
                html_blocks = re.findall(r'"""([^"]*<[^>]+>[^"]*)"""', content, re.DOTALL)
                html_blocks.extend(re.findall(r"'''([^']*<[^>]+>[^']*)'''", content, re.DOTALL))
                
                for html_block in html_blocks:
                    # Vérifier les balises non fermées
                    self.check_unclosed_html_tags(py_file, html_block)
                    
                    # Vérifier les styles CSS problématiques
                    self.check_css_issues(py_file, html_block)
                
            except Exception as e:
                print(f"⚠️ Erreur analyse HTML dans {py_file}: {e}")
    
    def check_missing_imports(self, file_path: Path, content: str):
        """VÉRIFIER LES IMPORTS MANQUANTS"""
        # Modules couramment utilisés
        common_modules = {
            'gradio': ['gr\\.', 'gradio'],
            'requests': ['requests\\.'],
            'json': ['json\\.'],
            'datetime': ['datetime\\.', 'timedelta'],
            'os': ['os\\.'],
            'sys': ['sys\\.'],
            'time': ['time\\.'],
            'threading': ['threading\\.', 'Thread'],
            'subprocess': ['subprocess\\.']
        }
        
        imports = re.findall(r'import\s+(\w+)', content)
        imports.extend(re.findall(r'from\s+(\w+)\s+import', content))
        
        for module, patterns in common_modules.items():
            if module not in imports:
                for pattern in patterns:
                    if re.search(pattern, content):
                        self.issues.append({
                            "type": "MISSING_IMPORT",
                            "file": str(file_path),
                            "module": module,
                            "message": f"Import manquant: {module}",
                            "severity": "MEDIUM",
                            "auto_fix": f"import {module}"
                        })
                        break
    
    def check_undefined_functions(self, file_path: Path, content: str):
        """DÉTECTER LES FONCTIONS NON DÉFINIES"""
        # Extraire les appels de fonction
        function_calls = re.findall(r'(\w+)\s*\(', content)
        
        # Extraire les définitions de fonction
        function_defs = re.findall(r'def\s+(\w+)', content)
        
        # Fonctions built-in Python à ignorer
        builtin_functions = {'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple', 'range', 'enumerate', 'zip', 'open', 'max', 'min', 'sum', 'any', 'all'}
        
        for func_call in set(function_calls):
            if func_call not in function_defs and func_call not in builtin_functions and not func_call.startswith('_'):
                self.scan_results["missing_functions"].append({
                    "function": func_call,
                    "file": str(file_path)
                })
    
    def check_undefined_variables(self, file_path: Path, content: str):
        """DÉTECTER LES VARIABLES NON DÉFINIES"""
        # Extraire les assignations de variables
        assignments = re.findall(r'(\w+)\s*=', content)
        
        # Extraire les utilisations de variables
        usages = re.findall(r'\b(\w+)\b', content)
        
        # Variables built-in à ignorer
        builtin_vars = {'True', 'False', 'None', '__name__', '__file__', '__doc__'}
        
        undefined_vars = []
        for var in set(usages):
            if (var not in assignments and 
                var not in builtin_vars and 
                not var.isupper() and  # Constantes
                not var.startswith('_') and
                var.isalpha()):
                undefined_vars.append(var)
        
        if undefined_vars:
            self.scan_results["undefined_variables"].extend([{
                "variable": var,
                "file": str(file_path)
            } for var in undefined_vars[:5]])  # Limiter à 5 pour éviter le spam
    
    def check_js_undefined_variables(self, file_path: Path, content: str):
        """DÉTECTER LES VARIABLES JS NON DÉCLARÉES"""
        # Variables déclarées
        declared_vars = re.findall(r'(?:var|let|const)\s+(\w+)', content)
        
        # Variables utilisées
        used_vars = re.findall(r'\b(\w+)\s*[=\.]', content)
        
        # Variables JS built-in à ignorer
        builtin_js = {'console', 'document', 'window', 'require', 'module', 'exports', 'process', 'Buffer', '__dirname', '__filename'}
        
        for var in set(used_vars):
            if var not in declared_vars and var not in builtin_js and var.isalpha():
                self.issues.append({
                    "type": "JS_UNDEFINED_VAR",
                    "file": str(file_path),
                    "variable": var,
                    "message": f"Variable JS potentiellement non déclarée: {var}",
                    "severity": "LOW"
                })
    
    def check_unclosed_html_tags(self, file_path: Path, html_content: str):
        """VÉRIFIER LES BALISES HTML NON FERMÉES"""
        # Balises auto-fermantes à ignorer
        self_closing = {'br', 'hr', 'img', 'input', 'meta', 'link'}
        
        # Extraire toutes les balises
        opening_tags = re.findall(r'<(\w+)[^>]*>', html_content)
        closing_tags = re.findall(r'</(\w+)>', html_content)
        
        for tag in opening_tags:
            if tag not in self_closing and opening_tags.count(tag) != closing_tags.count(tag):
                self.issues.append({
                    "type": "UNCLOSED_HTML_TAG",
                    "file": str(file_path),
                    "tag": tag,
                    "message": f"Balise HTML potentiellement non fermée: <{tag}>",
                    "severity": "MEDIUM"
                })
    
    def check_css_issues(self, file_path: Path, html_content: str):
        """DÉTECTER LES PROBLÈMES CSS"""
        # Détecter background: white sans color spécifié
        if re.search(r'background:\s*white', html_content) and not re.search(r'color:\s*[^;]+', html_content):
            self.issues.append({
                "type": "CSS_VISIBILITY_ISSUE",
                "file": str(file_path),
                "message": "Background blanc détecté sans couleur de texte spécifiée",
                "severity": "MEDIUM",
                "auto_fix": "Ajouter color: #333; ou color: #000;"
            })
    
    def verify_cross_references(self):
        """VÉRIFIER LES RÉFÉRENCES CROISÉES"""
        print("🔗 Vérification des références croisées...")
        
        # Vérifier que les IDs DOM requis par JS existent dans le HTML généré
        required_ids = [item["required_id"] for item in self.scan_results["html_content"]]
        
        # Chercher ces IDs dans le contenu HTML des fichiers Python
        python_files = list(self.project_root.glob("*.py"))
        found_ids = []
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for req_id in required_ids:
                    if f'id="{req_id}"' in content or f"id='{req_id}'" in content:
                        found_ids.append(req_id)
                        
            except Exception:
                continue
        
        # Signaler les IDs manquants
        missing_ids = set(required_ids) - set(found_ids)
        for missing_id in missing_ids:
            self.issues.append({
                "type": "MISSING_DOM_ID",
                "id": missing_id,
                "message": f"ID DOM requis par JS mais introuvable: #{missing_id}",
                "severity": "HIGH",
                "auto_fix": f'<div id="{missing_id}"></div>'
            })
    
    def detect_incomplete_functions(self):
        """DÉTECTER LES FONCTIONS INCOMPLÈTES"""
        print("🔍 Détection des fonctions incomplètes...")
        
        python_files = list(self.project_root.glob("*.py"))
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                in_function = False
                function_name = ""
                indent_level = 0
                
                for i, line in enumerate(lines):
                    stripped = line.strip()
                    
                    # Début de fonction
                    if stripped.startswith('def '):
                        in_function = True
                        function_name = re.search(r'def\s+(\w+)', stripped).group(1)
                        indent_level = len(line) - len(line.lstrip())
                        continue
                    
                    # Dans une fonction
                    if in_function:
                        current_indent = len(line) - len(line.lstrip())
                        
                        # Si on revient au niveau d'indentation de la fonction ou moins
                        if stripped and current_indent <= indent_level:
                            # Vérifier si la fonction était vide ou incomplète
                            if i > 0 and lines[i-1].strip() in ['', 'pass', '"""', "'''"] and not stripped.startswith('def '):
                                self.scan_results["incomplete_code"].append({
                                    "type": "EMPTY_FUNCTION",
                                    "function": function_name,
                                    "file": str(py_file),
                                    "line": i
                                })
                            in_function = False
                            
            except Exception as e:
                print(f"⚠️ Erreur analyse fonctions dans {py_file}: {e}")

    def suggest_auto_fixes(self):
        """PROPOSER DES CORRECTIONS AUTOMATIQUES"""
        print("🔧 Génération des corrections automatiques...")

        auto_fixes = []

        for issue in self.issues:
            if "auto_fix" in issue:
                auto_fixes.append({
                    "issue": issue,
                    "fix": issue["auto_fix"],
                    "confidence": self.calculate_fix_confidence(issue)
                })

        # Trier par confiance décroissante
        auto_fixes.sort(key=lambda x: x["confidence"], reverse=True)

        self.scan_results["auto_fixes"] = auto_fixes

        return auto_fixes

    def calculate_fix_confidence(self, issue: Dict) -> float:
        """CALCULER LA CONFIANCE DANS UNE CORRECTION"""
        confidence_map = {
            "MISSING_IMPORT": 0.9,
            "CSS_VISIBILITY_ISSUE": 0.8,
            "MISSING_DOM_ID": 0.7,
            "UNCLOSED_HTML_TAG": 0.6
        }

        return confidence_map.get(issue["type"], 0.5)

    def apply_auto_fixes(self, min_confidence: float = 0.8) -> List[Dict]:
        """APPLIQUER LES CORRECTIONS AUTOMATIQUES"""
        print(f"🚀 Application des corrections (confiance >= {min_confidence})...")

        applied_fixes = []

        for fix_item in self.scan_results.get("auto_fixes", []):
            if fix_item["confidence"] >= min_confidence:
                try:
                    success = self.apply_single_fix(fix_item)
                    if success:
                        applied_fixes.append(fix_item)
                        self.fixes_applied.append(fix_item)
                        print(f"✅ Correction appliquée: {fix_item['issue']['message']}")
                    else:
                        print(f"❌ Échec correction: {fix_item['issue']['message']}")

                except Exception as e:
                    print(f"❌ Erreur application correction: {e}")

        return applied_fixes

    def apply_single_fix(self, fix_item: Dict) -> bool:
        """APPLIQUER UNE CORRECTION UNIQUE"""
        issue = fix_item["issue"]
        fix_code = fix_item["fix"]

        if issue["type"] == "MISSING_IMPORT":
            return self.add_missing_import(issue["file"], fix_code)
        elif issue["type"] == "CSS_VISIBILITY_ISSUE":
            return self.fix_css_visibility(issue["file"], fix_code)
        elif issue["type"] == "MISSING_DOM_ID":
            return self.add_missing_dom_id(issue["file"], issue["id"], fix_code)

        return False

    def add_missing_import(self, file_path: str, import_statement: str) -> bool:
        """AJOUTER UN IMPORT MANQUANT"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Trouver où insérer l'import
            insert_line = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_line = i + 1
                elif line.strip() and not line.strip().startswith('#'):
                    break

            # Insérer l'import
            lines.insert(insert_line, f"{import_statement}\n")

            # Sauvegarder
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

            return True

        except Exception as e:
            print(f"❌ Erreur ajout import: {e}")
            return False

    def fix_css_visibility(self, file_path: str, color_fix: str) -> bool:
        """CORRIGER LES PROBLÈMES DE VISIBILITÉ CSS"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Remplacer background: white par background: white; color: #333;
            fixed_content = re.sub(
                r'background:\s*white;',
                f'background: white; {color_fix}',
                content
            )

            if fixed_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                return True

            return False

        except Exception as e:
            print(f"❌ Erreur correction CSS: {e}")
            return False

    def add_missing_dom_id(self, file_path: str, dom_id: str, element_html: str) -> bool:
        """AJOUTER UN ÉLÉMENT DOM MANQUANT"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Chercher un endroit approprié pour insérer l'élément
            # Par exemple, avant </div> ou </body>
            insertion_points = ['</div>', '</body>', '</html>']

            for point in insertion_points:
                if point in content:
                    fixed_content = content.replace(point, f"    {element_html}\n{point}", 1)

                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(fixed_content)
                    return True

            return False

        except Exception as e:
            print(f"❌ Erreur ajout DOM: {e}")
            return False

    def generate_report(self, analysis_time: float) -> Dict:
        """GÉNÉRER LE RAPPORT FINAL"""
        print("📊 Génération du rapport final...")

        # Compter les issues par sévérité
        severity_counts = {"CRITICAL": 0, "HIGH": 0, "MEDIUM": 0, "LOW": 0}
        for issue in self.issues:
            severity_counts[issue.get("severity", "LOW")] += 1

        # Statistiques générales
        total_python_files = len(self.scan_results["python_files"])
        total_js_files = len(self.scan_results["js_files"])
        total_issues = len(self.issues)
        total_fixes_applied = len(self.fixes_applied)

        report = {
            "timestamp": datetime.now().isoformat(),
            "analysis_time": round(analysis_time, 2),
            "summary": {
                "total_files_analyzed": total_python_files + total_js_files,
                "python_files": total_python_files,
                "js_files": total_js_files,
                "total_issues": total_issues,
                "fixes_applied": total_fixes_applied,
                "severity_breakdown": severity_counts
            },
            "issues": self.issues,
            "scan_results": self.scan_results,
            "fixes_applied": self.fixes_applied,
            "recommendations": self.generate_recommendations()
        }

        # Sauvegarder le rapport
        report_file = self.project_root / f"jarvis_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"📄 Rapport sauvegardé: {report_file}")

        return report

    def generate_recommendations(self) -> List[str]:
        """GÉNÉRER DES RECOMMANDATIONS"""
        recommendations = []

        # Analyser les patterns d'erreurs
        error_types = [issue["type"] for issue in self.issues]

        if error_types.count("MISSING_IMPORT") > 3:
            recommendations.append("🔧 Considérer l'ajout d'un système de gestion automatique des imports")

        if error_types.count("CSS_VISIBILITY_ISSUE") > 2:
            recommendations.append("🎨 Implémenter un système de thème cohérent pour éviter les problèmes de contraste")

        if error_types.count("MISSING_DOM_ID") > 1:
            recommendations.append("🌐 Créer un système de validation DOM automatique")

        if len(self.scan_results["incomplete_code"]) > 5:
            recommendations.append("📝 Compléter les fonctions vides ou ajouter des commentaires TODO")

        if not recommendations:
            recommendations.append("✅ Code en bon état général, continuer les bonnes pratiques")

        return recommendations

    def print_summary_report(self, report: Dict):
        """AFFICHER UN RÉSUMÉ DU RAPPORT"""
        print("\n" + "=" * 80)
        print("📋 RAPPORT D'ANALYSE JARVIS - RÉSUMÉ")
        print("=" * 80)

        summary = report["summary"]

        print(f"⏱️  Temps d'analyse: {report['analysis_time']}s")
        print(f"📁 Fichiers analysés: {summary['total_files_analyzed']} ({summary['python_files']} Python, {summary['js_files']} JS)")
        print(f"🔍 Issues détectées: {summary['total_issues']}")
        print(f"🔧 Corrections appliquées: {summary['fixes_applied']}")

        print("\n📊 RÉPARTITION PAR SÉVÉRITÉ:")
        for severity, count in summary["severity_breakdown"].items():
            if count > 0:
                emoji = {"CRITICAL": "🔴", "HIGH": "🟠", "MEDIUM": "🟡", "LOW": "🟢"}[severity]
                print(f"   {emoji} {severity}: {count}")

        print("\n💡 RECOMMANDATIONS:")
        for rec in report["recommendations"]:
            print(f"   {rec}")

        if summary["total_issues"] == 0:
            print("\n🎉 FÉLICITATIONS! Aucun problème détecté.")
        elif summary["fixes_applied"] > 0:
            print(f"\n✅ {summary['fixes_applied']} corrections appliquées automatiquement.")

        print("=" * 80)


def main():
    """FONCTION PRINCIPALE"""
    print("🚀 JARVIS CODE ANALYZER & AUTO-REPAIR")
    print("Développé par Jean-Luc Passave")
    print("=" * 50)

    # Initialiser l'analyseur
    analyzer = JarvisCodeAnalyzer()

    # Lancer l'analyse complète
    report = analyzer.analyze_all()

    # Appliquer les corrections automatiques
    analyzer.apply_auto_fixes(min_confidence=0.8)

    # Afficher le résumé
    analyzer.print_summary_report(report)

    return report


if __name__ == "__main__":
    main()
