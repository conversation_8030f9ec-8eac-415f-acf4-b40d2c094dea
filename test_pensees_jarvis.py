import time
#!/usr/bin/env python3
"""
🧠 TEST RAPIDE DU SYSTÈME DE PENSÉES JARVIS
<PERSON><PERSON><PERSON> - Diagnostic et correction
"""

import gradio as gr
from datetime import datetime

def test_thoughts_system():
    """Test du système de pensées"""
    
    # Historique des pensées pour test
    thoughts_history = []
    
    def update_thoughts_display(thoughts_text):
        """Test de mise à jour des pensées"""
        nonlocal thoughts_history
        
        # Ajouter à l'historique
        thoughts_history.append({
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "content": thoughts_text
        })
        
        # Garder seulement les 5 dernières pensées
        if len(thoughts_history) > 5:
            thoughts_history = thoughts_history[-5:]
        
        # Générer l'affichage
        thoughts_html = ""
        for i, thought in enumerate(reversed(thoughts_history)):
            opacity = 1.0 - (i * 0.15)
            thoughts_html += f"""
            <div style='margin: 5px 0; padding: 12px; background: white; border-radius: 8px; font-size: 0.95em; box-shadow: 0 2px 4px rgba(0,0,0,0.1); opacity: {opacity};'>
                <div style='display: flex; align-items: center; margin-bottom: 8px;'>
                    <div style='width: 8px; height: 8px; background: #9C27B0; border-radius: 50%; margin-right: 8px; animation: pulse 2s infinite;'></div>
                    <strong style='color: #9C27B0;'>🧠 Test Pensées - {thought['timestamp']}</strong>
                </div>
                <div style='color: #333; line-height: 1.4;'>{thought['content']}</div>
            </div>
            """
        
        return f"""
        <div style='background: linear-gradient(135deg, #f3e5f5, #e1bee7); padding: 15px; border-radius: 10px; border-left: 4px solid #9C27B0; max-height: 400px; overflow-y: auto;'>
            {thoughts_html}
        </div>
        <style>
        @keyframes pulse {{
            0%, 100% {{ opacity: 1; transform: scale(1); }}
            50% {{ opacity: 0.7; transform: scale(1.2); }}
        }}
        </style>
        """
    
    def test_thought_generation():
        """Génère une pensée de test"""
        test_thoughts = [
            "🧠 Test système de pensées - Neurones activés",
            "🔄 Analyse des connexions cognitives en cours...",
            "💡 Génération d'idées créatives spontanées",
            "⚡ Accélérateurs turbo thermiques opérationnels",
            "🎨 Mode créatif autonome prêt à l'activation"
        ]
        
        import random
        selected_thought = random.choice(test_thoughts)
        return update_thoughts_display(selected_thought)
    
    def test_deepseek_connection():
        """Test de connexion DeepSeek"""
        try:
            import requests
            response = requests.get("http://localhost:8000/v1/models", timeout=5)
            if response.status_code == 200:
                return "✅ DeepSeek R1 8B connecté sur localhost:8000"
            else:
                return f"❌ DeepSeek erreur: {response.status_code}"
        except:
            return "❌ DeepSeek R1 8B non accessible - Démarrez VLLM"
    
    # Interface de test
    with gr.Blocks(title="🧠 Test Pensées JARVIS") as demo:
        gr.HTML("<h1>🧠 Test du Système de Pensées JARVIS</h1>")
        
        with gr.Row():
            with gr.Column():
                test_btn = gr.Button("🧠 Générer Pensée Test", variant="primary")
                connection_btn = gr.Button("🔍 Test DeepSeek", variant="secondary")
                
            with gr.Column():
                thoughts_display = gr.HTML("""
                <div style='background: #f3e5f5; padding: 15px; border-radius: 10px; text-align: center;'>
                    <strong>🧠 Système de Pensées</strong><br>
                    <em>Cliquez sur "Générer Pensée Test" pour tester</em>
                </div>
                """)
                
                status_display = gr.HTML("""
                <div style='background: #e3f2fd; padding: 15px; border-radius: 10px; text-align: center;'>
                    <strong>🔍 Statut Connexion</strong><br>
                    <em>Cliquez sur "Test DeepSeek" pour vérifier</em>
                </div>
                """)
        
        # Connexions
        test_btn.click(
            fn=test_thought_generation,
            outputs=[thoughts_display]
        )
        
        connection_btn.click(
            fn=test_deepseek_connection,
            outputs=[status_display]
        )
    
    return demo

if __name__ == "__main__":
    demo = test_thoughts_system()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7865,
        share=False,
        show_error=True
    )
