#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 TESTEUR CORRECTIONS JARVIS - JEAN-LUC PASSAVE
Validation complète de toutes les corrections appliquées
"""

import os
import sys
import json
import time
import requests
import subprocess
from datetime import datetime
import threading

class TesteurCorrectionsJarvis:
    """TESTEUR COMPLET DES CORRECTIONS JARVIS"""
    
    def __init__(self):
        self.resultats_tests = []
        self.ports_a_tester = {
            "Dashboard Principal": 8101,
            "Communication": 8100,
            "Code Editor": 8108,  # Port corrigé
            "Thoughts": 8103,
            "Configuration": 8104,
            "WhatsApp": 8105,
            "Security": 8106,
            "Monitoring": 8107,
            "Surveillance": 8260  # Nouvelle interface
        }
        
    def log_test(self, test_name, status, details=""):
        """ENREGISTRER UN RÉSULTAT DE TEST"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "test": test_name,
            "status": status,
            "details": details
        }
        self.resultats_tests.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   📝 {details}")
    
    def tester_fichiers_corriges(self):
        """TESTER QUE LES FICHIERS CORRIGÉS EXISTENT"""
        print("🔍 TEST 1: VÉRIFICATION FICHIERS CORRIGÉS")
        print("-" * 50)
        
        fichiers_requis = [
            "jarvis_architecture_multi_fenetres.py",
            "interface_surveillance_jarvis.py",
            "correcteur_securite_electron.py",
            "correcteur_modules_manquants.py",
            "redis_fallback.py",
            "diffusers_fallback.py",
            "torch_fallback.py"
        ]
        
        for fichier in fichiers_requis:
            if os.path.exists(fichier):
                self.log_test(f"Fichier {fichier}", "PASS", "Fichier présent")
            else:
                self.log_test(f"Fichier {fichier}", "FAIL", "Fichier manquant")
    
    def tester_corrections_ports(self):
        """TESTER LES CORRECTIONS DE PORTS"""
        print("\n🔌 TEST 2: VÉRIFICATION CORRECTIONS PORTS")
        print("-" * 50)
        
        # Vérifier que le port 8102 a été changé en 8108
        try:
            with open("jarvis_architecture_multi_fenetres.py", 'r', encoding='utf-8') as f:
                contenu = f.read()
            
            if '"code_port": 8108' in contenu:
                self.log_test("Port Code corrigé", "PASS", "8102 → 8108")
            else:
                self.log_test("Port Code corrigé", "FAIL", "Port non corrigé")
            
            if 'PORT 8102 CHANGÉ EN 8108' in contenu:
                self.log_test("Commentaires ports", "PASS", "Commentaires ajoutés")
            else:
                self.log_test("Commentaires ports", "WARN", "Commentaires manquants")
                
        except Exception as e:
            self.log_test("Lecture fichier ports", "FAIL", str(e))
    
    def tester_interface_surveillance(self):
        """TESTER L'INTERFACE DE SURVEILLANCE"""
        print("\n🕐 TEST 3: INTERFACE SURVEILLANCE")
        print("-" * 50)
        
        # Vérifier que le fichier interface existe
        if os.path.exists("interface_surveillance_jarvis.py"):
            self.log_test("Interface surveillance créée", "PASS", "Fichier présent")
            
            # Vérifier le contenu
            try:
                with open("interface_surveillance_jarvis.py", 'r', encoding='utf-8') as f:
                    contenu = f.read()
                
                if "create_surveillance_interface" in contenu:
                    self.log_test("Fonction principale", "PASS", "create_surveillance_interface trouvée")
                else:
                    self.log_test("Fonction principale", "FAIL", "Fonction manquante")
                
                if "port=8260" in contenu:
                    self.log_test("Port surveillance", "PASS", "Port 8260 configuré")
                else:
                    self.log_test("Port surveillance", "FAIL", "Port non configuré")
                    
            except Exception as e:
                self.log_test("Analyse interface surveillance", "FAIL", str(e))
        else:
            self.log_test("Interface surveillance créée", "FAIL", "Fichier manquant")
    
    def tester_fallbacks_modules(self):
        """TESTER LES FALLBACKS DES MODULES"""
        print("\n🔧 TEST 4: FALLBACKS MODULES")
        print("-" * 50)
        
        # Tester Redis fallback
        try:
            if os.path.exists("redis_fallback.py"):
                sys.path.insert(0, '.')
                from redis_fallback import Redis
                redis_test = Redis()
                redis_test.set("test", "value")
                if redis_test.get("test") == "value":
                    self.log_test("Redis Fallback", "PASS", "Fonctionnel")
                else:
                    self.log_test("Redis Fallback", "FAIL", "Non fonctionnel")
            else:
                self.log_test("Redis Fallback", "FAIL", "Fichier manquant")
        except Exception as e:
            self.log_test("Redis Fallback", "FAIL", str(e))
        
        # Tester Diffusers fallback
        try:
            if os.path.exists("diffusers_fallback.py"):
                from diffusers_fallback import StableDiffusionPipeline
                pipeline = StableDiffusionPipeline.from_pretrained("test")
                self.log_test("Diffusers Fallback", "PASS", "Importable")
            else:
                self.log_test("Diffusers Fallback", "FAIL", "Fichier manquant")
        except Exception as e:
            self.log_test("Diffusers Fallback", "FAIL", str(e))
        
        # Tester Torch fallback
        try:
            if os.path.exists("torch_fallback.py"):
                from torch_fallback import torch
                tensor = torch.tensor([1, 2, 3])
                self.log_test("Torch Fallback", "PASS", "Fonctionnel")
            else:
                self.log_test("Torch Fallback", "FAIL", "Fichier manquant")
        except Exception as e:
            self.log_test("Torch Fallback", "FAIL", str(e))
    
    def tester_boutons_navigation(self):
        """TESTER LES BOUTONS DE NAVIGATION"""
        print("\n🔗 TEST 5: BOUTONS NAVIGATION")
        print("-" * 50)
        
        try:
            with open("jarvis_architecture_multi_fenetres.py", 'r', encoding='utf-8') as f:
                contenu = f.read()
            
            # Vérifier bouton surveillance dans monitoring
            if 'surveillance_btn.click' in contenu:
                self.log_test("Bouton surveillance", "PASS", "Connexion trouvée")
            else:
                self.log_test("Bouton surveillance", "FAIL", "Connexion manquante")
            
            # Vérifier port surveillance dans mapping
            if '"surveillance": 8260' in contenu:
                self.log_test("Mapping port surveillance", "PASS", "Port mappé")
            else:
                self.log_test("Mapping port surveillance", "FAIL", "Port non mappé")
                
        except Exception as e:
            self.log_test("Analyse boutons navigation", "FAIL", str(e))
    
    def tester_connectivite_ports(self):
        """TESTER LA CONNECTIVITÉ DES PORTS"""
        print("\n🌐 TEST 6: CONNECTIVITÉ PORTS")
        print("-" * 50)
        
        for nom, port in self.ports_a_tester.items():
            try:
                response = requests.get(f"http://localhost:{port}", timeout=2)
                if response.status_code == 200:
                    self.log_test(f"Port {port} ({nom})", "PASS", "Accessible")
                else:
                    self.log_test(f"Port {port} ({nom})", "WARN", f"Code {response.status_code}")
            except requests.exceptions.ConnectionError:
                self.log_test(f"Port {port} ({nom})", "WARN", "Service non démarré")
            except Exception as e:
                self.log_test(f"Port {port} ({nom})", "FAIL", str(e))
    
    def tester_securite_electron(self):
        """TESTER LES CORRECTIONS DE SÉCURITÉ ELECTRON"""
        print("\n🔒 TEST 7: SÉCURITÉ ELECTRON")
        print("-" * 50)
        
        fichiers_electron = [
            "jarvis_electron_final_complet.js",
            "jarvis_electron_multi_interfaces.js"
        ]
        
        for fichier in fichiers_electron:
            if os.path.exists(fichier):
                try:
                    with open(fichier, 'r', encoding='utf-8') as f:
                        contenu = f.read()
                    
                    # Vérifier corrections sécurité
                    if 'webSecurity: true' in contenu:
                        self.log_test(f"webSecurity {fichier}", "PASS", "Activé")
                    else:
                        self.log_test(f"webSecurity {fichier}", "WARN", "Non configuré")
                    
                    if 'Content-Security-Policy' in contenu:
                        self.log_test(f"CSP {fichier}", "PASS", "Configuré")
                    else:
                        self.log_test(f"CSP {fichier}", "WARN", "Non configuré")
                        
                except Exception as e:
                    self.log_test(f"Analyse sécurité {fichier}", "FAIL", str(e))
            else:
                self.log_test(f"Fichier {fichier}", "WARN", "Non trouvé")
    
    def generer_rapport_final(self):
        """GÉNÉRER LE RAPPORT FINAL"""
        print("\n" + "=" * 70)
        print("📊 RAPPORT FINAL DES TESTS")
        print("=" * 70)
        
        total_tests = len(self.resultats_tests)
        tests_pass = len([t for t in self.resultats_tests if t['status'] == 'PASS'])
        tests_fail = len([t for t in self.resultats_tests if t['status'] == 'FAIL'])
        tests_warn = len([t for t in self.resultats_tests if t['status'] == 'WARN'])
        
        print(f"📈 STATISTIQUES:")
        print(f"   ✅ Tests réussis: {tests_pass}/{total_tests}")
        print(f"   ❌ Tests échoués: {tests_fail}/{total_tests}")
        print(f"   ⚠️ Avertissements: {tests_warn}/{total_tests}")
        
        pourcentage_reussite = (tests_pass / total_tests) * 100 if total_tests > 0 else 0
        print(f"   📊 Taux de réussite: {pourcentage_reussite:.1f}%")
        
        # Sauvegarder le rapport
        rapport = {
            "timestamp": datetime.now().isoformat(),
            "statistiques": {
                "total": total_tests,
                "pass": tests_pass,
                "fail": tests_fail,
                "warn": tests_warn,
                "taux_reussite": pourcentage_reussite
            },
            "resultats_detailles": self.resultats_tests,
            "recommandations": self.generer_recommandations()
        }
        
        with open('rapport_tests_corrections.json', 'w', encoding='utf-8') as f:
            json.dump(rapport, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Rapport sauvegardé: rapport_tests_corrections.json")
        
        if pourcentage_reussite >= 80:
            print("🎉 CORRECTIONS VALIDÉES - JARVIS PRÊT À FONCTIONNER")
        elif pourcentage_reussite >= 60:
            print("⚠️ CORRECTIONS PARTIELLES - QUELQUES AJUSTEMENTS NÉCESSAIRES")
        else:
            print("❌ CORRECTIONS INSUFFISANTES - RÉVISION REQUISE")
    
    def generer_recommandations(self):
        """GÉNÉRER DES RECOMMANDATIONS"""
        recommandations = []
        
        tests_fail = [t for t in self.resultats_tests if t['status'] == 'FAIL']
        
        if tests_fail:
            recommandations.append("Corriger les tests échoués avant de lancer JARVIS")
        
        if any('Port' in t['test'] and t['status'] == 'WARN' for t in self.resultats_tests):
            recommandations.append("Démarrer JARVIS pour tester la connectivité des ports")
        
        if any('Sécurité' in t['test'] and t['status'] == 'WARN' for t in self.resultats_tests):
            recommandations.append("Exécuter le correcteur de sécurité Electron")
        
        if any('Fallback' in t['test'] and t['status'] == 'FAIL' for t in self.resultats_tests):
            recommandations.append("Installer les modules manquants ou corriger les fallbacks")
        
        recommandations.extend([
            "Tester l'interface de surveillance sur http://localhost:8260",
            "Vérifier que tous les boutons de navigation fonctionnent",
            "Surveiller les logs pour détecter d'éventuels problèmes"
        ])
        
        return recommandations
    
    def executer_tous_tests(self):
        """EXÉCUTER TOUS LES TESTS"""
        print("🧪 DÉMARRAGE TESTS CORRECTIONS JARVIS - JEAN-LUC PASSAVE")
        print("=" * 70)
        
        self.tester_fichiers_corriges()
        self.tester_corrections_ports()
        self.tester_interface_surveillance()
        self.tester_fallbacks_modules()
        self.tester_boutons_navigation()
        self.tester_connectivite_ports()
        self.tester_securite_electron()
        
        self.generer_rapport_final()

def main():
    """FONCTION PRINCIPALE"""
    testeur = TesteurCorrectionsJarvis()
    testeur.executer_tous_tests()

if __name__ == "__main__":
    main()
