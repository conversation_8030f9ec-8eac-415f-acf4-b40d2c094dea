import time
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 INTERFACE JARVIS CODE ANALYZER - JEAN-LUC PASSAVE
Interface Gradio pour l'analyse et auto-réparation de code
"""

import gradio as gr
import json
import os
from datetime import datetime
from jarvis_code_analyzer_auto_repair import JarvisCodeAnalyzer

def create_code_analyzer_interface():
    """CRÉER L'INTERFACE D'ANALYSE DE CODE"""
    
    with gr.Blocks(title="🔧 JARVIS Code Analyzer", theme=gr.themes.Soft()) as analyzer_interface:
        
        # En-tête
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border-radius: 15px; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 2.5em;">🔧 JARVIS CODE ANALYZER</h1>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Analyse Statique & Auto-Réparation Intelligente</p>
        </div>
        """)
        
        with gr.Tabs():
            # ANALYSE RAPIDE
            with gr.TabItem("🚀 Analyse Rapide"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.HTML("<h3>🔍 Configuration d'Analyse</h3>")
                        
                        project_path = gr.Textbox(
                            label="Chemin du Projet",
                            value="/Volumes/seagate/Louna_Electron_Latest",
                            placeholder="Chemin vers le projet à analyser..."
                        )
                        
                        analysis_options = gr.CheckboxGroup(
                            choices=[
                                "Analyser fichiers Python",
                                "Analyser fichiers JavaScript", 
                                "Vérifier contenu HTML",
                                "Détecter références croisées",
                                "Chercher fonctions incomplètes"
                            ],
                            value=[
                                "Analyser fichiers Python",
                                "Analyser fichiers JavaScript",
                                "Vérifier contenu HTML"
                            ],
                            label="Options d'Analyse"
                        )
                        
                        auto_fix_enabled = gr.Checkbox(
                            label="Appliquer corrections automatiques",
                            value=True
                        )
                        
                        confidence_threshold = gr.Slider(
                            minimum=0.1,
                            maximum=1.0,
                            value=0.8,
                            label="Seuil de confiance pour auto-réparation"
                        )
                        
                        with gr.Row():
                            analyze_btn = gr.Button("🔍 Lancer Analyse", variant="primary", size="lg")
                            clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
                    
                    with gr.Column(scale=2):
                        gr.HTML("<h3>📊 Résultats d'Analyse</h3>")
                        
                        analysis_status = gr.HTML("""
                        <div style="background: #f0f8ff; padding: 15px; border-radius: 10px;">
                            <h4>📋 Statut</h4>
                            <p>Prêt pour l'analyse...</p>
                        </div>
                        """)
                        
                        analysis_results = gr.Textbox(
                            label="Résultats Détaillés",
                            lines=15,
                            interactive=False,
                            placeholder="Les résultats d'analyse apparaîtront ici..."
                        )
                        
                        with gr.Row():
                            download_report_btn = gr.Button("📄 Télécharger Rapport", variant="secondary")
                            view_fixes_btn = gr.Button("🔧 Voir Corrections", variant="secondary")
            
            # ANALYSE DÉTAILLÉE
            with gr.TabItem("📊 Analyse Détaillée"):
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>📈 Métriques de Code</h3>")
                        
                        code_metrics = gr.HTML("""
                        <div style="background: #f9f9f9; padding: 15px; border-radius: 10px;">
                            <h4>📊 Statistiques</h4>
                            <p>Lancez une analyse pour voir les métriques...</p>
                        </div>
                        """)
                        
                        issue_breakdown = gr.HTML("""
                        <div style="background: #fff3e0; padding: 15px; border-radius: 10px;">
                            <h4>🔍 Répartition des Issues</h4>
                            <p>Aucune analyse effectuée</p>
                        </div>
                        """)
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🔧 Corrections Appliquées</h3>")
                        
                        applied_fixes = gr.HTML("""
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 10px;">
                            <h4>✅ Corrections Automatiques</h4>
                            <p>Aucune correction appliquée</p>
                        </div>
                        """)
                        
                        recommendations = gr.HTML("""
                        <div style="background: #f0f8ff; padding: 15px; border-radius: 10px;">
                            <h4>💡 Recommandations</h4>
                            <p>Lancez une analyse pour obtenir des recommandations</p>
                        </div>
                        """)
            
            # HISTORIQUE
            with gr.TabItem("📚 Historique"):
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>📚 Historique des Analyses</h3>")
                        
                        history_list = gr.HTML("""
                        <div style="background: #f9f9f9; padding: 15px; border-radius: 10px;">
                            <h4>📋 Analyses Précédentes</h4>
                            <p>Aucun historique disponible</p>
                        </div>
                        """)
                        
                        with gr.Row():
                            refresh_history_btn = gr.Button("🔄 Actualiser", variant="secondary")
                            clear_history_btn = gr.Button("🗑️ Vider Historique", variant="secondary")
        
        # FONCTIONS DE L'INTERFACE
        def run_analysis(project_path, options, auto_fix, confidence):
            """LANCER L'ANALYSE"""
            try:
                # Initialiser l'analyseur
                analyzer = JarvisCodeAnalyzer(project_path)
                
                # Statut de démarrage
                status_html = """
                <div style="background: #fff3cd; padding: 15px; border-radius: 10px;">
                    <h4>🔄 Analyse en cours...</h4>
                    <p>Scanning des fichiers...</p>
                </div>
                """
                
                # Lancer l'analyse
                report = analyzer.analyze_all()
                
                # Appliquer les corrections si demandé
                if auto_fix:
                    applied_fixes = analyzer.apply_auto_fixes(min_confidence=confidence)
                    fixes_count = len(applied_fixes)
                else:
                    fixes_count = 0
                
                # Générer le résumé
                summary = report["summary"]
                results_text = f"""
🔍 ANALYSE TERMINÉE - {datetime.now().strftime('%H:%M:%S')}

📊 STATISTIQUES:
• Fichiers analysés: {summary['total_files_analyzed']}
• Python: {summary['python_files']} fichiers
• JavaScript: {summary['js_files']} fichiers
• Issues détectées: {summary['total_issues']}
• Corrections appliquées: {fixes_count}

🚨 ISSUES PAR SÉVÉRITÉ:
• 🔴 Critiques: {summary['severity_breakdown']['CRITICAL']}
• 🟠 Hautes: {summary['severity_breakdown']['HIGH']}
• 🟡 Moyennes: {summary['severity_breakdown']['MEDIUM']}
• 🟢 Basses: {summary['severity_breakdown']['LOW']}

💡 RECOMMANDATIONS:
{chr(10).join(f"• {rec}" for rec in report['recommendations'])}

⏱️ Temps d'analyse: {report['analysis_time']}s
                """
                
                # Statut final
                if summary['total_issues'] == 0:
                    final_status = """
                    <div style="background: #d4edda; padding: 15px; border-radius: 10px;">
                        <h4>🎉 Analyse Terminée - Aucun Problème</h4>
                        <p>Votre code est en excellent état!</p>
                    </div>
                    """
                else:
                    final_status = f"""
                    <div style="background: #fff3cd; padding: 15px; border-radius: 10px;">
                        <h4>✅ Analyse Terminée</h4>
                        <p>{summary['total_issues']} issues détectées, {fixes_count} corrections appliquées</p>
                    </div>
                    """
                
                return final_status, results_text
                
            except Exception as e:
                error_status = f"""
                <div style="background: #f8d7da; padding: 15px; border-radius: 10px;">
                    <h4>❌ Erreur d'Analyse</h4>
                    <p>Erreur: {str(e)}</p>
                </div>
                """
                return error_status, f"❌ Erreur lors de l'analyse: {str(e)}"
        
        def clear_results():
            """EFFACER LES RÉSULTATS"""
            status_html = """
            <div style="background: #f0f8ff; padding: 15px; border-radius: 10px;">
                <h4>📋 Statut</h4>
                <p>Prêt pour l'analyse...</p>
            </div>
            """
            return status_html, ""
        
        def get_analysis_history():
            """OBTENIR L'HISTORIQUE DES ANALYSES"""
            try:
                project_root = "/Volumes/seagate/Louna_Electron_Latest"
                report_files = []
                
                for file in os.listdir(project_root):
                    if file.startswith("jarvis_analysis_report_") and file.endswith(".json"):
                        report_files.append(file)
                
                if not report_files:
                    return """
                    <div style="background: #f9f9f9; padding: 15px; border-radius: 10px;">
                        <h4>📋 Historique</h4>
                        <p>Aucun rapport d'analyse trouvé</p>
                    </div>
                    """
                
                # Trier par date (plus récent en premier)
                report_files.sort(reverse=True)
                
                history_html = """
                <div style="background: #f9f9f9; padding: 15px; border-radius: 10px;">
                    <h4>📋 Analyses Précédentes</h4>
                """
                
                for i, report_file in enumerate(report_files[:10]):  # Limiter à 10
                    # Extraire la date du nom de fichier
                    date_str = report_file.replace("jarvis_analysis_report_", "").replace(".json", "")
                    try:
                        date_obj = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                        formatted_date = date_obj.strftime("%d/%m/%Y %H:%M")
                    except:
                        formatted_date = date_str
                    
                    history_html += f"""
                    <div style="margin: 10px 0; padding: 8px; background: #e9ecef; border-radius: 5px;">
                        <strong>📄 Rapport {i+1}</strong><br>
                        <small>📅 {formatted_date}</small><br>
                        <small>📁 {report_file}</small>
                    </div>
                    """
                
                history_html += "</div>"
                return history_html
                
            except Exception as e:
                return f"""
                <div style="background: #f8d7da; padding: 15px; border-radius: 10px;">
                    <h4>❌ Erreur</h4>
                    <p>Impossible de charger l'historique: {str(e)}</p>
                </div>
                """
        
        # CONNEXIONS DES ÉVÉNEMENTS
        analyze_btn.click(
            fn=run_analysis,
            inputs=[project_path, analysis_options, auto_fix_enabled, confidence_threshold],
            outputs=[analysis_status, analysis_results]
        )
        
        clear_btn.click(
            fn=clear_results,
            outputs=[analysis_status, analysis_results]
        )
        
        refresh_history_btn.click(
            fn=get_analysis_history,
            outputs=[history_list]
        )
        
        # Charger l'historique au démarrage
        analyzer_interface.load(
            fn=get_analysis_history,
            outputs=[history_list]
        )
    
    return analyzer_interface

if __name__ == "__main__":
    interface = create_code_analyzer_interface()
    interface.launch(server_port=7899, share=False)
