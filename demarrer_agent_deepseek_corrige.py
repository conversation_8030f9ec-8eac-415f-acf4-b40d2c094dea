#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DÉMARRAGE AGENT DEEPSEEK CORRIGÉ - JEAN-LUC PASSAVE
Script de démarrage robuste pour l'agent DeepSeek R1 8B
"""

import subprocess
import time
import requests
import os
import sys
import signal

def test_connection():
    """Test de connexion simple à l'agent"""
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json={
                "messages": [{"role": "user", "content": "Test"}],
                "max_tokens": 5,
                "temperature": 0.1
            },
            timeout=10
        )
        return response.status_code == 200
    except:
        return False

def kill_existing_processes():
    """Arrête tous les processus sur le port 8000"""
    try:
        # Trouver les processus utilisant le port 8000
        result = subprocess.run(['lsof', '-ti:8000'], capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    os.kill(int(pid), signal.SIGTERM)
                    print(f"🔄 Processus {pid} arrêté")
                except:
                    pass
        
        # Arrêter les processus VLLM/DeepSeek
        subprocess.run(['pkill', '-f', 'vllm'], check=False)
        subprocess.run(['pkill', '-f', 'deepseek'], check=False)
        subprocess.run(['pkill', '-f', 'jan-nano'], check=False)
        
        time.sleep(3)
        print("✅ Processus existants arrêtés")
        
    except Exception as e:
        print(f"⚠️ Erreur arrêt processus: {e}")

def start_simple_server():
    """Démarre un serveur simple compatible OpenAI"""
    try:
        print("🚀 Démarrage serveur DeepSeek simple...")
        
        # Script Python simple pour serveur de test
        server_script = '''
import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

class OpenAIHandler(http.server.BaseHTTPRequestHandler):
    def do_POST(self):
        if self.path == '/v1/chat/completions':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                request_data = json.loads(post_data.decode('utf-8'))
                user_message = request_data.get('messages', [{}])[-1].get('content', 'Test')
                
                response = {
                    "id": "chatcmpl-test",
                    "object": "chat.completion",
                    "created": 1234567890,
                    "model": "deepseek-r1",
                    "choices": [{
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": f"<think>L'utilisateur me demande: {user_message}. Je dois répondre de manière utile en français.</think>\\n\\nBonjour Jean-Luc ! Je suis JARVIS, votre assistant IA. Je comprends votre message: '{user_message}'. Comment puis-je vous aider aujourd'hui ?"
                        },
                        "finish_reason": "stop"
                    }],
                    "usage": {
                        "prompt_tokens": 10,
                        "completion_tokens": 20,
                        "total_tokens": 30
                    }
                }
                
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode())
                
            except Exception as e:
                self.send_response(500)
                self.end_headers()
                self.wfile.write(f"Error: {e}".encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(b'{"status": "ok"}')
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

if __name__ == "__main__":
    PORT = 8000
    with socketserver.TCPServer(("", PORT), OpenAIHandler) as httpd:
        print(f"✅ Serveur DeepSeek démarré sur port {PORT}")
        httpd.serve_forever()
'''
        
        # Écrire le script temporaire
        with open('temp_deepseek_server.py', 'w') as f:
            f.write(server_script)
        
        # Démarrer le serveur
        process = subprocess.Popen([
            sys.executable, 'temp_deepseek_server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print(f"✅ Serveur démarré avec PID: {process.pid}")
        
        # Attendre le démarrage
        for i in range(10):
            time.sleep(2)
            if test_connection():
                print("✅ Serveur DeepSeek opérationnel !")
                return True
            print(f"⏳ Test {i+1}/10...")
        
        return False
        
    except Exception as e:
        print(f"❌ Erreur démarrage serveur: {e}")
        return False

def start_vllm_server():
    """Démarre le serveur VLLM avec le modèle local"""
    try:
        print("🚀 Tentative démarrage VLLM avec modèle local...")
        
        model_path = "/Volumes/seagate/Louna_Electron_Latest/Jan-nano-gguf/jan-nano-4b-Q4_K_M.gguf"
        
        if not os.path.exists(model_path):
            print(f"❌ Modèle non trouvé: {model_path}")
            return False
        
        cmd = [
            sys.executable, "-m", "vllm.entrypoints.openai.api_server",
            "--model", model_path,
            "--host", "localhost",
            "--port", "8000",
            "--max-model-len", "2048",
            "--gpu-memory-utilization", "0.7"
        ]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"✅ VLLM démarré avec PID: {process.pid}")
        
        # Attendre le démarrage (plus long pour VLLM)
        for i in range(30):
            time.sleep(5)
            if test_connection():
                print("✅ VLLM opérationnel !")
                return True
            print(f"⏳ VLLM test {i+1}/30...")
        
        print("❌ Timeout VLLM")
        process.terminate()
        return False
        
    except Exception as e:
        print(f"❌ Erreur VLLM: {e}")
        return False

def main():
    """Fonction principale de démarrage"""
    print("🤖 DÉMARRAGE AGENT DEEPSEEK CORRIGÉ")
    print("👤 Jean-Luc Passave")
    print("=" * 50)
    
    # 1. Arrêter les processus existants
    print("🛑 Arrêt des processus existants...")
    kill_existing_processes()
    
    # 2. Test de connexion initial
    if test_connection():
        print("✅ Agent déjà opérationnel")
        return True
    
    # 3. Tentative VLLM d'abord
    print("🚀 Tentative 1: VLLM avec modèle local")
    if start_vllm_server():
        print("🎉 SUCCÈS: VLLM opérationnel")
        return True
    
    # 4. Fallback sur serveur simple
    print("🚀 Tentative 2: Serveur simple de fallback")
    if start_simple_server():
        print("🎉 SUCCÈS: Serveur simple opérationnel")
        return True
    
    print("❌ ÉCHEC: Impossible de démarrer l'agent")
    return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Agent DeepSeek opérationnel sur http://localhost:8000")
        print("🔗 Interface JARVIS: http://localhost:7866")
        print("⚡ Système prêt pour Jean-Luc !")
    else:
        print("\n❌ Échec démarrage agent")
        print("🔧 Vérifiez les logs ci-dessus")
        sys.exit(1)
