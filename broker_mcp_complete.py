#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 MCP BROKER COMPLET POUR JARVIS - SELON CHATGPT
Système nerveux central sécurisé et extensible
Jean-<PERSON>ave - 22 Juin 2025
"""

import asyncio
import uuid
from typing import Dict, Any
from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import time
from datetime import datetime

# 🧠 IMPORT FALLBACK MÉMOIRE THERMIQUE SELON CHATGPT
try:
    from memory_fallback_manager import ThermalMemoryFallback, MCPMemoryFallbackAgent
    MEMORY_FALLBACK_AVAILABLE = True
    print("✅ Fallback mémoire thermique chargé selon ChatGPT")
except ImportError as e:
    MEMORY_FALLBACK_AVAILABLE = False
    print(f"⚠️ Fallback mémoire thermique non disponible: {e}")

app = FastAPI(title="JARVIS MCP Broker", version="1.0.0")

# 🔒 SÉCURITÉ - Autoriser Electron local + réseau local
origins = [
    "http://localhost",
    "http://127.0.0.1", 
    "http://localhost:3000",
    "http://localhost:7866",  # JARVIS principal
    "http://localhost:7867",  # Dashboard
    "http://localhost:7868",  # Éditeur
    "http://localhost:7869",  # Pensées
    "http://localhost:7870",  # Configuration
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 🧠 ÉTAT GLOBAL DU BROKER
connected_clients: Dict[str, WebSocket] = {}
message_history: list = []
agent_stats: Dict[str, Dict] = {}

# 🧠 INITIALISATION FALLBACK MÉMOIRE THERMIQUE
if MEMORY_FALLBACK_AVAILABLE:
    memory_fallback_agent = MCPMemoryFallbackAgent("thermal_memory_persistent.json")
    print("🧠 Agent fallback mémoire thermique initialisé")
else:
    memory_fallback_agent = None

@app.websocket("/ws/{agent_id}")
async def websocket_endpoint(websocket: WebSocket, agent_id: str):
    """Point d'entrée WebSocket pour agents JARVIS"""
    await websocket.accept()
    connected_clients[agent_id] = websocket
    
    # 📊 Initialiser stats agent
    agent_stats[agent_id] = {
        "connected_at": datetime.now().isoformat(),
        "messages_sent": 0,
        "messages_received": 0,
        "last_activity": datetime.now().isoformat()
    }
    
    print(f"✅ [{agent_id}] connecté au MCP Broker JARVIS")
    
    # 📢 Notifier les autres agents de la connexion
    await broadcast_system_message(f"Agent {agent_id} connecté", exclude=agent_id)

    try:
        while True:
            message = await websocket.receive_json()
            await route_message(agent_id, message)
    except WebSocketDisconnect:
        print(f"❌ [{agent_id}] déconnecté du MCP Broker")
    except Exception as e:
        print(f"⚠️ Erreur [{agent_id}]: {e}")
    finally:
        # 🧹 Nettoyage
        connected_clients.pop(agent_id, None)
        agent_stats.pop(agent_id, None)
        await broadcast_system_message(f"Agent {agent_id} déconnecté", exclude=agent_id)

async def route_message(sender_id: str, message: Dict[str, Any]):
    """Routage intelligent des messages MCP"""
    target_id = message.get("to")
    payload = message.get("payload", {})
    msg_id = str(uuid.uuid4())
    timestamp = datetime.now().isoformat()
    
    # 📊 Mise à jour stats
    if sender_id in agent_stats:
        agent_stats[sender_id]["messages_sent"] += 1
        agent_stats[sender_id]["last_activity"] = timestamp
    
    # 📝 Historique des messages
    message_record = {
        "id": msg_id,
        "from": sender_id,
        "to": target_id,
        "payload": payload,
        "timestamp": timestamp,
        "status": "pending"
    }
    message_history.append(message_record)
    
    # 🎯 Routage selon le destinataire
    if target_id == "broadcast":
        # 📢 Diffusion à tous les agents
        await broadcast_message(sender_id, payload, exclude=sender_id)
        message_record["status"] = "broadcasted"
        
    elif target_id in connected_clients:
        # 📤 Envoi direct à l'agent cible
        try:
            await connected_clients[target_id].send_json({
                "id": msg_id,
                "from": sender_id,
                "payload": payload,
                "timestamp": timestamp
            })
            
            # 📊 Mise à jour stats destinataire
            if target_id in agent_stats:
                agent_stats[target_id]["messages_received"] += 1
                agent_stats[target_id]["last_activity"] = timestamp
            
            message_record["status"] = "delivered"
            print(f"📤 MCP: [{sender_id}] → [{target_id}] : {payload.get('type', 'unknown')}")
            
        except Exception as e:
            message_record["status"] = "failed"
            print(f"❌ Erreur envoi [{sender_id}] → [{target_id}]: {e}")
            
    else:
        # 🔄 FALLBACK - Agent non connecté
        message_record["status"] = "fallback_triggered"
        await handle_fallback(sender_id, target_id, payload, msg_id)

async def broadcast_message(sender_id: str, payload: Dict[str, Any], exclude: str = None):
    """Diffusion d'un message à tous les agents connectés"""
    msg_id = str(uuid.uuid4())
    timestamp = datetime.now().isoformat()
    
    for agent_id, websocket in connected_clients.items():
        if agent_id != exclude:
            try:
                await websocket.send_json({
                    "id": msg_id,
                    "from": sender_id,
                    "payload": payload,
                    "timestamp": timestamp,
                    "broadcast": True
                })
            except Exception as e:
                print(f"❌ Erreur broadcast vers [{agent_id}]: {e}")

async def broadcast_system_message(content: str, exclude: str = None):
    """Diffusion d'un message système"""
    await broadcast_message("mcp_broker", {
        "type": "system_notification",
        "content": content
    }, exclude=exclude)

async def handle_fallback(sender_id: str, target_id: str, payload: Dict[str, Any], msg_id: str):
    """Gestion fallback selon recommandations ChatGPT"""
    print(f"🔄 FALLBACK: [{sender_id}] → [{target_id}] non connecté")
    
    # 🧠 Fallback spécialisés selon le type d'agent
    fallback_response = None
    
    if target_id == "memory_manager":
        fallback_response = await fallback_memory_manager(payload)
    elif target_id == "deepseek_agent":
        fallback_response = await fallback_deepseek_agent(payload)
    elif target_id == "creative_agent":
        fallback_response = await fallback_creative_agent(payload)
    else:
        fallback_response = {
            "type": "fallback_response",
            "content": f"Agent {target_id} non disponible. Message mis en queue.",
            "original_message_id": msg_id
        }
    
    # 📤 Renvoyer la réponse fallback à l'expéditeur
    if sender_id in connected_clients and fallback_response:
        try:
            await connected_clients[sender_id].send_json({
                "id": str(uuid.uuid4()),
                "from": "fallback_manager",
                "payload": fallback_response,
                "timestamp": datetime.now().isoformat(),
                "fallback": True
            })
        except Exception as e:
            print(f"❌ Erreur envoi fallback: {e}")

async def fallback_memory_manager(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Fallback pour gestionnaire mémoire avec vraie mémoire thermique"""
    if memory_fallback_agent:
        try:
            # Utiliser le vrai fallback mémoire thermique
            content = payload.get("content", "réflexion générale")
            message_type = payload.get("type", "thought_request")

            # Créer un message pour le fallback
            fallback_message = {
                "payload": {
                    "content": content,
                    "type": message_type
                }
            }

            # Générer la réponse avec la vraie mémoire thermique
            fallback_response = await memory_fallback_agent.handle_fallback_request(fallback_message)

            return {
                "type": "memory_response",
                "content": fallback_response,
                "data": {"status": "thermal_memory_active", "source": "real_memory"}
            }
        except Exception as e:
            print(f"❌ Erreur fallback mémoire thermique: {e}")

    # Fallback de base si erreur
    return {
        "type": "memory_response",
        "content": "🧠 Mémoire thermique en mode fallback - Données locales utilisées",
        "data": {"status": "fallback_active"}
    }

async def fallback_deepseek_agent(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Fallback pour agent DeepSeek"""
    return {
        "type": "ai_response",
        "content": "🤖 Agent IA en mode fallback - Utilisation mémoire thermique locale",
        "fallback_reason": "deepseek_unavailable"
    }

async def fallback_creative_agent(payload: Dict[str, Any]) -> Dict[str, Any]:
    """Fallback pour agent créatif"""
    return {
        "type": "creative_response",
        "content": "🎨 Mode créatif fallback - Génération basée sur patterns locaux",
        "creativity_level": "fallback"
    }

# 📊 ENDPOINTS DE MONITORING
@app.get("/status")
async def get_broker_status():
    """Status du broker MCP"""
    return {
        "status": "active",
        "connected_agents": list(connected_clients.keys()),
        "total_messages": len(message_history),
        "agent_stats": agent_stats,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/agents")
async def get_connected_agents():
    """Liste des agents connectés"""
    return {
        "agents": list(connected_clients.keys()),
        "count": len(connected_clients),
        "stats": agent_stats
    }

@app.get("/messages")
async def get_message_history(limit: int = 50):
    """Historique des messages"""
    return {
        "messages": message_history[-limit:],
        "total": len(message_history)
    }

# 🚀 DÉMARRAGE DU BROKER
async def start_mcp_broker():
    """Démarre le MCP Broker pour JARVIS"""
    print("🚀 Démarrage MCP Broker JARVIS selon recommandations ChatGPT")
    print("📡 WebSocket: ws://localhost:8766/ws/{agent_id}")
    print("📊 Status: http://localhost:8766/status")
    print("🔗 Agents: http://localhost:8766/agents")

    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=8766,
        log_level="info"
    )
    server = uvicorn.Server(config)
    await server.serve()

if __name__ == "__main__":
    print("🤖 JARVIS MCP BROKER - Système nerveux central")
    print("=" * 60)
    asyncio.run(start_mcp_broker())
