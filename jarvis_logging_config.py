import time
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CONFIGURATION LOGGING JARVIS - JEAN-LUC PASSAVE
Système de logging intelligent pour réduire les logs excessifs
"""

import os
import json
from datetime import datetime

# ============================================================================
# CONFIGURATION LOGGING JARVIS
# ============================================================================

class JarvisLoggingConfig:
    """Configuration centralisée du logging JARVIS"""
    
    def __init__(self):
        self.config_file = "jarvis_logging_settings.json"
        self.load_config()
    
    def load_config(self):
        """Charger la configuration depuis le fichier"""
        default_config = {
            "verbose_logging": False,
            "critical_only": True,
            "show_startup": True,
            "show_memory": False,
            "show_vllm": False,
            "show_server_launch": False,
            "show_thoughts": False,
            "show_communication": False,
            "show_debug": False,
            "log_to_file": True,
            "log_file": "jarvis_logs.txt",
            "max_log_size_mb": 10
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Fusionner avec les valeurs par défaut
                    default_config.update(loaded_config)
            
            self.config = default_config
            self.save_config()  # Sauvegarder pour créer le fichier si nécessaire
            
        except Exception as e:
            print(f"⚠️ Erreur chargement config logging: {e}")
            self.config = default_config
    
    def save_config(self):
        """Sauvegarder la configuration"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde config logging: {e}")
    
    def get(self, key, default=None):
        """Obtenir une valeur de configuration"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Définir une valeur de configuration"""
        self.config[key] = value
        self.save_config()
    
    def enable_verbose(self):
        """Activer les logs verbeux"""
        self.set("verbose_logging", True)
        self.set("critical_only", False)
        print("✅ Logs verbeux activés")
    
    def enable_quiet(self):
        """Activer le mode silencieux"""
        self.set("verbose_logging", False)
        self.set("critical_only", True)
        print("✅ Mode silencieux activé")
    
    def toggle_category(self, category):
        """Basculer une catégorie de logs"""
        current = self.get(f"show_{category}", False)
        self.set(f"show_{category}", not current)
        status = "activé" if not current else "désactivé"
        print(f"✅ Logs {category} {status}")

# Instance globale
logging_config = JarvisLoggingConfig()

# ============================================================================
# FONCTIONS DE LOGGING INTELLIGENTES
# ============================================================================

def log_message(message, level="INFO", category="general"):
    """
    Système de logging intelligent
    
    Args:
        message: Message à logger
        level: Niveau (CRITICAL, ERROR, INFO, DEBUG)
        category: Catégorie (startup, memory, vllm, server, thoughts, etc.)
    """
    
    # Vérifier si on doit afficher ce message
    should_log = False
    
    if level == "CRITICAL":
        should_log = True
    elif level == "ERROR" and not logging_config.get("critical_only", True):
        should_log = True
    elif level == "INFO":
        if logging_config.get("verbose_logging", False):
            should_log = True
        elif category == "startup" and logging_config.get("show_startup", True):
            should_log = True
        elif logging_config.get(f"show_{category}", False):
            should_log = True
    elif level == "DEBUG" and logging_config.get("show_debug", False):
        should_log = True
    
    # Afficher le message si nécessaire
    if should_log:
        print(message)
    
    # Logger dans un fichier si activé
    if logging_config.get("log_to_file", True):
        log_to_file(message, level, category)

def log_to_file(message, level, category):
    """Logger dans un fichier"""
    try:
        log_file = logging_config.get("log_file", "jarvis_logs.txt")
        max_size = logging_config.get("max_log_size_mb", 10) * 1024 * 1024
        
        # Vérifier la taille du fichier
        if os.path.exists(log_file) and os.path.getsize(log_file) > max_size:
            # Archiver l'ancien fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            archive_name = f"jarvis_logs_archive_{timestamp}.txt"
            os.rename(log_file, archive_name)
        
        # Écrire le message
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] [{category}] {message}\n"
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
            
    except Exception as e:
        # Ne pas afficher d'erreur pour éviter les boucles
        pass

# ============================================================================
# FONCTIONS DE CONTRÔLE RAPIDE
# ============================================================================

def enable_verbose_logs():
    """Activer tous les logs pour debug"""
    logging_config.enable_verbose()

def enable_quiet_mode():
    """Mode silencieux - seulement les messages critiques"""
    logging_config.enable_quiet()

def toggle_startup_logs():
    """Basculer les logs de démarrage"""
    logging_config.toggle_category("startup")

def toggle_memory_logs():
    """Basculer les logs mémoire"""
    logging_config.toggle_category("memory")

def toggle_vllm_logs():
    """Basculer les logs VLLM"""
    logging_config.toggle_category("vllm")

def toggle_server_logs():
    """Basculer les logs serveur"""
    logging_config.toggle_category("server_launch")

def show_logging_status():
    """Afficher le statut actuel du logging"""
    config = logging_config.config
    
    print("\n📊 STATUT LOGGING JARVIS:")
    print(f"   🔊 Verbose: {'✅' if config.get('verbose_logging') else '❌'}")
    print(f"   🔇 Silencieux: {'✅' if config.get('critical_only') else '❌'}")
    print(f"   🚀 Démarrage: {'✅' if config.get('show_startup') else '❌'}")
    print(f"   💾 Mémoire: {'✅' if config.get('show_memory') else '❌'}")
    print(f"   🤖 VLLM: {'✅' if config.get('show_vllm') else '❌'}")
    print(f"   🌐 Serveurs: {'✅' if config.get('show_server_launch') else '❌'}")
    print(f"   🧠 Pensées: {'✅' if config.get('show_thoughts') else '❌'}")
    print(f"   💬 Communication: {'✅' if config.get('show_communication') else '❌'}")
    print(f"   🐛 Debug: {'✅' if config.get('show_debug') else '❌'}")
    print(f"   📝 Fichier: {'✅' if config.get('log_to_file') else '❌'}")
    print()

def create_logging_interface():
    """Créer une interface Gradio pour contrôler les logs"""
    try:
        import gradio as gr
        
        with gr.Blocks(title="🔧 Configuration Logging JARVIS") as interface:
            gr.HTML("<h2>🔧 Configuration Logging JARVIS</h2>")
            
            with gr.Row():
                verbose_btn = gr.Button("🔊 Mode Verbose")
                quiet_btn = gr.Button("🔇 Mode Silencieux")
                status_btn = gr.Button("📊 Statut")
            
            with gr.Row():
                startup_btn = gr.Button("🚀 Toggle Démarrage")
                memory_btn = gr.Button("💾 Toggle Mémoire")
                vllm_btn = gr.Button("🤖 Toggle VLLM")
                server_btn = gr.Button("🌐 Toggle Serveurs")
            
            status_display = gr.HTML()
            
            # Connexions
            verbose_btn.click(fn=enable_verbose_logs)
            quiet_btn.click(fn=enable_quiet_mode)
            startup_btn.click(fn=toggle_startup_logs)
            memory_btn.click(fn=toggle_memory_logs)
            vllm_btn.click(fn=toggle_vllm_logs)
            server_btn.click(fn=toggle_server_logs)
            
            def get_status():
                show_logging_status()
                return "✅ Statut affiché dans la console"
            
            status_btn.click(fn=get_status, outputs=[status_display])
        
        return interface
        
    except ImportError:
        print("⚠️ Gradio non disponible pour l'interface logging")
        return None

if __name__ == "__main__":
    # Test du système de logging
    print("🧪 Test du système de logging JARVIS")
    
    show_logging_status()
    
    log_message("Test message critique", "CRITICAL")
    log_message("Test message erreur", "ERROR")
    log_message("Test message info", "INFO")
    log_message("Test message debug", "DEBUG")
    log_message("Test message démarrage", "INFO", "startup")
    log_message("Test message mémoire", "INFO", "memory")
    
    print("\n✅ Test terminé")
