import time
#!/usr/bin/env python3
"""
INTERFACE CRÉATIVITÉ JARVIS - VERSION SIMPLE
RÊVES = CRÉATIVITÉ = MÊME CHOSE
Pour Jean-Luc Passave
"""

import gradio as gr
import json
import os
from datetime import datetime

def load_all_creative_content():
    """Charge TOUT le contenu créatif de JARVIS"""
    all_creative = []
    
    try:
        # 1. Rêves créatifs
        if os.path.exists('jarvis_reves_creatifs.json'):
            with open('jarvis_reves_creatifs.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            for reve in data.get('reves_creatifs', []):
                all_creative.append({
                    "timestamp": reve.get('timestamp', ''),
                    "type": "🌙 Rêve Créatif",
                    "sujet": reve.get('sujet', ''),
                    "contenu": reve.get('reve', '')
                })
        
        # 2. Rêves spontanés
        if os.path.exists('jarvis_reves_spontanes.json'):
            with open('jarvis_reves_spontanes.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            for reve in data.get('reves_spontanes', []):
                all_creative.append({
                    "timestamp": reve.get('timestamp', ''),
                    "type": "✨ Rêve Spontané",
                    "sujet": reve.get('theme', ''),
                    "contenu": reve.get('reve', '')
                })
        
        # 3. Pensées créatives
        if os.path.exists('jarvis_pensees_eveil.json'):
            with open('jarvis_pensees_eveil.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            for pensee in data.get('pensees_eveil', []):
                contenu = pensee.get('pensee', '').lower()
                if any(word in contenu for word in ['créer', 'innover', 'imaginer', 'concevoir', 'créatif']):
                    all_creative.append({
                        "timestamp": pensee.get('timestamp', ''),
                        "type": "💡 Pensée Créative",
                        "sujet": pensee.get('sujet', ''),
                        "contenu": pensee.get('pensee', '')
                    })
        
        # 4. Auto-questions créatives
        if os.path.exists('jarvis_auto_questions.json'):
            with open('jarvis_auto_questions.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            for qa in data.get('auto_questions', []):
                question = qa.get('question', '').lower()
                if any(word in question for word in ['créatif', 'créativité', 'innover', 'idée']):
                    all_creative.append({
                        "timestamp": qa.get('timestamp', ''),
                        "type": "🤔 Question Créative",
                        "sujet": qa.get('question', ''),
                        "contenu": qa.get('reponse', '')
                    })

        # 5. RÊVES PRODUCTIFS (DREAM ENGINE)
        if os.path.exists('jarvis_reves_productifs.json'):
            with open('jarvis_reves_productifs.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            for reve in data.get('reves_productifs', []):
                all_creative.append({
                    "timestamp": reve.get('timestamp', ''),
                    "type": "🌙 Rêve Productif",
                    "sujet": f"{reve.get('type', '').replace('_', ' ').title()} - {', '.join(reve.get('themes_combines', [])[:2])}",
                    "contenu": reve.get('reve_productif', '')
                })

        # 6. INVENTIONS DE RÊVES
        if os.path.exists('jarvis_inventions_reves.json'):
            with open('jarvis_inventions_reves.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            for invention in data.get('inventions', []):
                all_creative.append({
                    "timestamp": invention.get('timestamp', ''),
                    "type": "💡 Invention Rêvée",
                    "sujet": f"Invention: {', '.join(invention.get('themes_combines', [])[:2])}",
                    "contenu": invention.get('reve_productif', '')
                })
        
        # Trier par timestamp
        all_creative.sort(key=lambda x: x['timestamp'], reverse=True)
        return all_creative
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def format_creative_for_display():
    """Formate le contenu créatif pour Gradio"""
    creative_content = load_all_creative_content()
    
    if not creative_content:
        return [{"role": "assistant", "content": "🎨 Aucun contenu créatif - JARVIS dort..."}]
    
    messages = []
    
    for item in creative_content:
        # En-tête
        timestamp = item['timestamp']
        time_str = timestamp[11:19] if len(timestamp) > 19 else timestamp
        
        header = f"{item['type']} | {time_str}"
        messages.append({"role": "user", "content": header})
        
        # Sujet
        if item['sujet']:
            messages.append({"role": "user", "content": f"🎯 {item['sujet']}"})
        
        # Contenu
        contenu = item['contenu']
        if contenu:
            if len(contenu) > 800:
                contenu = contenu[:800] + "..."
            messages.append({"role": "assistant", "content": f"💭 {contenu}"})
        
        # Séparateur
        messages.append({"role": "assistant", "content": "─" * 40})
    
    return messages

def get_creative_stats():
    """Statistiques créativité"""
    creative_content = load_all_creative_content()
    
    if not creative_content:
        return "📊 Aucun contenu créatif"
    
    # Compter par type
    stats = {}
    for item in creative_content:
        item_type = item['type']
        stats[item_type] = stats.get(item_type, 0) + 1
    
    # Dernier contenu
    last_item = creative_content[0] if creative_content else None
    last_time = last_item['timestamp'][11:19] if last_item and last_item['timestamp'] else "Jamais"
    
    stats_text = f"""
📊 **CRÉATIVITÉ JARVIS**

🎨 **Total**: {len(creative_content)}

📈 **Par type**:
{chr(10).join([f"   • {t}: {c}" for t, c in stats.items()])}

🕐 **Dernier**: {last_time}
    """
    
    return stats_text

# Interface Gradio
def create_creativity_interface():
    """Interface créativité unifiée"""
    
    with gr.Blocks(
        title="🎨 JARVIS - Créativité & Rêves",
        theme=gr.themes.Soft(),
        css="""
        .creativity-chatbot {
            font-size: 16px !important;
            line-height: 1.6 !important;
        }
        .creativity-chatbot .message {
            padding: 15px !important;
            margin: 10px 0 !important;
            border-radius: 10px !important;
        }
        .creativity-chatbot .user {
            background: linear-gradient(135deg, #FFF3E0, #FFE0B2) !important;
            border-left: 4px solid #FF9800 !important;
        }
        .creativity-chatbot .assistant {
            background: linear-gradient(135deg, #F3E5F5, #E1BEE7) !important;
            border-left: 4px solid #9C27B0 !important;
        }
        """
    ) as creativity_interface:
        
        gr.HTML("<h1>🎨 JARVIS - Interface Créativité & Rêves</h1>")
        gr.HTML("<p><strong>RÊVES = CRÉATIVITÉ = MÊME CHOSE</strong> - Tout ce que JARVIS conçoit et crée</p>")
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>📊 Statistiques</h3>")
                stats_display = gr.Markdown(
                    value=get_creative_stats(),
                    label="Stats Créativité"
                )
                
                gr.HTML("<h3>🎛️ Contrôles</h3>")
                refresh_btn = gr.Button("🔄 Actualiser", variant="primary")
                export_btn = gr.Button("📥 Exporter", variant="secondary")
                
                status_display = gr.Textbox(
                    label="Statut",
                    value="✅ Interface créativité prête",
                    interactive=False
                )
            
            with gr.Column(scale=3):
                gr.HTML("<h3>🎨 Flux Créativité JARVIS</h3>")
                creativity_display = gr.Chatbot(
                    value=format_creative_for_display(),
                    height=600,
                    label="🎨 Toute la Créativité de JARVIS - Rêves, Idées, Innovations",
                    type="messages",
                    elem_classes=["creativity-chatbot"]
                )
        
        # Fonctions
        def refresh_creativity():
            return format_creative_for_display(), get_creative_stats(), "🔄 Créativité actualisée"
        
        def export_creativity():
            try:
                creative_content = load_all_creative_content()
                if not creative_content:
                    return format_creative_for_display(), get_creative_stats(), "❌ Rien à exporter"
                
                filename = f"jarvis_creativite_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump({"creative_content": creative_content}, f, ensure_ascii=False, indent=2)
                
                return format_creative_for_display(), get_creative_stats(), f"✅ Exporté: {filename}"
            except Exception as e:
                return format_creative_for_display(), get_creative_stats(), f"❌ Erreur: {e}"
        
        # Connecter boutons
        refresh_btn.click(
            fn=refresh_creativity,
            outputs=[creativity_display, stats_display, status_display]
        )
        
        export_btn.click(
            fn=export_creativity,
            outputs=[creativity_display, stats_display, status_display]
        )
        
        # Auto-refresh au chargement
        creativity_interface.load(
            fn=refresh_creativity,
            outputs=[creativity_display, stats_display, status_display]
        )
        
        # Auto-refresh JavaScript
        gr.HTML("""
        <script>
        function autoRefreshCreativity() {
            setInterval(function() {
                try {
                    const refreshBtn = document.querySelector('button:contains("🔄")');
                    if (refreshBtn) {
                        refreshBtn.click();
                        console.log('🎨 Créativité JARVIS mise à jour');
                    }
                } catch (e) {
                    console.log('Erreur auto-refresh créativité:', e);
                }
            }, 30000); // 30 secondes
        }
        
        setTimeout(autoRefreshCreativity, 3000);
        </script>
        """, visible=False)
    
    return creativity_interface

if __name__ == "__main__":
    print("🎨 INTERFACE CRÉATIVITÉ JARVIS")
    print("=" * 40)
    print("RÊVES = CRÉATIVITÉ = MÊME CHOSE")
    
    interface = create_creativity_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7892,  # Port créativité
        share=False,
        show_error=True
    )
