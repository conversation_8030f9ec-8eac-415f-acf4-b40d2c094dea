#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST ULTRA COMPLET JARVIS - JEAN-LUC PASSAVE
Test TRÈS TRÈS TRÈS POUSSÉ de TOUS les composants
"""

import requests
import time
import json
from datetime import datetime

def print_section(title):
    """Affiche une section de test"""
    print("\n" + "="*80)
    print(f"🔍 {title}")
    print("="*80)

def test_endpoint(url, name, method="GET", payload=None, timeout=15):
    """Test un endpoint avec détails complets"""
    try:
        start_time = time.time()
        
        if method == "POST" and payload:
            response = requests.post(url, json=payload, timeout=timeout)
        else:
            response = requests.get(url, timeout=timeout)
            
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ {name}")
            print(f"   📊 Status: {response.status_code}")
            print(f"   ⏱️ Temps: {response_time:.3f}s")
            print(f"   📏 Taille: {len(response.text)} bytes")
            
            if payload and "chat/completions" in url:
                try:
                    data = response.json()
                    content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                    print(f"   💬 Réponse: {content[:80]}...")
                except:
                    pass
            
            return True, response_time
        else:
            print(f"❌ {name}")
            print(f"   📊 Status: {response.status_code}")
            print(f"   ⏱️ Temps: {response_time:.3f}s")
            return False, response_time
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {name}")
        print(f"   🔌 CONNEXION REFUSÉE")
        return False, 0
    except requests.exceptions.Timeout:
        print(f"⏱️ {name}")
        print(f"   ⏰ TIMEOUT ({timeout}s)")
        return False, timeout
    except Exception as e:
        print(f"❌ {name}")
        print(f"   💥 ERREUR: {str(e)}")
        return False, 0

def main():
    """Test ultra complet"""
    print("🤖 TEST ULTRA COMPLET SYSTÈME JARVIS")
    print("👤 Jean-Luc Passave")
    print("📅", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("🔍 TEST TRÈS TRÈS TRÈS TRÈS POUSSÉ")
    
    results = {}
    total_time = 0
    
    # TEST 1: INTERFACES WEB
    print_section("TEST 1: TOUTES LES INTERFACES WEB")
    
    interfaces = [
        ("Interface Principale", "http://localhost:7866"),
        ("Test Audio Complet", "http://localhost:8111"),
        ("Configuration Système", "http://localhost:8114"),
        ("Surveillance Temps Réel", "http://localhost:8260")
    ]
    
    interface_results = []
    interface_times = []
    
    for name, url in interfaces:
        success, response_time = test_endpoint(url, name)
        interface_results.append(success)
        interface_times.append(response_time)
        total_time += response_time
        time.sleep(1)
    
    results["interfaces"] = {
        "success": all(interface_results),
        "count": f"{sum(interface_results)}/{len(interface_results)}",
        "avg_time": sum(interface_times) / len(interface_times) if interface_times else 0
    }
    
    # TEST 2: AGENT JARVIS
    print_section("TEST 2: AGENT JARVIS DEEPSEEK")
    
    agent_url = "http://localhost:8001/v1/chat/completions"
    agent_payload = {
        "messages": [{"role": "user", "content": "Test ultra complet pour Jean-Luc Passave"}],
        "max_tokens": 150
    }
    
    agent_success, agent_time = test_endpoint(agent_url, "Agent JARVIS DeepSeek", "POST", agent_payload)
    total_time += agent_time
    
    results["agent"] = {
        "success": agent_success,
        "response_time": agent_time
    }
    
    # TEST 3: CONVERSATION MULTIPLE
    print_section("TEST 3: CONVERSATION MULTIPLE AVEC JARVIS")
    
    messages = [
        "Bonjour JARVIS, comment allez-vous aujourd'hui ?",
        "Pouvez-vous m'expliquer le machine learning ?",
        "Quelle est votre fonction principale ?",
        "Merci pour vos réponses JARVIS"
    ]
    
    conversation_results = []
    conversation_times = []
    
    for i, message in enumerate(messages):
        payload = {
            "messages": [{"role": "user", "content": message}],
            "max_tokens": 100
        }
        
        success, response_time = test_endpoint(agent_url, f"Message {i+1}/4: {message[:30]}...", "POST", payload)
        conversation_results.append(success)
        conversation_times.append(response_time)
        total_time += response_time
        time.sleep(2)
    
    results["conversation"] = {
        "success": all(conversation_results),
        "count": f"{sum(conversation_results)}/{len(conversation_results)}",
        "avg_time": sum(conversation_times) / len(conversation_times) if conversation_times else 0
    }
    
    # TEST 4: STRESS TEST
    print_section("TEST 4: STRESS TEST AGENT (5 REQUÊTES RAPIDES)")
    
    stress_results = []
    stress_times = []
    
    for i in range(5):
        payload = {
            "messages": [{"role": "user", "content": f"Stress test {i+1}: Répondez rapidement"}],
            "max_tokens": 30
        }
        
        success, response_time = test_endpoint(agent_url, f"Stress {i+1}/5", "POST", payload, 10)
        stress_results.append(success)
        stress_times.append(response_time)
        total_time += response_time
        time.sleep(0.5)
    
    results["stress"] = {
        "success": all(stress_results),
        "count": f"{sum(stress_results)}/{len(stress_results)}",
        "avg_time": sum(stress_times) / len(stress_times) if stress_times else 0,
        "max_time": max(stress_times) if stress_times else 0
    }
    
    # TEST 5: PERSISTANCE (TEST SUR 30 SECONDES)
    print_section("TEST 5: PERSISTANCE CONNEXION (6 TESTS SUR 30s)")
    
    persistence_results = []
    persistence_times = []
    
    for i in range(6):
        payload = {
            "messages": [{"role": "user", "content": f"Test persistance {i+1}"}],
            "max_tokens": 20
        }
        
        success, response_time = test_endpoint(agent_url, f"Persistance {i+1}/6", "POST", payload, 8)
        persistence_results.append(success)
        persistence_times.append(response_time)
        total_time += response_time
        time.sleep(5)
    
    results["persistence"] = {
        "success": sum(persistence_results) >= 5,  # Au moins 5/6 doivent réussir
        "count": f"{sum(persistence_results)}/{len(persistence_results)}",
        "success_rate": sum(persistence_results) / len(persistence_results) * 100
    }
    
    # TEST 6: RESSOURCES SYSTÈME
    print_section("TEST 6: RESSOURCES SYSTÈME")
    
    try:
        import psutil
        memory = psutil.virtual_memory()
        cpu = psutil.cpu_percent(interval=2)
        
        print(f"💾 Mémoire: {memory.percent:.1f}% ({memory.used//1024**3}/{memory.total//1024**3} GB)")
        print(f"🖥️ CPU: {cpu:.1f}%")
        
        resources_ok = memory.percent < 85 and cpu < 80
        
        if resources_ok:
            print("✅ Ressources système: EXCELLENTES")
        else:
            print("⚠️ Ressources système: ÉLEVÉES")
            
        results["resources"] = {
            "success": resources_ok,
            "memory_percent": memory.percent,
            "cpu_percent": cpu
        }
        
    except Exception as e:
        print(f"❌ Erreur test ressources: {e}")
        results["resources"] = {"success": False}
    
    # RÉSULTATS FINAUX
    print_section("RÉSULTATS FINAUX ULTRA COMPLETS")
    
    print("📊 DÉTAIL DES RÉSULTATS:")
    print(f"   🌐 Interfaces Web: {results['interfaces']['count']} - Temps moyen: {results['interfaces']['avg_time']:.3f}s")
    print(f"   🤖 Agent JARVIS: {'✅' if results['agent']['success'] else '❌'} - Temps: {results['agent']['response_time']:.3f}s")
    print(f"   💬 Conversation: {results['conversation']['count']} - Temps moyen: {results['conversation']['avg_time']:.3f}s")
    print(f"   ⚡ Stress Test: {results['stress']['count']} - Temps max: {results['stress']['max_time']:.3f}s")
    print(f"   🔄 Persistance: {results['persistence']['count']} - Taux: {results['persistence']['success_rate']:.1f}%")
    print(f"   💻 Ressources: {'✅' if results['resources']['success'] else '❌'}")
    
    # Calcul score global
    scores = [
        results['interfaces']['success'],
        results['agent']['success'],
        results['conversation']['success'],
        results['stress']['success'],
        results['persistence']['success'],
        results['resources']['success']
    ]
    
    total_score = sum(scores) / len(scores) * 100
    
    print(f"\n🎯 SCORE GLOBAL: {total_score:.1f}% ({sum(scores)}/{len(scores)} tests réussis)")
    print(f"⏱️ TEMPS TOTAL: {total_time:.3f}s")
    
    if total_score >= 95:
        print("\n🎉 SYSTÈME JARVIS: PARFAITEMENT OPÉRATIONNEL")
        print("✅ TOUS les tests critiques sont passés")
        print("🚀 Système EXCELLENT pour Jean-Luc")
    elif total_score >= 85:
        print("\n✅ SYSTÈME JARVIS: TRÈS BIEN OPÉRATIONNEL")
        print("🔧 Quelques optimisations possibles")
    elif total_score >= 70:
        print("\n⚠️ SYSTÈME JARVIS: CORRECTEMENT OPÉRATIONNEL")
        print("🔧 Améliorations recommandées")
    else:
        print("\n❌ SYSTÈME JARVIS: PROBLÈMES DÉTECTÉS")
        print("🔧 Intervention requise")
    
    return total_score >= 85

if __name__ == "__main__":
    success = main()
    print(f"\n{'✅ SUCCÈS' if success else '❌ ÉCHEC'}: Test ultra complet terminé")
    exit(0 if success else 1)
