{"neuron_memories": [{"neuron_id": "uuid-1750659730224", "activation_timestamp": "2025-06-23T02:22:10.000Z", "local_timestamp": "2025-06-23T02:22:10.000Z", "calendar_data": {"date": "2025-06-23", "time": "02:22:43", "day_of_week": "Unknown", "timezone": "Europe/Paris"}, "memory_content": {"user_message": "Est-ce que t'es là", "agent_response": "🤖 **JARVIS DeepSeek R1 8B** - Réponse générée\n\n**Votre question :** Est-ce que t'es là\n\n**Réponse :** Je suis JARVIS avec DeepSeek R1 8B intégré ! Je traite votre demande avec mes capacités avancées de raisonnement.\n\n*Note : Endpoint `/v1/chat/completions` maintenant fonctionnel !*\n", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "95dfa4c8-2a61-4d54-9a11-45382aeabaa0"}, "neuron_metadata": {"sujet": "Migré", "keywords": [], "complexity": 5.0, "agent": "DeepSeek-R1-8B", "thermal_zone": "migrated_neuron", "activation_level": 5.0, "memory_priority": 5.0, "retention_score": 7.0}}, {"neuron_id": "922d3714-84eb-4847-993f-fb3736c09748", "activation_timestamp": "2025-06-23T06:22:43.127128+00:00", "local_timestamp": "2025-06-23T08:22:43.127128+02:00", "calendar_data": {"date": "2025-06-23", "time": "08:22:43", "day_of_week": "Monday", "day_of_year": 174, "week_number": 26, "month": "June", "year": 2025, "timezone": "Europe/Paris", "utc_offset": "+0200"}, "memory_content": {"user_message": "Est-ce que t'es là", "agent_response": "🤖 **JARVIS DeepSeek R1 8B** - Réponse générée\n\n**Votre question :** Est-ce que t'es là\n\n**Réponse :** Je suis JARVIS avec DeepSeek R1 8B intégré ! Je traite votre demande avec mes capacités avancées de raisonnement.\n\n*Note : Endpoint `/v1/chat/completions` maintenant fonctionnel !*\n", "user_name": "<PERSON><PERSON><PERSON>", "conversation_id": "3f34b6c3-1d51-411e-ad4c-a4bef365d0ec"}, "neuron_metadata": {"sujet": "Est-Ce T'Es", "keywords": ["jarvis", "deepseek", "réponse", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "question"], "complexity": 3.58, "agent": "DeepSeek-R1-8B", "thermal_zone": "neuron_calendar_system", "activation_level": 2.55, "memory_priority": 5.0, "retention_score": 2.8}, "temporal_links": {"previous_day": "2025-06-22", "next_day": "2025-06-24", "same_time_yesterday": "2025-06-22T08:22:43.127128+02:00", "weekly_pattern": "Monday-08"}}], "internal_calendar": {"current_date": "2025-06-23", "current_time": "08:22:43", "timezone": "Europe/Paris", "calendar_events": {"2025-06-23": [{"time": "08:22:43", "event": "Conversation: Est-<PERSON>E<PERSON>", "neuron_id": "922d3714-84eb-4847-993f-fb3736c09748", "priority": 5.0}]}, "daily_summaries": {}, "weekly_patterns": {"Monday-08": 1}, "monthly_trends": {}}, "neuron_stats": {"total_neurons": 2, "active_neurons": 0, "backup_neurons": 1, "memory_efficiency": 0.5, "activation_patterns": {"level_2": 1}, "retention_rates": {}, "temporal_distribution": {}}, "backup_system": {"last_backup": "2025-06-23T02:22:43.127334", "backup_frequency": "continuous", "backup_locations": [], "integrity_checks": [], "recovery_points": []}, "learning_patterns": {"user_habits": {}, "conversation_patterns": {}, "temporal_preferences": {}, "subject_interests": {}, "interaction_styles": {}}, "lastUpdate": "2025-06-23T02:22:43.126831"}