import time
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 CLIENT MCP POUR JARVIS - CONNEXION AU BROKER
Intégration WebSocket avec le MCP Broker complet
Jean-Luc Passave - 22 Juin 2025
"""

import asyncio
import websockets
import json
import threading
from typing import Dict, Any, Callable, Optional
from datetime import datetime
import uuid

class JarvisMCPClient:
    """Client MCP pour connexion au Broker JARVIS"""
    
    def __init__(self, agent_id: str, broker_url: str = "ws://localhost:8765"):
        self.agent_id = agent_id
        self.broker_url = f"{broker_url}/ws/{agent_id}"
        self.websocket = None
        self.connected = False
        self.message_handlers: Dict[str, Callable] = {}
        self.running = False
        
    async def connect(self):
        """Connexion au MCP Broker"""
        try:
            self.websocket = await websockets.connect(self.broker_url)
            self.connected = True
            self.running = True
            print(f"✅ [{self.agent_id}] connecté au MCP Broker")
            
            # Démarrer l'écoute des messages
            asyncio.create_task(self._listen_messages())
            
        except Exception as e:
            print(f"❌ Erreur connexion MCP [{self.agent_id}]: {e}")
            self.connected = False
    
    async def disconnect(self):
        """Déconnexion du MCP Broker"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
        self.connected = False
        print(f"🔌 [{self.agent_id}] déconnecté du MCP Broker")
    
    async def send_message(self, target_agent: str, message_type: str, content: Any):
        """Envoi d'un message via MCP"""
        if not self.connected:
            print(f"⚠️ [{self.agent_id}] non connecté au MCP")
            return False
        
        message = {
            "to": target_agent,
            "payload": {
                "type": message_type,
                "content": content,
                "sender_id": self.agent_id,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        try:
            await self.websocket.send(json.dumps(message))
            print(f"📤 [{self.agent_id}] → [{target_agent}]: {message_type}")
            return True
        except Exception as e:
            print(f"❌ Erreur envoi message [{self.agent_id}]: {e}")
            return False
    
    async def broadcast_message(self, message_type: str, content: Any):
        """Diffusion d'un message à tous les agents"""
        return await self.send_message("broadcast", message_type, content)
    
    def register_handler(self, message_type: str, handler: Callable):
        """Enregistre un handler pour un type de message"""
        self.message_handlers[message_type] = handler
        print(f"🔧 [{self.agent_id}] handler enregistré: {message_type}")
    
    async def _listen_messages(self):
        """Écoute continue des messages MCP"""
        while self.running and self.connected:
            try:
                message_raw = await self.websocket.recv()
                message = json.loads(message_raw)
                await self._handle_message(message)
                
            except websockets.exceptions.ConnectionClosed:
                print(f"🔌 Connexion fermée [{self.agent_id}]")
                self.connected = False
                break
            except Exception as e:
                print(f"❌ Erreur écoute [{self.agent_id}]: {e}")
    
    async def _handle_message(self, message: Dict[str, Any]):
        """Traite un message reçu"""
        payload = message.get("payload", {})
        message_type = payload.get("type", "unknown")
        sender = message.get("from", "unknown")
        
        print(f"📥 [{self.agent_id}] ← [{sender}]: {message_type}")
        
        # Appeler le handler approprié
        if message_type in self.message_handlers:
            try:
                handler = self.message_handlers[message_type]
                if asyncio.iscoroutinefunction(handler):
                    await handler(message)
                else:
                    handler(message)
            except Exception as e:
                print(f"❌ Erreur handler [{message_type}]: {e}")
        else:
            # Handler par défaut
            await self._default_handler(message)
    
    async def _default_handler(self, message: Dict[str, Any]):
        """Handler par défaut pour messages non traités"""
        payload = message.get("payload", {})
        print(f"📋 [{self.agent_id}] message non traité: {payload.get('type', 'unknown')}")

# 🤖 AGENTS JARVIS SPÉCIALISÉS

class JarvisMainAgent(JarvisMCPClient):
    """Agent principal JARVIS"""
    
    def __init__(self):
        super().__init__("jarvis_main")
        self.setup_handlers()
    
    def setup_handlers(self):
        """Configuration des handlers spécialisés"""
        self.register_handler("user_input", self.handle_user_input)
        self.register_handler("memory_request", self.handle_memory_request)
        self.register_handler("creative_request", self.handle_creative_request)
    
    async def handle_user_input(self, message: Dict[str, Any]):
        """Traite les entrées utilisateur"""
        content = message["payload"]["content"]
        print(f"🤖 JARVIS traite: {content}")
        
        # Transférer à la mémoire pour analyse
        await self.send_message("memory_manager", "analyze_input", content)
        
        # Générer une réponse
        response = f"🤖 JARVIS: J'ai bien reçu votre message: {content}"
        await self.send_message("interface_gui", "display_response", response)
    
    async def handle_memory_request(self, message: Dict[str, Any]):
        """Traite les demandes de mémoire"""
        await self.send_message("memory_manager", "retrieve_context", message["payload"])
    
    async def handle_creative_request(self, message: Dict[str, Any]):
        """Traite les demandes créatives"""
        await self.send_message("creative_agent", "generate_creative", message["payload"])

class JarvisMemoryAgent(JarvisMCPClient):
    """Agent gestionnaire mémoire thermique"""
    
    def __init__(self):
        super().__init__("memory_manager")
        self.setup_handlers()
    
    def setup_handlers(self):
        """Configuration des handlers mémoire"""
        self.register_handler("analyze_input", self.analyze_input)
        self.register_handler("retrieve_context", self.retrieve_context)
        self.register_handler("store_memory", self.store_memory)
    
    async def analyze_input(self, message: Dict[str, Any]):
        """Analyse l'entrée utilisateur"""
        content = message["payload"]["content"]
        print(f"🧠 Analyse mémoire: {content}")
        
        # Simulation analyse mémoire
        analysis = {
            "sentiment": "positif",
            "keywords": ["jarvis", "mcp", "broker"],
            "context_relevance": 0.8
        }
        
        await self.send_message("jarvis_main", "memory_analysis", analysis)
    
    async def retrieve_context(self, message: Dict[str, Any]):
        """Récupère le contexte de la mémoire"""
        print("🧠 Récupération contexte mémoire thermique")
        # Implémentation récupération mémoire
    
    async def store_memory(self, message: Dict[str, Any]):
        """Stocke en mémoire thermique"""
        print("🧠 Stockage mémoire thermique")
        # Implémentation stockage mémoire

class JarvisCreativeAgent(JarvisMCPClient):
    """Agent créatif JARVIS"""
    
    def __init__(self):
        super().__init__("creative_agent")
        self.setup_handlers()
    
    def setup_handlers(self):
        """Configuration des handlers créatifs"""
        self.register_handler("generate_creative", self.generate_creative)
        self.register_handler("generate_thought", self.generate_thought)
    
    async def generate_creative(self, message: Dict[str, Any]):
        """Génère du contenu créatif"""
        content = message["payload"]["content"]
        print(f"🎨 Génération créative: {content}")
        
        # Génération créative basée sur mémoire
        creative_response = f"🎨 Création inspirée par: {content}"
        await self.send_message("jarvis_main", "creative_response", creative_response)
    
    async def generate_thought(self, message: Dict[str, Any]):
        """Génère une pensée créative"""
        print("💭 Génération pensée créative")
        # Implémentation génération pensée

# 🚀 FONCTIONS D'INITIALISATION

async def start_jarvis_agents():
    """Démarre tous les agents JARVIS"""
    print("🚀 Démarrage agents JARVIS MCP")
    
    # Créer les agents
    main_agent = JarvisMainAgent()
    memory_agent = JarvisMemoryAgent()
    creative_agent = JarvisCreativeAgent()
    
    # Connecter au broker
    await main_agent.connect()
    await memory_agent.connect()
    await creative_agent.connect()
    
    print("✅ Tous les agents JARVIS connectés au MCP Broker")
    
    return main_agent, memory_agent, creative_agent

def start_mcp_client_thread():
    """Démarre les clients MCP en thread séparé"""
    def run_clients():
        asyncio.run(start_jarvis_agents())
    
    thread = threading.Thread(target=run_clients, daemon=True)
    thread.start()
    return thread

if __name__ == "__main__":
    print("🤖 Test clients MCP JARVIS")
    asyncio.run(start_jarvis_agents())
