#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS SURVEILLANCE INTERFACE - JEAN-LUC PASSAVE
Interface de surveillance avec navigation aller-retour
Port 8260 (port 8102 occupé)
"""

import gradio as gr
import requests
import datetime
import psutil
import json
import os

def get_system_stats():
    """Récupère les statistiques système"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "cpu": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used": memory.used // (1024**3),  # GB
            "memory_total": memory.total // (1024**3),  # GB
            "disk_percent": disk.percent,
            "disk_used": disk.used // (1024**3),  # GB
            "disk_total": disk.total // (1024**3)  # GB
        }
    except:
        return {
            "cpu": 0,
            "memory_percent": 0,
            "memory_used": 0,
            "memory_total": 16,
            "disk_percent": 0,
            "disk_used": 0,
            "disk_total": 1000
        }

def check_jarvis_services():
    """Vérifie l'état des services JARVIS"""
    services = {
        "Interface Principale": "http://localhost:7866",
        "Configuration Système": "http://localhost:8114", 
        "Audio Complet": "http://localhost:8111",
        "Agent DeepSeek": "http://localhost:8000/v1/chat/completions",
        "MCP Broker": "http://localhost:8766/status"
    }
    
    status = {}
    for name, url in services.items():
        try:
            if "chat/completions" in url:
                response = requests.post(url, json={"messages": [{"role": "user", "content": "test"}], "max_tokens": 1}, timeout=3)
            else:
                response = requests.get(url, timeout=3)
            
            if response.status_code == 200:
                status[name] = "✅ Opérationnel"
            else:
                status[name] = f"⚠️ Erreur {response.status_code}"
        except:
            status[name] = "❌ Non accessible"
    
    return status

def create_surveillance_display():
    """Crée l'affichage de surveillance"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    stats = get_system_stats()
    services = check_jarvis_services()
    
    return f"""
    <div style="background: linear-gradient(135deg, #e8f5e8, #d4edda); padding: 20px; border-radius: 15px; border-left: 6px solid #28a745; margin: 10px 0;">
        <div style="text-align: center; margin-bottom: 15px;">
            <h3 style="color: #28a745; margin: 0; font-size: 1.4em;">📊 SURVEILLANCE SYSTÈME JARVIS</h3>
            <p style="margin: 5px 0; color: #666;">Monitoring en Temps Réel - Jean-Luc Passave</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin: 10px 0; border-left: 3px solid #28a745;">
            <strong style="color: #28a745; font-size: 1.1em;">⏰ Dernière Mise à Jour [{current_time}]</strong>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
            <div style="background: #fff3cd; padding: 15px; border-radius: 10px; border-left: 4px solid #ffc107;">
                <h4 style="color: #856404; margin: 0 0 10px 0;">💻 Ressources Système</h4>
                <div style="font-size: 0.9em; line-height: 1.4;">
                    <div style="margin: 5px 0;"><strong>CPU:</strong> {stats['cpu']:.1f}%</div>
                    <div style="margin: 5px 0;"><strong>RAM:</strong> {stats['memory_percent']:.1f}% ({stats['memory_used']}/{stats['memory_total']} GB)</div>
                    <div style="margin: 5px 0;"><strong>Disque:</strong> {stats['disk_percent']:.1f}% ({stats['disk_used']}/{stats['disk_total']} GB)</div>
                </div>
            </div>
            
            <div style="background: #d1ecf1; padding: 15px; border-radius: 10px; border-left: 4px solid #17a2b8;">
                <h4 style="color: #0c5460; margin: 0 0 10px 0;">🔗 Services JARVIS</h4>
                <div style="font-size: 0.9em; line-height: 1.4;">
                    {''.join([f'<div style="margin: 5px 0;"><strong>{name}:</strong> {status}</div>' for name, status in services.items()])}
                </div>
            </div>
        </div>
        
        <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #2196f3;">
            <h4 style="color: #2196f3; margin: 0 0 10px 0;">🏠 Navigation Aller-Retour</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-size: 0.9em;">
                <button onclick="goToHome()" style="background: #28a745; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">🏠 Interface Principale</button>
                <button onclick="goToConfig()" style="background: #ff9800; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">⚙️ Configuration</button>
                <button onclick="goToLastTab()" style="background: #2196f3; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">↩️ Dernier Onglet</button>
            </div>
        </div>
    </div>
    
    <script>
    // NAVIGATION ALLER-RETOUR - JEAN-LUC PASSAVE
    function goToHome() {{
        localStorage.setItem('jarvis_last_url', window.location.href);
        window.location.href = 'http://localhost:7866';
    }}
    
    function goToConfig() {{
        localStorage.setItem('jarvis_last_url', window.location.href);
        window.open('http://localhost:8114', '_blank');
    }}
    
    function goToLastTab() {{
        const lastUrl = localStorage.getItem('jarvis_last_url');
        if (lastUrl && lastUrl !== window.location.href) {{
            window.open(lastUrl, '_blank');
        }} else {{
            window.open('http://localhost:7866', '_blank');
        }}
    }}
    
    console.log('📊 Surveillance JARVIS initialisée');
    </script>
    """

def create_jarvis_surveillance_interface():
    """Interface de surveillance JARVIS"""
    
    with gr.Blocks(
        title="📊 JARVIS Surveillance Système",
        theme=gr.themes.Soft(),
        css="""
        .surveillance-btn {
            background: linear-gradient(45deg, #4caf50, #8bc34a) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.2em !important;
            padding: 15px 25px !important;
            border-radius: 20px !important;
            box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3) !important;
            transition: all 0.3s ease !important;
        }
        .surveillance-btn:hover {
            background: linear-gradient(45deg, #8bc34a, #4caf50) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.4) !important;
        }
        """
    ) as interface:
        
        # Header avec Navigation
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #4caf50, #8bc34a); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <button onclick="goToHome()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold;">
                    🏠 INTERFACE PRINCIPALE
                </button>
                <div style="text-align: center;">
                    <h1 style="margin: 0; font-size: 2.2em;">📊 Surveillance Système</h1>
                </div>
                <button onclick="goToLastTab()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold;">
                    ↩️ DERNIER ONGLET
                </button>
            </div>
            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Monitoring Temps Réel - Jean-Luc Passave</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                # SURVEILLANCE EN TEMPS RÉEL
                surveillance_display = gr.HTML(create_surveillance_display())
                
                # BOUTONS DE CONTRÔLE
                with gr.Row():
                    refresh_btn = gr.Button("🔄 ACTUALISER", elem_classes=["surveillance-btn"])
                    auto_refresh_btn = gr.Button("⚡ AUTO-REFRESH", elem_classes=["surveillance-btn"])
                    stop_refresh_btn = gr.Button("⏹️ ARRÊTER AUTO", elem_classes=["surveillance-btn"])
                
                # ACTIONS SYSTÈME
                gr.HTML("<h3 style='color: #4caf50; text-align: center; margin-top: 30px;'>🔧 Actions Système</h3>")
                
                with gr.Row():
                    restart_services_btn = gr.Button("🔄 Redémarrer Services", variant="primary")
                    clean_memory_btn = gr.Button("🧹 Nettoyer Mémoire", variant="secondary")
                    backup_btn = gr.Button("💾 Sauvegarde", variant="secondary")
            
            with gr.Column(scale=1):
                # Zone d'informations
                gr.HTML("""
                <div style="background: #f8d7da; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin: 0 0 10px 0;">ℹ️ Informations Surveillance</h4>
                    <div style="font-size: 0.9em; line-height: 1.4;">
                        <div style="margin: 5px 0;">📊 <strong>Port Surveillance</strong> : 8260</div>
                        <div style="margin: 5px 0;">🔄 <strong>Actualisation</strong> : Manuelle/Auto</div>
                        <div style="margin: 5px 0;">🏠 <strong>Navigation</strong> : Aller-retour</div>
                        <div style="margin: 5px 0;">⚡ <strong>Temps Réel</strong> : Monitoring continu</div>
                    </div>
                </div>
                
                <div style="background: #d4edda; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #28a745;">
                    <h4 style="color: #155724; margin: 0 0 10px 0;">✅ Services Surveillés</h4>
                    <div style="font-size: 0.9em; line-height: 1.4;">
                        <div style="margin: 5px 0;">🔊 <strong>Interface Principale</strong> : 7866</div>
                        <div style="margin: 5px 0;">⚙️ <strong>Configuration</strong> : 8114</div>
                        <div style="margin: 5px 0;">🎧 <strong>Audio Complet</strong> : 8111</div>
                        <div style="margin: 5px 0;">🤖 <strong>Agent DeepSeek</strong> : 8000</div>
                        <div style="margin: 5px 0;">🔗 <strong>MCP Broker</strong> : 8766</div>
                    </div>
                </div>
                """)
        
        # Fonctions
        def refresh_surveillance():
            return create_surveillance_display()
        
        def start_auto_refresh():
            return create_surveillance_display()
        
        def stop_auto_refresh():
            return create_surveillance_display()
        
        def restart_services():
            return create_surveillance_display()
        
        def clean_memory():
            return create_surveillance_display()
        
        def create_backup():
            return create_surveillance_display()
        
        # Connexions
        refresh_btn.click(
            fn=refresh_surveillance,
            outputs=[surveillance_display]
        )
        
        auto_refresh_btn.click(
            fn=start_auto_refresh,
            outputs=[surveillance_display],
            js="() => { setInterval(() => { document.querySelector('button[data-testid=\"refresh-btn\"]')?.click(); }, 5000); console.log('⚡ Auto-refresh activé'); }"
        )
        
        stop_refresh_btn.click(
            fn=stop_auto_refresh,
            outputs=[surveillance_display],
            js="() => { console.log('⏹️ Auto-refresh arrêté'); }"
        )
        
        restart_services_btn.click(
            fn=restart_services,
            outputs=[surveillance_display]
        )
        
        clean_memory_btn.click(
            fn=clean_memory,
            outputs=[surveillance_display]
        )
        
        backup_btn.click(
            fn=create_backup,
            outputs=[surveillance_display]
        )
    
    return interface

if __name__ == "__main__":
    print("📊 JARVIS SURVEILLANCE INTERFACE - JEAN-LUC PASSAVE")
    print("=" * 60)
    print("📊 Monitoring temps réel des services JARVIS")
    print("🏠 Navigation: Boutons aller-retour")
    print("🔄 Actualisation: Manuelle et automatique")
    print("⚙️ Actions: Redémarrage, nettoyage, sauvegarde")
    print("🌐 Interface disponible sur localhost:8260")
    print("=" * 60)
    
    interface = create_jarvis_surveillance_interface()
    interface.launch(
        server_name="localhost",
        server_port=8260,
        share=False,
        debug=True,
        show_error=True
    )
