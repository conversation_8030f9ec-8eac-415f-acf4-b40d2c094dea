#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 CORRECTION CONNEXION PERSISTANTE JARVIS - JEAN-LUC PASSAVE
Correction du problème de déconnexion quand l'interface reste ouverte
"""

import threading
import time
import requests
import json
from datetime import datetime

class GestionnaireConnexionPersistante:
    """GESTIONNAIRE CONNEXION PERSISTANTE JARVIS - JEAN-LUC PASSAVE"""
    
    def __init__(self):
        self.connexion_active = False
        self.thread_keepalive = None
        self.derniere_activite = datetime.now()
        self.url_deepseek = "http://localhost:8000/v1/chat/completions"
        self.intervalle_keepalive = 30  # 30 secondes
        self.timeout_inactivite = 300   # 5 minutes
        
    def demarrer_keepalive(self):
        """DÉMARRER SYSTÈME KEEP-ALIVE"""
        if self.connexion_active:
            print("⚠️ Keep-alive déjà actif")
            return
        
        self.connexion_active = True
        self.thread_keepalive = threading.Thread(target=self._boucle_keepalive, daemon=True)
        self.thread_keepalive.start()
        
        print("🚀 SYSTÈME KEEP-ALIVE DÉMARRÉ")
        print(f"⏰ Vérification toutes les {self.intervalle_keepalive} secondes")
        print(f"🕐 Timeout inactivité: {self.timeout_inactivite} secondes")
    
    def arreter_keepalive(self):
        """ARRÊTER SYSTÈME KEEP-ALIVE"""
        self.connexion_active = False
        if self.thread_keepalive:
            self.thread_keepalive.join(timeout=5)
        
        print("⏹️ SYSTÈME KEEP-ALIVE ARRÊTÉ")
    
    def _boucle_keepalive(self):
        """BOUCLE PRINCIPALE KEEP-ALIVE"""
        while self.connexion_active:
            try:
                # Attendre l'intervalle
                time.sleep(self.intervalle_keepalive)
                
                # Vérifier si on doit envoyer un ping
                temps_inactif = (datetime.now() - self.derniere_activite).total_seconds()
                
                if temps_inactif > self.intervalle_keepalive:
                    # Envoyer ping de connexion
                    if self._envoyer_ping_connexion():
                        print(f"✅ Ping connexion réussi ({datetime.now().strftime('%H:%M:%S')})")
                        self.derniere_activite = datetime.now()
                    else:
                        print(f"❌ Ping connexion échoué - Tentative reconnexion...")
                        self._tenter_reconnexion()
                
            except Exception as e:
                print(f"❌ Erreur keep-alive: {e}")
                time.sleep(60)  # Attendre 1 minute avant de réessayer
    
    def _envoyer_ping_connexion(self):
        """ENVOYER PING DE CONNEXION À DEEPSEEK"""
        try:
            # Message ping minimal
            ping_data = {
                "model": "deepseek-r1:8b",
                "messages": [
                    {
                        "role": "system",
                        "content": "Tu es JARVIS. Réponds juste 'PING_OK' pour confirmer la connexion."
                    },
                    {
                        "role": "user", 
                        "content": "ping"
                    }
                ],
                "max_tokens": 10,
                "temperature": 0.1,
                "stream": False
            }
            
            # Envoyer requête avec timeout court
            response = requests.post(
                self.url_deepseek,
                json=ping_data,
                timeout=10,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                return 'PING' in content.upper() or 'OK' in content.upper()
            
            return False
            
        except Exception as e:
            print(f"❌ Erreur ping: {e}")
            return False
    
    def _tenter_reconnexion(self):
        """TENTER RECONNEXION DEEPSEEK"""
        print("🔄 TENTATIVE DE RECONNEXION...")
        
        for tentative in range(3):
            try:
                print(f"🔄 Tentative {tentative + 1}/3...")
                
                if self._envoyer_ping_connexion():
                    print("✅ RECONNEXION RÉUSSIE")
                    self.derniere_activite = datetime.now()
                    return True
                
                time.sleep(5)  # Attendre 5 secondes entre tentatives
                
            except Exception as e:
                print(f"❌ Tentative {tentative + 1} échouée: {e}")
        
        print("❌ RECONNEXION ÉCHOUÉE - Vérifiez DeepSeek R1")
        return False
    
    def marquer_activite(self):
        """MARQUER ACTIVITÉ UTILISATEUR"""
        self.derniere_activite = datetime.now()
    
    def verifier_connexion_deepseek(self):
        """VÉRIFIER CONNEXION DEEPSEEK"""
        try:
            # Test simple de connexion
            test_data = {
                "model": "deepseek-r1:8b",
                "messages": [
                    {
                        "role": "user",
                        "content": "test"
                    }
                ],
                "max_tokens": 5,
                "temperature": 0.1
            }
            
            response = requests.post(
                self.url_deepseek,
                json=test_data,
                timeout=15
            )
            
            if response.status_code == 200:
                print("✅ CONNEXION DEEPSEEK ACTIVE")
                return True
            else:
                print(f"❌ CONNEXION DEEPSEEK ERREUR: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ CONNEXION DEEPSEEK IMPOSSIBLE: {e}")
            return False

# Instance globale
gestionnaire_connexion = GestionnaireConnexionPersistante()

def demarrer_connexion_persistante():
    """DÉMARRER CONNEXION PERSISTANTE JARVIS"""
    print("🔧 DÉMARRAGE CONNEXION PERSISTANTE JARVIS - JEAN-LUC PASSAVE")
    
    # Vérifier connexion DeepSeek
    if gestionnaire_connexion.verifier_connexion_deepseek():
        gestionnaire_connexion.demarrer_keepalive()
        return True
    else:
        print("❌ IMPOSSIBLE DE DÉMARRER - DEEPSEEK NON ACCESSIBLE")
        return False

def arreter_connexion_persistante():
    """ARRÊTER CONNEXION PERSISTANTE"""
    gestionnaire_connexion.arreter_keepalive()

def marquer_activite_utilisateur():
    """MARQUER ACTIVITÉ UTILISATEUR"""
    gestionnaire_connexion.marquer_activite()

def main():
    """FONCTION PRINCIPALE DE TEST"""
    print("🔧 TEST CONNEXION PERSISTANTE JARVIS - JEAN-LUC PASSAVE")
    print("=" * 60)
    
    try:
        # Démarrer connexion persistante
        if demarrer_connexion_persistante():
            print("🚀 CONNEXION PERSISTANTE ACTIVE")
            print("💡 JARVIS restera connecté même si vous laissez l'interface ouverte")
            print("💡 Utilisez Ctrl+C pour arrêter")
            
            # Boucle de test
            while True:
                time.sleep(60)
                print(f"🕐 Connexion active - {datetime.now().strftime('%H:%M:%S')}")
        else:
            print("❌ ÉCHEC DÉMARRAGE CONNEXION PERSISTANTE")
    
    except KeyboardInterrupt:
        print("\n🛑 ARRÊT DEMANDÉ")
        arreter_connexion_persistante()
        print("✅ CONNEXION PERSISTANTE ARRÊTÉE")

if __name__ == "__main__":
    main()
