#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📱 GESTIONNAIRE DE NOTIFICATIONS JARVIS EXPERT NIVEAU 20
Système complet de notifications WhatsApp, Email, Système
Jean-Luc <PERSON>ave - 23 Juin 2025
"""

import os
import json
import time
import smtplib
import platform
import subprocess
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from dataclasses import dataclass
from collections import defaultdict, deque
import threading

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class NotificationConfig:
    """Configuration des notifications"""
    # WhatsApp via Twilio
    twilio_sid: Optional[str] = None
    twilio_token: Optional[str] = None
    twilio_whatsapp_from: Optional[str] = None
    whatsapp_to: Optional[str] = None
    
    # Email
    smtp_server: Optional[str] = None
    smtp_port: int = 587
    email_user: Optional[str] = None
    email_password: Optional[str] = None
    email_to: Optional[str] = None
    
    # Système
    enable_system_notifications: bool = True
    enable_console_logs: bool = True
    enable_file_logs: bool = True
    
    # Rate limiting
    max_notifications_per_minute: int = 10
    max_notifications_per_hour: int = 100
    
    # Priorités
    priority_levels: Dict[str, int] = None
    
    def __post_init__(self):
        if self.priority_levels is None:
            self.priority_levels = {
                "DEBUG": 0,
                "INFO": 1,
                "WARNING": 2,
                "ERROR": 3,
                "CRITICAL": 4
            }

class NotificationManager:
    """
    🔔 GESTIONNAIRE DE NOTIFICATIONS JARVIS EXPERT NIVEAU 20
    
    Fonctionnalités:
    - Notifications WhatsApp via Twilio
    - Notifications Email SMTP
    - Notifications système (macOS/Linux/Windows)
    - Rate limiting intelligent
    - Système de priorités
    - Fallback automatique
    - Logs détaillés
    - Configuration JSON
    """
    
    def __init__(self, config_file: str = "notification_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        
        # Rate limiting
        self.notification_history = deque(maxlen=1000)
        self.rate_limits = {
            "minute": deque(maxlen=self.config.max_notifications_per_minute),
            "hour": deque(maxlen=self.config.max_notifications_per_hour)
        }
        
        # Statistiques
        self.stats = defaultdict(int)
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Initialisation des services
        self._init_twilio()
        self._init_email()
        
        logger.info("🔔 Gestionnaire de notifications JARVIS initialisé")
    
    def _load_config(self) -> NotificationConfig:
        """Charge la configuration depuis le fichier JSON"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                return NotificationConfig(**config_data)
            else:
                # Créer configuration par défaut
                default_config = NotificationConfig()
                self._save_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"❌ Erreur chargement config: {e}")
            return NotificationConfig()
    
    def _save_config(self, config: NotificationConfig):
        """Sauvegarde la configuration"""
        try:
            config_dict = {
                "twilio_sid": config.twilio_sid,
                "twilio_token": config.twilio_token,
                "twilio_whatsapp_from": config.twilio_whatsapp_from,
                "whatsapp_to": config.whatsapp_to,
                "smtp_server": config.smtp_server,
                "smtp_port": config.smtp_port,
                "email_user": config.email_user,
                "email_password": config.email_password,
                "email_to": config.email_to,
                "enable_system_notifications": config.enable_system_notifications,
                "enable_console_logs": config.enable_console_logs,
                "enable_file_logs": config.enable_file_logs,
                "max_notifications_per_minute": config.max_notifications_per_minute,
                "max_notifications_per_hour": config.max_notifications_per_hour,
                "priority_levels": config.priority_levels
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde config: {e}")
    
    def _init_twilio(self):
        """Initialise le client Twilio"""
        try:
            if (self.config.twilio_sid and self.config.twilio_token):
                from twilio.rest import Client
                self.twilio_client = Client(
                    self.config.twilio_sid, 
                    self.config.twilio_token
                )
                logger.info("✅ Client Twilio initialisé")
            else:
                self.twilio_client = None
                logger.warning("⚠️ Configuration Twilio manquante")
        except ImportError:
            logger.error("❌ Module twilio non installé: pip install twilio")
            self.twilio_client = None
        except Exception as e:
            logger.error(f"❌ Erreur initialisation Twilio: {e}")
            self.twilio_client = None
    
    def _init_email(self):
        """Initialise la configuration email"""
        if (self.config.smtp_server and self.config.email_user and 
            self.config.email_password):
            logger.info("✅ Configuration email disponible")
        else:
            logger.warning("⚠️ Configuration email incomplète")
    
    def notify(self, message: str, level: str = "INFO", 
               force_whatsapp: bool = False, force_email: bool = False,
               metadata: Dict[str, Any] = None) -> bool:
        """
        Envoie une notification multi-canal
        
        Args:
            message: Message à envoyer
            level: Niveau de priorité (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            force_whatsapp: Forcer l'envoi WhatsApp
            force_email: Forcer l'envoi email
            metadata: Métadonnées additionnelles
        
        Returns:
            True si au moins un canal a réussi
        """
        with self.lock:
            try:
                # Vérification rate limiting
                if not self._check_rate_limit():
                    logger.warning("⚠️ Rate limit atteint, notification ignorée")
                    return False
                
                # Formatage du message
                formatted_message = self._format_message(message, level, metadata)
                
                success_count = 0
                
                # Console (toujours actif)
                if self.config.enable_console_logs:
                    print(f"[{level}] {formatted_message}")
                    success_count += 1
                
                # Logs fichier
                if self.config.enable_file_logs:
                    self._log_to_file(formatted_message, level)
                    success_count += 1
                
                # Notifications système
                if self.config.enable_system_notifications:
                    if self._send_system_notification(message, level):
                        success_count += 1
                
                # WhatsApp (selon priorité ou force)
                priority = self.config.priority_levels.get(level, 0)
                if (force_whatsapp or priority >= 2):  # WARNING et plus
                    if self._send_whatsapp_notification(formatted_message):
                        success_count += 1
                
                # Email (selon priorité ou force)
                if (force_email or priority >= 3):  # ERROR et plus
                    if self._send_email_notification(formatted_message, level):
                        success_count += 1
                
                # Mise à jour statistiques
                self.stats[f"notifications_{level.lower()}"] += 1
                self.stats["total_notifications"] += 1
                
                # Historique
                self.notification_history.append({
                    "timestamp": datetime.now().isoformat(),
                    "message": message,
                    "level": level,
                    "success_count": success_count,
                    "metadata": metadata
                })
                
                return success_count > 0
                
            except Exception as e:
                logger.error(f"❌ Erreur notification: {e}")
                return False
    
    def _check_rate_limit(self) -> bool:
        """Vérifie les limites de taux"""
        now = datetime.now()
        
        # Nettoyer les anciens timestamps
        minute_ago = now - timedelta(minutes=1)
        hour_ago = now - timedelta(hours=1)
        
        # Filtrer les timestamps récents
        self.rate_limits["minute"] = deque(
            [ts for ts in self.rate_limits["minute"] if ts > minute_ago],
            maxlen=self.config.max_notifications_per_minute
        )
        self.rate_limits["hour"] = deque(
            [ts for ts in self.rate_limits["hour"] if ts > hour_ago],
            maxlen=self.config.max_notifications_per_hour
        )
        
        # Vérifier les limites
        if len(self.rate_limits["minute"]) >= self.config.max_notifications_per_minute:
            return False
        if len(self.rate_limits["hour"]) >= self.config.max_notifications_per_hour:
            return False
        
        # Ajouter timestamp actuel
        self.rate_limits["minute"].append(now)
        self.rate_limits["hour"].append(now)
        
        return True
    
    def _format_message(self, message: str, level: str, 
                       metadata: Dict[str, Any] = None) -> str:
        """Formate le message avec timestamp et métadonnées"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted = f"[{timestamp}] [JARVIS Expert] [{level}] {message}"
        
        if metadata:
            metadata_str = " | ".join([f"{k}:{v}" for k, v in metadata.items()])
            formatted += f" | {metadata_str}"
        
        return formatted
    
    def _log_to_file(self, message: str, level: str):
        """Écrit dans le fichier de log"""
        try:
            log_file = f"jarvis_notifications_{datetime.now().strftime('%Y%m%d')}.log"
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{message}\n")
        except Exception as e:
            logger.error(f"❌ Erreur log fichier: {e}")
    
    def _send_system_notification(self, message: str, level: str) -> bool:
        """Envoie une notification système"""
        try:
            os_name = platform.system()
            title = f"JARVIS Expert - {level}"
            
            if os_name == "Darwin":  # macOS
                script = f'display notification "{message}" with title "{title}"'
                subprocess.run(['osascript', '-e', script], 
                             capture_output=True, timeout=5)
                return True
                
            elif os_name == "Linux":
                subprocess.run(['notify-send', title, message], 
                             capture_output=True, timeout=5)
                return True
                
            elif os_name == "Windows":
                try:
                    from win10toast import ToastNotifier
                    toaster = ToastNotifier()
                    toaster.show_toast(title, message, duration=5)
                    return True
                except ImportError:
                    logger.warning("⚠️ win10toast non installé pour Windows")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Erreur notification système: {e}")
            return False

    def _send_whatsapp_notification(self, message: str) -> bool:
        """Envoie une notification WhatsApp via Twilio"""
        try:
            if not self.twilio_client or not self.config.whatsapp_to:
                return False

            # Limitation de taille du message WhatsApp
            if len(message) > 1600:
                message = message[:1597] + "..."

            self.twilio_client.messages.create(
                from_=f'whatsapp:{self.config.twilio_whatsapp_from}',
                to=f'whatsapp:{self.config.whatsapp_to}',
                body=f"🤖 {message}"
            )

            logger.info("📱 Notification WhatsApp envoyée")
            return True

        except Exception as e:
            logger.error(f"❌ Erreur WhatsApp: {e}")
            return False

    def _send_email_notification(self, message: str, level: str) -> bool:
        """Envoie une notification par email"""
        try:
            if not all([self.config.smtp_server, self.config.email_user,
                       self.config.email_password, self.config.email_to]):
                return False

            # Création du message email
            msg = MIMEMultipart()
            msg['From'] = self.config.email_user
            msg['To'] = self.config.email_to
            msg['Subject'] = f"JARVIS Expert Alert - {level}"

            # Corps du message
            body = f"""
            JARVIS Expert Notification

            Niveau: {level}
            Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            Message:
            {message}

            ---
            Envoyé automatiquement par JARVIS Expert Niveau 20
            """

            msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # Envoi
            with smtplib.SMTP(self.config.smtp_server, self.config.smtp_port) as server:
                server.starttls()
                server.login(self.config.email_user, self.config.email_password)
                server.send_message(msg)

            logger.info("📧 Notification email envoyée")
            return True

        except Exception as e:
            logger.error(f"❌ Erreur email: {e}")
            return False

    def send_critical_alert(self, message: str, metadata: Dict[str, Any] = None):
        """Envoie une alerte critique sur tous les canaux"""
        return self.notify(
            message=f"🚨 ALERTE CRITIQUE: {message}",
            level="CRITICAL",
            force_whatsapp=True,
            force_email=True,
            metadata=metadata
        )

    def send_system_status(self, cpu_usage: float, memory_usage: float,
                          disk_usage: float, active_processes: int):
        """Envoie un rapport de statut système"""
        status_message = f"""
        📊 Statut Système JARVIS:
        • CPU: {cpu_usage:.1f}%
        • RAM: {memory_usage:.1f}%
        • Disque: {disk_usage:.1f}%
        • Processus: {active_processes}
        """

        level = "INFO"
        if cpu_usage > 90 or memory_usage > 90 or disk_usage > 95:
            level = "CRITICAL"
        elif cpu_usage > 80 or memory_usage > 80 or disk_usage > 85:
            level = "WARNING"

        return self.notify(status_message, level)

    def get_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques des notifications"""
        return {
            "stats": dict(self.stats),
            "recent_notifications": len(self.notification_history),
            "rate_limits": {
                "minute_remaining": self.config.max_notifications_per_minute - len(self.rate_limits["minute"]),
                "hour_remaining": self.config.max_notifications_per_hour - len(self.rate_limits["hour"])
            },
            "config_status": {
                "twilio_configured": bool(self.twilio_client),
                "email_configured": bool(self.config.smtp_server and self.config.email_user),
                "system_notifications": self.config.enable_system_notifications
            }
        }

    def test_all_channels(self) -> Dict[str, bool]:
        """Test tous les canaux de notification"""
        test_message = f"🧪 Test JARVIS Expert - {datetime.now().strftime('%H:%M:%S')}"

        results = {
            "console": True,  # Toujours disponible
            "file": False,
            "system": False,
            "whatsapp": False,
            "email": False
        }

        # Test fichier
        try:
            self._log_to_file(test_message, "TEST")
            results["file"] = True
        except:
            pass

        # Test système
        results["system"] = self._send_system_notification(test_message, "TEST")

        # Test WhatsApp
        results["whatsapp"] = self._send_whatsapp_notification(test_message)

        # Test email
        results["email"] = self._send_email_notification(test_message, "TEST")

        return results

# ============================================================================
# CONFIGURATION AUTOMATIQUE
# ============================================================================

def setup_notification_config():
    """Assistant de configuration des notifications"""
    print("🔧 CONFIGURATION NOTIFICATIONS JARVIS EXPERT")
    print("=" * 50)

    config = NotificationConfig()

    # Configuration Twilio
    print("\n📱 Configuration WhatsApp (Twilio):")
    config.twilio_sid = input("Twilio Account SID (optionnel): ").strip() or None
    if config.twilio_sid:
        config.twilio_token = input("Twilio Auth Token: ").strip() or None
        config.twilio_whatsapp_from = input("Numéro WhatsApp Twilio (+***********): ").strip() or "+***********"
        config.whatsapp_to = input("Votre numéro WhatsApp (+33XXXXXXXXX): ").strip() or None

    # Configuration Email
    print("\n📧 Configuration Email:")
    config.smtp_server = input("Serveur SMTP (ex: smtp.gmail.com): ").strip() or None
    if config.smtp_server:
        config.smtp_port = int(input("Port SMTP (587): ").strip() or "587")
        config.email_user = input("Email utilisateur: ").strip() or None
        config.email_password = input("Mot de passe email: ").strip() or None
        config.email_to = input("Email destinataire: ").strip() or None

    # Sauvegarde
    manager = NotificationManager()
    manager.config = config
    manager._save_config(config)

    print("\n✅ Configuration sauvegardée !")

    # Test
    test_results = manager.test_all_channels()
    print("\n🧪 Résultats des tests:")
    for channel, success in test_results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {channel.capitalize()}")

    return manager

# ============================================================================
# EXEMPLE D'UTILISATION
# ============================================================================

if __name__ == "__main__":
    print("🚀 GESTIONNAIRE DE NOTIFICATIONS JARVIS EXPERT NIVEAU 20")
    print("=" * 60)

    # Choix: configuration ou utilisation
    choice = input("\n1. Configurer les notifications\n2. Tester les notifications\nChoix (1/2): ").strip()

    if choice == "1":
        manager = setup_notification_config()
    else:
        manager = NotificationManager()

    # Tests de démonstration
    print("\n🧪 Tests de démonstration...")

    # Test notifications basiques
    manager.notify("JARVIS Expert démarré avec succès", "INFO")
    time.sleep(1)

    manager.notify("Mémoire thermique optimisée", "INFO", metadata={"module": "memory"})
    time.sleep(1)

    manager.notify("Charge CPU élevée détectée", "WARNING", metadata={"cpu": "85%"})
    time.sleep(1)

    # Test alerte critique
    manager.send_critical_alert("Erreur système critique détectée",
                               metadata={"error_code": "SYS_001", "component": "neural_engine"})

    # Test statut système
    manager.send_system_status(cpu_usage=45.2, memory_usage=67.8,
                              disk_usage=23.1, active_processes=156)

    # Affichage des statistiques
    stats = manager.get_statistics()
    print(f"\n📊 Statistiques: {stats}")

    print("\n🎉 Tests terminés ! Gestionnaire de notifications opérationnel.")
