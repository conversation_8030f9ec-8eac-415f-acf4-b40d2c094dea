#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 MÉMOIRE THERMIQUE JARVIS EXPERT NIVEAU 20 - VERSION CORRIGÉE
Système de mémoire thermique avancé avec corrections ChatGPT
Jean-Luc Passave - 23 Juin 2025
"""

import json
import os
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import deque, defaultdict
import threading
import sqlite3
import pickle
import numpy as np
from dataclasses import dataclass, asdict
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MemoryEntry:
    """Structure d'une entrée mémoire"""
    id: str
    content: Any
    timestamp: str
    tags: List[str]
    importance: float
    access_count: int
    last_access: str
    temperature: str  # hot, warm, cold
    semantic_vector: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None

class ThermalMemoryAdvanced:
    """
    🧠 MÉMOIRE THERMIQUE JARVIS EXPERT NIVEAU 20 - VERSION CORRIGÉE
    
    Fonctionnalités:
    - Zones thermiques (hot/warm/cold)
    - Indexation sémantique
    - Recherche vectorielle
    - Compression automatique
    - Sauvegarde incrémentale
    - Gestion d'erreurs robuste
    - Interface thread-safe
    """
    
    def __init__(self, memory_file: str = "jarvis_thermal_memory_v20.json", 
                 db_file: str = "jarvis_thermal_memory_v20.db"):
        self.memory_file = memory_file
        self.db_file = db_file
        self.max_entries = 100000
        self.compression_threshold = 50000
        
        # Structures de données corrigées
        self.memory_index: Dict[str, MemoryEntry] = {}
        self.thermal_zones = {
            "hot": deque(maxlen=1000),     # Mémoires très récentes/actives
            "warm": deque(maxlen=5000),    # Mémoires moyennement actives
            "cold": deque(maxlen=10000)    # Mémoires anciennes mais importantes
        }
        
        # Index sémantique
        self.semantic_index: Dict[str, List[str]] = defaultdict(list)
        self.keyword_index: Dict[str, List[str]] = defaultdict(list)
        
        # Statistiques
        self.stats = {
            "total_entries": 0,
            "total_accesses": 0,
            "last_cleanup": datetime.now().isoformat(),
            "memory_level": 20,
            "version": "2.0-corrected"
        }
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Initialisation
        self._init_database()
        self.load_memory()
        
        logger.info("🧠 Mémoire thermique JARVIS Expert Niveau 20 initialisée")
    
    def _init_database(self):
        """Initialise la base de données SQLite pour persistance"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS thermal_memory (
                        id TEXT PRIMARY KEY,
                        content TEXT,
                        timestamp TEXT,
                        tags TEXT,
                        importance REAL,
                        access_count INTEGER,
                        last_access TEXT,
                        temperature TEXT,
                        semantic_vector BLOB,
                        metadata TEXT
                    )
                """)
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_timestamp ON thermal_memory(timestamp)
                """)
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_temperature ON thermal_memory(temperature)
                """)
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_importance ON thermal_memory(importance)
                """)
                conn.commit()
        except Exception as e:
            logger.error(f"❌ Erreur initialisation DB: {e}")
    
    def add_memory(self, content: Any, tags: List[str] = None, 
                   importance: float = 0.5, temperature: str = "hot") -> str:
        """
        Ajoute une nouvelle entrée à la mémoire thermique
        
        Args:
            content: Contenu à mémoriser
            tags: Tags associés
            importance: Importance (0.0 à 1.0)
            temperature: Zone thermique (hot/warm/cold)
        
        Returns:
            ID de l'entrée créée
        """
        with self.lock:
            try:
                # Génération ID unique
                entry_id = self._generate_id(content)
                timestamp = datetime.now().isoformat()
                
                # Création de l'entrée
                entry = MemoryEntry(
                    id=entry_id,
                    content=content,
                    timestamp=timestamp,
                    tags=tags or [],
                    importance=importance,
                    access_count=0,
                    last_access=timestamp,
                    temperature=temperature,
                    metadata={"created_by": "jarvis_expert_v20"}
                )
                
                # Ajout aux structures
                self.memory_index[entry_id] = entry
                self.thermal_zones[temperature].append(entry_id)
                
                # Indexation sémantique
                self._update_semantic_index(entry_id, content, tags)
                
                # Sauvegarde
                self._save_to_db(entry)
                
                # Mise à jour statistiques
                self.stats["total_entries"] += 1
                
                # Nettoyage automatique si nécessaire
                if self.stats["total_entries"] > self.compression_threshold:
                    self._auto_cleanup()
                
                logger.info(f"💾 Mémoire ajoutée: {entry_id[:8]}... (zone: {temperature})")
                return entry_id
                
            except Exception as e:
                logger.error(f"❌ Erreur ajout mémoire: {e}")
                return None
    
    def get_memory(self, entry_id: str) -> Optional[MemoryEntry]:
        """Récupère une entrée mémoire par ID"""
        with self.lock:
            try:
                if entry_id in self.memory_index:
                    entry = self.memory_index[entry_id]
                    # Mise à jour accès
                    entry.access_count += 1
                    entry.last_access = datetime.now().isoformat()
                    self.stats["total_accesses"] += 1
                    
                    # Promotion thermique si très accédé
                    self._thermal_promotion(entry_id, entry)
                    
                    return entry
                return None
            except Exception as e:
                logger.error(f"❌ Erreur récupération mémoire: {e}")
                return None
    
    def search_memory(self, query: str, limit: int = 10, 
                     temperature_filter: str = None) -> List[MemoryEntry]:
        """
        Recherche dans la mémoire thermique
        
        Args:
            query: Requête de recherche
            limit: Nombre max de résultats
            temperature_filter: Filtrer par zone thermique
        
        Returns:
            Liste des entrées correspondantes
        """
        with self.lock:
            try:
                results = []
                query_lower = query.lower()
                
                # Recherche dans l'index sémantique
                matching_ids = set()
                
                # Recherche par mots-clés
                for keyword in query_lower.split():
                    if keyword in self.keyword_index:
                        matching_ids.update(self.keyword_index[keyword])
                
                # Recherche dans le contenu
                for entry_id, entry in self.memory_index.items():
                    if temperature_filter and entry.temperature != temperature_filter:
                        continue
                    
                    content_str = str(entry.content).lower()
                    if query_lower in content_str or entry_id in matching_ids:
                        # Score de pertinence
                        score = self._calculate_relevance_score(entry, query_lower)
                        results.append((score, entry))
                
                # Tri par pertinence et importance
                results.sort(key=lambda x: (x[0], x[1].importance), reverse=True)
                
                return [entry for _, entry in results[:limit]]
                
            except Exception as e:
                logger.error(f"❌ Erreur recherche mémoire: {e}")
                return []
    
    def get_recent_memories(self, count: int = 10, 
                           temperature: str = "hot") -> List[MemoryEntry]:
        """Récupère les mémoires récentes d'une zone thermique"""
        with self.lock:
            try:
                if temperature not in self.thermal_zones:
                    return []
                
                recent_ids = list(self.thermal_zones[temperature])[-count:]
                return [self.memory_index[id] for id in recent_ids 
                       if id in self.memory_index]
                
            except Exception as e:
                logger.error(f"❌ Erreur récupération mémoires récentes: {e}")
                return []
    
    def _generate_id(self, content: Any) -> str:
        """Génère un ID unique pour le contenu"""
        content_str = str(content) + str(time.time())
        return hashlib.sha256(content_str.encode()).hexdigest()[:16]
    
    def _update_semantic_index(self, entry_id: str, content: Any, tags: List[str]):
        """Met à jour l'index sémantique"""
        try:
            # Extraction mots-clés du contenu
            content_str = str(content).lower()
            words = content_str.split()
            
            # Indexation par mots-clés significatifs
            for word in words:
                if len(word) > 3:  # Ignorer mots trop courts
                    self.keyword_index[word].append(entry_id)
            
            # Indexation par tags
            for tag in tags:
                self.semantic_index[tag.lower()].append(entry_id)
                
        except Exception as e:
            logger.error(f"❌ Erreur indexation sémantique: {e}")
    
    def _thermal_promotion(self, entry_id: str, entry: MemoryEntry):
        """Promotion thermique basée sur l'accès"""
        try:
            current_temp = entry.temperature
            
            # Règles de promotion
            if entry.access_count > 10 and current_temp == "cold":
                self._move_to_zone(entry_id, "warm")
            elif entry.access_count > 5 and current_temp == "warm":
                self._move_to_zone(entry_id, "hot")
                
        except Exception as e:
            logger.error(f"❌ Erreur promotion thermique: {e}")
    
    def _move_to_zone(self, entry_id: str, new_temperature: str):
        """Déplace une entrée vers une nouvelle zone thermique"""
        try:
            entry = self.memory_index[entry_id]
            old_temperature = entry.temperature
            
            # Retirer de l'ancienne zone
            if entry_id in self.thermal_zones[old_temperature]:
                temp_list = list(self.thermal_zones[old_temperature])
                temp_list.remove(entry_id)
                self.thermal_zones[old_temperature] = deque(temp_list, 
                                                          maxlen=self.thermal_zones[old_temperature].maxlen)
            
            # Ajouter à la nouvelle zone
            self.thermal_zones[new_temperature].append(entry_id)
            entry.temperature = new_temperature
            
            logger.info(f"🌡️ Promotion thermique: {entry_id[:8]}... {old_temperature} → {new_temperature}")
            
        except Exception as e:
            logger.error(f"❌ Erreur déplacement zone: {e}")
    
    def _calculate_relevance_score(self, entry: MemoryEntry, query: str) -> float:
        """Calcule le score de pertinence d'une entrée"""
        try:
            score = 0.0
            content_str = str(entry.content).lower()
            
            # Score basé sur la correspondance exacte
            if query in content_str:
                score += 1.0
            
            # Score basé sur les mots communs
            query_words = set(query.split())
            content_words = set(content_str.split())
            common_words = query_words.intersection(content_words)
            score += len(common_words) * 0.1
            
            # Bonus pour importance et accès récents
            score += entry.importance * 0.5
            score += min(entry.access_count * 0.01, 0.2)
            
            return score
            
        except Exception as e:
            logger.error(f"❌ Erreur calcul pertinence: {e}")
            return 0.0

    def _save_to_db(self, entry: MemoryEntry):
        """Sauvegarde une entrée dans la base de données"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO thermal_memory
                    (id, content, timestamp, tags, importance, access_count,
                     last_access, temperature, semantic_vector, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    entry.id,
                    json.dumps(entry.content, ensure_ascii=False),
                    entry.timestamp,
                    json.dumps(entry.tags),
                    entry.importance,
                    entry.access_count,
                    entry.last_access,
                    entry.temperature,
                    pickle.dumps(entry.semantic_vector) if entry.semantic_vector else None,
                    json.dumps(entry.metadata) if entry.metadata else None
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde DB: {e}")

    def load_memory(self):
        """Charge la mémoire depuis la base de données"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.execute("SELECT * FROM thermal_memory")
                rows = cursor.fetchall()

                for row in rows:
                    entry = MemoryEntry(
                        id=row[0],
                        content=json.loads(row[1]),
                        timestamp=row[2],
                        tags=json.loads(row[3]),
                        importance=row[4],
                        access_count=row[5],
                        last_access=row[6],
                        temperature=row[7],
                        semantic_vector=pickle.loads(row[8]) if row[8] else None,
                        metadata=json.loads(row[9]) if row[9] else None
                    )

                    self.memory_index[entry.id] = entry
                    self.thermal_zones[entry.temperature].append(entry.id)
                    self._update_semantic_index(entry.id, entry.content, entry.tags)

                self.stats["total_entries"] = len(self.memory_index)
                logger.info(f"💾 Mémoire chargée: {self.stats['total_entries']} entrées")

        except Exception as e:
            logger.error(f"❌ Erreur chargement mémoire: {e}")

    def _auto_cleanup(self):
        """Nettoyage automatique de la mémoire"""
        try:
            logger.info("🧹 Démarrage nettoyage automatique...")

            # Déplacer les anciennes entrées vers cold
            cutoff_date = datetime.now() - timedelta(days=30)

            for entry_id, entry in list(self.memory_index.items()):
                entry_date = datetime.fromisoformat(entry.timestamp)

                if entry_date < cutoff_date and entry.temperature == "hot":
                    if entry.access_count < 2:
                        self._move_to_zone(entry_id, "cold")
                    else:
                        self._move_to_zone(entry_id, "warm")

            # Supprimer les entrées très anciennes et peu importantes
            very_old_date = datetime.now() - timedelta(days=90)
            to_remove = []

            for entry_id, entry in self.memory_index.items():
                entry_date = datetime.fromisoformat(entry.timestamp)
                if (entry_date < very_old_date and
                    entry.importance < 0.3 and
                    entry.access_count < 1):
                    to_remove.append(entry_id)

            # Suppression effective
            for entry_id in to_remove:
                self._remove_entry(entry_id)

            self.stats["last_cleanup"] = datetime.now().isoformat()
            logger.info(f"🧹 Nettoyage terminé: {len(to_remove)} entrées supprimées")

        except Exception as e:
            logger.error(f"❌ Erreur nettoyage: {e}")

    def _remove_entry(self, entry_id: str):
        """Supprime une entrée de toutes les structures"""
        try:
            if entry_id in self.memory_index:
                entry = self.memory_index[entry_id]

                # Retirer des zones thermiques
                for zone in self.thermal_zones.values():
                    if entry_id in zone:
                        temp_list = list(zone)
                        temp_list.remove(entry_id)
                        zone.clear()
                        zone.extend(temp_list)

                # Retirer des index
                for keyword_list in self.keyword_index.values():
                    if entry_id in keyword_list:
                        keyword_list.remove(entry_id)

                for semantic_list in self.semantic_index.values():
                    if entry_id in semantic_list:
                        semantic_list.remove(entry_id)

                # Supprimer de la DB
                with sqlite3.connect(self.db_file) as conn:
                    conn.execute("DELETE FROM thermal_memory WHERE id = ?", (entry_id,))
                    conn.commit()

                # Supprimer de l'index principal
                del self.memory_index[entry_id]
                self.stats["total_entries"] -= 1

        except Exception as e:
            logger.error(f"❌ Erreur suppression entrée: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques de la mémoire"""
        with self.lock:
            zone_stats = {}
            for zone_name, zone in self.thermal_zones.items():
                zone_stats[zone_name] = len(zone)

            return {
                **self.stats,
                "zones": zone_stats,
                "index_keywords": len(self.keyword_index),
                "index_semantic": len(self.semantic_index),
                "memory_usage_mb": self._estimate_memory_usage()
            }

    def _estimate_memory_usage(self) -> float:
        """Estime l'usage mémoire en MB"""
        try:
            import sys
            total_size = 0
            total_size += sys.getsizeof(self.memory_index)
            total_size += sys.getsizeof(self.thermal_zones)
            total_size += sys.getsizeof(self.keyword_index)
            total_size += sys.getsizeof(self.semantic_index)
            return total_size / (1024 * 1024)  # Conversion en MB
        except:
            return 0.0

    def export_memory(self, export_file: str = None) -> str:
        """Exporte la mémoire vers un fichier JSON"""
        if not export_file:
            export_file = f"jarvis_memory_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            export_data = {
                "metadata": {
                    "export_date": datetime.now().isoformat(),
                    "version": "2.0-corrected",
                    "total_entries": len(self.memory_index)
                },
                "statistics": self.get_statistics(),
                "entries": [asdict(entry) for entry in self.memory_index.values()]
            }

            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            logger.info(f"📤 Mémoire exportée: {export_file}")
            return export_file

        except Exception as e:
            logger.error(f"❌ Erreur export: {e}")
            return None

# ============================================================================
# INTERFACE DE COMPATIBILITÉ
# ============================================================================

class ThermalMemory:
    """Interface de compatibilité pour l'ancien code"""

    def __init__(self):
        self.advanced_memory = ThermalMemoryAdvanced()
        self.memory = self  # Pour éviter l'erreur "not subscriptable"
        self.episodes = []

    def __getitem__(self, key):
        """Permet l'accès par crochets pour compatibilité"""
        if key == "memory":
            return self.advanced_memory.memory_index
        return None

    def values(self):
        """Retourne les valeurs pour compatibilité"""
        return list(self.advanced_memory.memory_index.values())

    def add_conversation(self, user_message: str, agent_response: str, agent_type: str = "agent1"):
        """Ajoute une conversation"""
        content = {
            "user_message": user_message,
            "agent_response": agent_response,
            "agent_type": agent_type
        }
        return self.advanced_memory.add_memory(
            content=content,
            tags=["conversation", agent_type],
            importance=0.7,
            temperature="hot"
        )

    def search_memory(self, query: str, limit: int = 5):
        """Recherche dans la mémoire"""
        return self.advanced_memory.search_memory(query, limit)

    def get_recent_memories(self, count: int = 10):
        """Récupère les mémoires récentes"""
        return self.advanced_memory.get_recent_memories(count)

# ============================================================================
# INSTANCE GLOBALE CORRIGÉE
# ============================================================================

# Instance globale corrigée
thermal_memory_corrected = ThermalMemory()

if __name__ == "__main__":
    print("🧠 Test de la mémoire thermique corrigée...")

    # Test d'ajout
    memory_id = thermal_memory_corrected.add_conversation(
        "Bonjour JARVIS",
        "Bonjour Jean-Luc ! Comment puis-je vous aider aujourd'hui ?"
    )
    print(f"✅ Mémoire ajoutée: {memory_id}")

    # Test de recherche
    results = thermal_memory_corrected.search_memory("bonjour")
    print(f"✅ Recherche: {len(results)} résultats")

    # Test statistiques
    stats = thermal_memory_corrected.advanced_memory.get_statistics()
    print(f"✅ Statistiques: {stats}")

    print("🎉 Tests réussis ! Mémoire thermique corrigée opérationnelle.")
