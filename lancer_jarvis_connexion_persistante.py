#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 LANCEUR JARVIS AVEC CONNEXION PERSISTANTE - JEAN-LUC PASSAVE
Lance JARVIS avec connexion qui ne se déconnecte jamais
"""

import subprocess
import sys
import time
import signal
import os
from datetime import datetime

def lancer_connexion_persistante_background():
    """LANCER CONNEXION PERSISTANTE EN ARRIÈRE-PLAN"""
    try:
        print("🔧 Démarrage connexion persistante JARVIS...")
        
        # Lancer connexion persistante en arrière-plan
        connexion_process = subprocess.Popen([
            sys.executable, "correction_connexion_persistante_jarvis.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre un peu pour s'assurer que la connexion démarre
        time.sleep(5)
        
        # Vérifier si le processus est toujours actif
        if connexion_process.poll() is None:
            print("✅ Connexion persistante démarrée (PID: {})".format(connexion_process.pid))
            print("🔧 JARVIS ne se déconnectera plus même si vous laissez l'interface ouverte")
            return connexion_process
        else:
            print("❌ Erreur démarrage connexion persistante")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lancement connexion persistante: {e}")
        return None

def lancer_surveillance_background():
    """LANCER SURVEILLANCE EN ARRIÈRE-PLAN"""
    try:
        print("🕐 Démarrage surveillance activité JARVIS...")
        
        # Lancer surveillance en arrière-plan
        surveillance_process = subprocess.Popen([
            sys.executable, "surveillance_jarvis_activite.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre un peu
        time.sleep(3)
        
        # Vérifier si le processus est toujours actif
        if surveillance_process.poll() is None:
            print("✅ Surveillance activité démarrée (PID: {})".format(surveillance_process.pid))
            return surveillance_process
        else:
            print("❌ Erreur démarrage surveillance")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lancement surveillance: {e}")
        return None

def lancer_visualiseur_background():
    """LANCER VISUALISEUR EN ARRIÈRE-PLAN"""
    try:
        print("📊 Démarrage visualiseur activité JARVIS...")
        
        # Lancer visualiseur en arrière-plan
        visualiseur_process = subprocess.Popen([
            sys.executable, "visualiseur_activite_jarvis.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre un peu
        time.sleep(3)
        
        # Vérifier si le processus est toujours actif
        if visualiseur_process.poll() is None:
            print("✅ Visualiseur activité démarré (PID: {})".format(visualiseur_process.pid))
            print("🌐 Interface surveillance: http://localhost:8250")
            return visualiseur_process
        else:
            print("❌ Erreur démarrage visualiseur")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lancement visualiseur: {e}")
        return None

def lancer_jarvis_principal():
    """LANCER JARVIS PRINCIPAL"""
    try:
        print("🤖 Démarrage JARVIS principal...")
        
        # Lancer JARVIS principal
        jarvis_process = subprocess.Popen([
            sys.executable, "jarvis_architecture_multi_fenetres.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre un peu
        time.sleep(5)
        
        # Vérifier si le processus est toujours actif
        if jarvis_process.poll() is None:
            print("✅ JARVIS principal démarré (PID: {})".format(jarvis_process.pid))
            print("🌐 Interface principale: http://localhost:8100")
            print("🌐 Dashboard: http://localhost:8101")
            print("🌐 Mémoire thermique: http://localhost:8102")
            print("🌐 Créativité: http://localhost:8103")
            return jarvis_process
        else:
            print("❌ Erreur démarrage JARVIS")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lancement JARVIS: {e}")
        return None

def arreter_processus_proprement(processus_list):
    """ARRÊTER TOUS LES PROCESSUS PROPREMENT"""
    print("\n🛑 ARRÊT EN COURS...")
    
    for nom, process in processus_list:
        if process and process.poll() is None:
            try:
                print(f"⏹️ Arrêt {nom}...")
                process.terminate()
                
                # Attendre un peu pour arrêt propre
                try:
                    process.wait(timeout=5)
                    print(f"✅ {nom} arrêté proprement")
                except subprocess.TimeoutExpired:
                    print(f"🔥 Forçage arrêt {nom}...")
                    process.kill()
                    process.wait()
                    print(f"✅ {nom} forcé à s'arrêter")
                    
            except Exception as e:
                print(f"❌ Erreur arrêt {nom}: {e}")

def signal_handler(signum, frame):
    """GESTIONNAIRE DE SIGNAL POUR ARRÊT PROPRE"""
    print(f"\n🚨 Signal reçu: {signum}")

def main():
    """FONCTION PRINCIPALE"""
    print("🔧 LANCEUR JARVIS AVEC CONNEXION PERSISTANTE")
    print("=" * 60)
    print(f"📅 Démarrage: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("👤 Utilisateur: Jean-Luc Passave")
    print("🎯 CORRECTION: JARVIS ne se déconnectera plus !")
    print("=" * 60)
    
    # Installer gestionnaire de signal
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    processus_actifs = []
    
    try:
        # 1. Lancer connexion persistante (PRIORITÉ)
        connexion_proc = lancer_connexion_persistante_background()
        if connexion_proc:
            processus_actifs.append(("Connexion Persistante", connexion_proc))
        
        # 2. Lancer surveillance
        surveillance_proc = lancer_surveillance_background()
        if surveillance_proc:
            processus_actifs.append(("Surveillance", surveillance_proc))
        
        # 3. Lancer visualiseur
        visualiseur_proc = lancer_visualiseur_background()
        if visualiseur_proc:
            processus_actifs.append(("Visualiseur", visualiseur_proc))
        
        # 4. Lancer JARVIS principal
        jarvis_proc = lancer_jarvis_principal()
        if jarvis_proc:
            processus_actifs.append(("JARVIS Principal", jarvis_proc))
        
        if not processus_actifs:
            print("❌ AUCUN PROCESSUS DÉMARRÉ - ARRÊT")
            return
        
        print("\n" + "=" * 60)
        print("🎉 JARVIS AVEC CONNEXION PERSISTANTE DÉMARRÉ !")
        print("=" * 60)
        print("🔧 CORRECTION APPLIQUÉE:")
        print("   ✅ Connexion persistante active")
        print("   ✅ Keep-alive automatique toutes les 30s")
        print("   ✅ Reconnexion automatique en cas de problème")
        print("   ✅ JARVIS ne se déconnectera plus !")
        print("=" * 60)
        print("🌐 INTERFACES DISPONIBLES:")
        print("   📊 Surveillance activité: http://localhost:8250")
        print("   💬 Communication JARVIS: http://localhost:8100")
        print("   🏠 Dashboard principal: http://localhost:8101")
        print("   🧠 Mémoire thermique: http://localhost:8102")
        print("   🎨 Créativité: http://localhost:8103")
        print("=" * 60)
        print("💡 Utilisez Ctrl+C pour arrêter proprement")
        print("🔧 PROBLÈME DE DÉCONNEXION RÉSOLU !")
        print("=" * 60)
        
        # Boucle de surveillance des processus
        while True:
            time.sleep(10)  # Vérifier toutes les 10 secondes
            
            # Vérifier si tous les processus sont encore actifs
            processus_morts = []
            for nom, process in processus_actifs:
                if process.poll() is not None:
                    processus_morts.append((nom, process))
                    print(f"⚠️ {nom} s'est arrêté inopinément")
            
            # Retirer les processus morts de la liste
            for processus_mort in processus_morts:
                processus_actifs.remove(processus_mort)
            
            # Si JARVIS principal est mort, arrêter tout
            if any(nom == "JARVIS Principal" for nom, _ in processus_morts):
                print("🚨 JARVIS PRINCIPAL ARRÊTÉ - ARRÊT COMPLET")
                break
            
            # Si connexion persistante est morte, la relancer
            if any(nom == "Connexion Persistante" for nom, _ in processus_morts):
                print("🔄 RELANCEMENT CONNEXION PERSISTANTE...")
                connexion_proc = lancer_connexion_persistante_background()
                if connexion_proc:
                    processus_actifs.append(("Connexion Persistante", connexion_proc))
    
    except KeyboardInterrupt:
        print("\n🛑 ARRÊT DEMANDÉ PAR L'UTILISATEUR")
    
    except Exception as e:
        print(f"\n❌ ERREUR CRITIQUE: {e}")
    
    finally:
        # Arrêter tous les processus proprement
        arreter_processus_proprement(processus_actifs)
        
        print("\n" + "=" * 60)
        print("✅ JARVIS AVEC CONNEXION PERSISTANTE ARRÊTÉ PROPREMENT")
        print(f"📅 Arrêt: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("👤 Au revoir Jean-Luc Passave !")
        print("🔧 La prochaine fois, JARVIS restera connecté !")
        print("=" * 60)

if __name__ == "__main__":
    main()
