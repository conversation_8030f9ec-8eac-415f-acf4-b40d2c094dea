[{"test": "Amélioration mémoire thermique", "prompt_length": 807, "response_length": 102, "timestamp": "2025-06-23T09:08:28.742898", "response": "❌ Erreur connexion: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)"}, {"test": "Notifications WhatsApp", "prompt_length": 991, "response_length": 102, "timestamp": "2025-06-23T09:09:00.752727", "response": "❌ Erreur connexion: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)"}, {"test": "Architecture avancée", "prompt_length": 970, "response_length": 102, "timestamp": "2025-06-23T09:09:32.768612", "response": "❌ Erreur connexion: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)"}, {"test": "Algorithmes d'optimisation", "prompt_length": 1051, "response_length": 102, "timestamp": "2025-06-23T09:10:04.776445", "response": "❌ Erreur connexion: HTTPConnectionPool(host='localhost', port=8000): Read timed out. (read timeout=30)"}]