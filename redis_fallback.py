
# 🔧 FALLBACK REDIS - JEAN-LUC PASSAVE
"""
Fallback pour Redis quand le module n'est pas disponible
Utilise un système de cache en mémoire et fichiers JSON
"""

import json
import os
import time
from datetime import datetime, timedelta
import threading

class RedisFallback:
    """Fallback Redis utilisant fichiers JSON et cache mémoire"""
    
    def __init__(self, db_file="redis_fallback.json"):
        self.db_file = db_file
        self.cache = {}
        self.lock = threading.Lock()
        self.load_from_file()
    
    def load_from_file(self):
        """Charger les données depuis le fichier"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.cache = data.get('cache', {})
        except Exception as e:
            print(f"⚠️ Erreur chargement Redis fallback: {e}")
            self.cache = {}
    
    def save_to_file(self):
        """Sauvegarder les données dans le fichier"""
        try:
            with self.lock:
                data = {
                    'cache': self.cache,
                    'timestamp': datetime.now().isoformat()
                }
                with open(self.db_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde Redis fallback: {e}")
    
    def set(self, key, value, ex=None):
        """Définir une valeur avec expiration optionnelle"""
        with self.lock:
            entry = {
                'value': value,
                'timestamp': time.time()
            }
            if ex:
                entry['expires'] = time.time() + ex
            
            self.cache[key] = entry
            self.save_to_file()
    
    def get(self, key):
        """Obtenir une valeur"""
        with self.lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            
            # Vérifier expiration
            if 'expires' in entry and time.time() > entry['expires']:
                del self.cache[key]
                self.save_to_file()
                return None
            
            return entry['value']
    
    def delete(self, key):
        """Supprimer une clé"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                self.save_to_file()
                return True
            return False
    
    def exists(self, key):
        """Vérifier si une clé existe"""
        return self.get(key) is not None
    
    def keys(self, pattern="*"):
        """Lister les clés (pattern simple)"""
        with self.lock:
            if pattern == "*":
                return list(self.cache.keys())
            else:
                # Pattern simple avec *
                import fnmatch
                return [k for k in self.cache.keys() if fnmatch.fnmatch(k, pattern)]
    
    def flushall(self):
        """Vider tout le cache"""
        with self.lock:
            self.cache = {}
            self.save_to_file()

# Instance globale
redis_fallback = RedisFallback()

# Compatibilité avec l'API Redis
class Redis:
    def __init__(self, *args, **kwargs):
        self.fallback = redis_fallback
    
    def set(self, key, value, ex=None):
        return self.fallback.set(key, value, ex)
    
    def get(self, key):
        return self.fallback.get(key)
    
    def delete(self, key):
        return self.fallback.delete(key)
    
    def exists(self, key):
        return self.fallback.exists(key)
    
    def keys(self, pattern="*"):
        return self.fallback.keys(pattern)
    
    def flushall(self):
        return self.fallback.flushall()

def from_url(url, **kwargs):
    """Compatibilité Redis.from_url"""
    return Redis()

print("✅ Redis Fallback initialisé - Utilisation de fichiers JSON")
