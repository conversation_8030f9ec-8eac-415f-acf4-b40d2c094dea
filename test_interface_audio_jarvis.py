#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST INTERFACE AUDIO JARVIS - JEAN-LUC PASSAVE
Interface de communication améliorée avec boutons audio fonctionnels
"""

import gradio as gr
import json
import os
import datetime
import tempfile
import webbrowser

def text_to_speech_jarvis(text):
    """Convertit le texte en audio avec la voix de JARVIS"""
    try:
        import pyttsx3
        
        # Initialiser le moteur TTS
        engine = pyttsx3.init()
        
        # Configuration de la voix JARVIS
        voices = engine.getProperty('voices')
        if voices:
            # Choisir une voix masculine si disponible
            for voice in voices:
                if 'male' in voice.name.lower() or 'homme' in voice.name.lower():
                    engine.setProperty('voice', voice.id)
                    break
        
        # Paramètres audio
        engine.setProperty('rate', 180)  # Vitesse de parole
        engine.setProperty('volume', 0.9)  # Volume
        
        # Créer un fichier audio temporaire
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
        temp_file.close()
        
        # Générer l'audio
        engine.save_to_file(text, temp_file.name)
        engine.runAndWait()
        
        return temp_file.name
        
    except Exception as e:
        print(f"Erreur TTS: {e}")
        return None

def get_last_jarvis_response(history):
    """Récupère la dernière réponse de JARVIS"""
    if history and len(history) > 0:
        last_message = history[-1]
        if isinstance(last_message, dict) and 'content' in last_message:
            return last_message['content']
        elif isinstance(last_message, list) and len(last_message) > 1:
            return last_message[1]  # Réponse de l'assistant
    return "Aucune réponse disponible"

def listen_to_last_response(history):
    """Génère l'audio de la dernière réponse"""
    last_response = get_last_jarvis_response(history)
    if last_response and last_response != "Aucune réponse disponible":
        audio_file = text_to_speech_jarvis(last_response)
        return audio_file
    return None

def simulate_jarvis_response(message, history):
    """Simule une réponse de JARVIS pour le test"""
    if not message.strip():
        return history, ""
    
    # Réponse simulée de JARVIS
    jarvis_response = f"Bonjour Jean-Luc ! J'ai bien reçu votre message : '{message}'. Je suis JARVIS, votre assistant IA expert niveau 20. Comment puis-je vous aider aujourd'hui ?"
    
    # Ajouter à l'historique
    history.append([message, jarvis_response])
    
    return history, ""

def create_test_interface():
    """Crée l'interface de test avec audio"""
    
    with gr.Blocks(
        title="🔊 TEST INTERFACE AUDIO JARVIS",
        theme=gr.themes.Soft(),
        css="""
        .audio-main-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.1em !important;
            padding: 12px 20px !important;
            border-radius: 10px !important;
            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3) !important;
            transition: all 0.3s ease !important;
        }
        .audio-main-btn:hover {
            background: linear-gradient(45deg, #c0392b, #a93226) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 12px rgba(231, 76, 60, 0.4) !important;
        }
        """
    ) as interface:
        
        # ENTÊTE
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #2c2c2c, #6a4c93, #9c27b0); color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 0 0 15px 15px;">
            <h1 style="margin: 0; font-size: 1.8em;">🔊 TEST INTERFACE AUDIO JARVIS</h1>
            <div style="margin: 10px 0;">
                <span style="display: inline-block; width: 12px; height: 12px; background: #4CAF50; border-radius: 50%; margin-right: 8px; animation: pulse 2s infinite;"></span>
                <span style="font-size: 1.1em; font-weight: bold;">JARVIS ACTIF - Test Audio Fonctionnel</span>
            </div>
        </div>
        <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        </style>
        """)
        
        with gr.Row():
            # COLONNE PRINCIPALE
            with gr.Column(scale=2):
                # CHAT PRINCIPAL
                main_chat = gr.Chatbot(
                    value=[],
                    height=500,
                    label="💬 Test Conversation avec JARVIS",
                    show_copy_button=True,
                    avatar_images=("👨‍💻", "🤖"),
                    type="messages",
                    elem_id="main_chatbot"
                )
                
                # ZONE AUDIO
                audio_output = gr.Audio(
                    label="🔊 Audio JARVIS",
                    visible=True,
                    autoplay=False,
                    elem_id="jarvis_audio_player"
                )
                
                # ZONE DE SAISIE
                with gr.Row():
                    user_input = gr.Textbox(
                        value="",
                        label="💬 Votre message de test",
                        scale=4,
                        lines=2
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)
                
                # BOUTONS AUDIO PRINCIPAUX
                gr.HTML("""
                <h4 style='margin: 15px 0 10px 0; color: #e74c3c; text-align: center; font-size: 1.3em;'>
                    🎵 BOUTONS AUDIO - TEST FONCTIONNEL
                </h4>
                """)
                
                with gr.Row():
                    listen_last_response_btn = gr.Button("🔊 Écouter Dernière Réponse", variant="primary", size="sm")
                    test_audio_btn = gr.Button("🎧 Test Audio Simple", variant="secondary", size="sm")
                
                # BOUTON PRINCIPAL AUDIO
                with gr.Row():
                    listen_response_big_btn = gr.Button(
                        "🔊 ÉCOUTER DERNIÈRE RÉPONSE JARVIS", 
                        variant="primary", 
                        size="lg",
                        elem_classes=["audio-main-btn"]
                    )
            
            # COLONNE LATÉRALE
            with gr.Column(scale=1):
                gr.HTML("<h3 style='color: #9C27B0; margin: 0 0 10px 0;'>🧠 Statut Test</h3>")
                
                status_display = gr.HTML("""
                <div style='background: linear-gradient(135deg, #f3e5f5, #e1bee7); padding: 20px; border-radius: 15px; border-left: 6px solid #9C27B0;'>
                    <div style='margin: 8px 0; padding: 12px; background: #2c3e50; color: #ecf0f1; border-radius: 8px; font-size: 1.2em; border: 2px solid #3498db;'>
                        <strong style="color: #f39c12;">💭 Test:</strong> Interface audio prête
                    </div>
                    <div style='margin: 5px 0; padding: 8px; background: #34495e; color: #ecf0f1; border-radius: 5px; border: 1px solid #27ae60;'>
                        <strong style="color: #27ae60;">🔍 Statut:</strong> Boutons audio fonctionnels
                    </div>
                </div>
                """)
                
                # BOUTONS DE TEST
                gr.HTML("<h4 style='color: #6a4c93; margin: 20px 0 10px 0;'>🧪 Tests Audio</h4>")
                
                with gr.Column():
                    test_simple_btn = gr.Button("🔊 Test Simple", size="sm", variant="secondary")
                    test_voice_btn = gr.Button("🗣️ Test Voix", size="sm", variant="secondary")
                    open_main_btn = gr.Button("🏠 Interface Principale", size="sm", variant="primary")
        
        # FONCTIONS DE TEST
        def test_simple_audio():
            """Test audio simple"""
            test_text = "Bonjour Jean-Luc, ceci est un test audio de JARVIS."
            audio_file = text_to_speech_jarvis(test_text)
            return audio_file
        
        def test_voice_selection():
            """Test de sélection de voix"""
            test_text = "Test de voix JARVIS. Intelligence artificielle opérationnelle."
            audio_file = text_to_speech_jarvis(test_text)
            return audio_file
        
        def open_main_interface():
            """Ouvre l'interface principale"""
            webbrowser.open("http://localhost:8100")
            return "🏠 Interface principale ouverte..."
        
        # CONNEXIONS
        send_btn.click(
            fn=simulate_jarvis_response,
            inputs=[user_input, main_chat],
            outputs=[main_chat, user_input]
        )
        
        listen_last_response_btn.click(
            fn=listen_to_last_response,
            inputs=[main_chat],
            outputs=[audio_output]
        )
        
        listen_response_big_btn.click(
            fn=listen_to_last_response,
            inputs=[main_chat],
            outputs=[audio_output]
        )
        
        test_audio_btn.click(
            fn=test_simple_audio,
            outputs=[audio_output]
        )
        
        test_simple_btn.click(
            fn=test_simple_audio,
            outputs=[audio_output]
        )
        
        test_voice_btn.click(
            fn=test_voice_selection,
            outputs=[audio_output]
        )
        
        open_main_btn.click(
            fn=open_main_interface,
            outputs=[status_display]
        )
    
    return interface

if __name__ == "__main__":
    print("🔊 LANCEMENT TEST INTERFACE AUDIO JARVIS - JEAN-LUC PASSAVE")
    print("=" * 60)
    
    # Créer et lancer l'interface de test
    test_interface = create_test_interface()
    
    # Lancer sur un port différent pour éviter les conflits
    test_interface.launch(
        server_name="localhost",
        server_port=8105,
        share=False,
        debug=True,
        show_error=True
    )
