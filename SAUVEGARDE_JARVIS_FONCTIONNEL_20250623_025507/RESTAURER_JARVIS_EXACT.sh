#!/bin/bash

# 🔄 RESTAURATION EXACTE JARVIS FONCTIONNEL
# <PERSON><PERSON><PERSON> - Sauvegarde du 23 juin 2025 02:55:07
# Cette version FONCTIONNE avec mémoire thermique complète

echo "🔄 RESTAURATION EXACTE JARVIS FONCTIONNEL"
echo "========================================"
echo "📅 Sauvegarde: 23 juin 2025 02:55:07"
echo "✅ Version qui FONCTIONNE avec 86 milliards neurones"
echo ""

# Aller dans le répertoire parent
cd "$(dirname "$0")/.."

echo "📁 Répertoire de travail: $(pwd)"
echo ""

# Arrêter les processus existants
echo "🛑 Arrêt des processus existants..."
pkill -f "jarvis_architecture_multi_fenetres.py" 2>/dev/null || true
pkill -f "jarvis_electron_force.js" 2>/dev/null || true
pkill -f "electron.*jarvis" 2>/dev/null || true
sleep 3

# Copier les fichiers EXACTS qui fonctionnent
echo "📋 Restauration des fichiers EXACTS..."

if [ -f "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/jarvis_architecture_multi_fenetres.py" ]; then
    cp "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/jarvis_architecture_multi_fenetres.py" .
    echo "✅ Interface principale restaurée"
else
    echo "❌ Interface principale manquante"
    exit 1
fi

if [ -f "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/jarvis_electron_force.js" ]; then
    cp "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/jarvis_electron_force.js" .
    echo "✅ Application Electron restaurée"
else
    echo "❌ Application Electron manquante"
    exit 1
fi

if [ -f "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/JARVIS_ELECTRON_LAUNCHER.command" ]; then
    cp "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/JARVIS_ELECTRON_LAUNCHER.command" .
    chmod +x JARVIS_ELECTRON_LAUNCHER.command
    echo "✅ Raccourci de lancement restauré"
else
    echo "❌ Raccourci de lancement manquant"
    exit 1
fi

if [ -f "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/package.json" ]; then
    cp "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/package.json" .
    echo "✅ Configuration Node.js restaurée"
fi

if [ -f "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/thermal_memory_persistent.json" ]; then
    cp "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/thermal_memory_persistent.json" .
    echo "✅ Mémoire thermique restaurée"
fi

# Modules de support
if [ -f "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/jarvis_nouvelles_fenetres_simple.py" ]; then
    cp "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/jarvis_nouvelles_fenetres_simple.py" .
    echo "✅ Module fenêtres restauré"
fi

if [ -f "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/memoire_thermique_turbo_adaptatif.py" ]; then
    cp "SAUVEGARDE_JARVIS_FONCTIONNEL_20250623_025507/memoire_thermique_turbo_adaptatif.py" .
    echo "✅ Module mémoire thermique restauré"
fi

echo ""
echo "🎉 RESTAURATION EXACTE TERMINÉE !"
echo "================================"
echo ""
echo "🚀 POUR LANCER JARVIS:"
echo "   ./JARVIS_ELECTRON_LAUNCHER.command"
echo ""
echo "✅ FONCTIONNALITÉS RESTAURÉES:"
echo "   🧠 86 milliards de neurones"
echo "   🔥 Mémoire thermique complète"
echo "   🍎 Optimisations Apple Silicon M4"
echo "   🎨 Flux de conscience thermique"
echo "   🖥️ Application Electron native"
echo "   📊 Interface multi-fenêtres"
echo ""
echo "📝 Cette version a été testée et FONCTIONNE"
echo "💾 Sauvegarde: 23 juin 2025 02:55:07"

read -p "Appuyez sur Entrée pour fermer..."
