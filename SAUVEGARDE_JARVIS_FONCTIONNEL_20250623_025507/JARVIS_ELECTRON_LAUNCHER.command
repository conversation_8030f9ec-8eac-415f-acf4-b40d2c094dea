#!/bin/bash

# 🚀 LANCEUR ELECTRON JARVIS VALIDÉ - JEAN-LUC PASSAVE
# Double-clic pour démarrer l'application validée sur port 7864

echo "🤖 JARVIS ELECTRON LAUNCHER VALIDÉ"
echo "=================================="

# Aller dans le bon répertoire
cd "$(dirname "$0")"

echo "📁 Répertoire: $(pwd)"

# Nettoyer les anciens processus
echo "🧹 Nettoyage des anciens processus..."
pkill -f "jarvis_interface_communication_principale.py" 2>/dev/null || true
pkill -f "jarvis_architecture_multi_fenetres.py" 2>/dev/null || true
pkill -f "electron.*jarvis" 2>/dev/null || true
pkill -f "port.*7864" 2>/dev/null || true
sleep 3

# Vérifier les fichiers
if [ -f "jarvis_electron_nouveau.js" ]; then
    echo "✅ Application Electron validée trouvée"
else
    echo "❌ Application Electron manquante"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ -f "jarvis_architecture_multi_fenetres.py" ]; then
    echo "✅ Interface JARVIS COMPLÈTE (port 7867) trouvée"
else
    echo "❌ Interface COMPLÈTE manquante"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

# Vérifier Electron
if [ -f "node_modules/.bin/electron" ]; then
    echo "✅ Electron installé"
else
    echo "🔧 Installation d'Electron..."
    npm install
fi

echo ""
echo "🚀 DÉMARRAGE JARVIS COMPLET (PORT 7867)..."
echo "🧠 Interface validée Jean-Luc Passave"

# Démarrer l'interface VRAIE JARVIS COMPLÈTE sur port 7867 avec le bon environnement
source venv_gradio/bin/activate && python3 jarvis_architecture_multi_fenetres.py &
JARVIS_PID=$!
echo "✅ JARVIS COMPLET démarré (PID: $JARVIS_PID) sur port 7867"

# Attendre que l'interface soit prête
echo "⏳ Attente du démarrage complet de l'interface..."
sleep 8

# Vérifier que le port 7867 est actif
echo "🔍 Vérification du port 7867..."
if curl -s http://localhost:7867 > /dev/null; then
    echo "✅ Interface JARVIS accessible sur port 7867"
else
    echo "⚠️ Interface en cours de démarrage..."
    sleep 5
fi

echo ""
echo "🖥️ DÉMARRAGE APPLICATION ELECTRON VALIDÉE..."
echo "🎯 Interface Dashboard violette validée"
echo "🧠 QI 648 + 100M neurones actifs"
echo ""

# Démarrer l'application Electron validée
npm run force

echo ""
echo "🔄 Application fermée"
echo "🛑 Arrêt de JARVIS..."
kill $JARVIS_PID 2>/dev/null || true
read -p "Appuyez sur Entrée pour fermer cette fenêtre..."
