#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS AUDIO SIMPLE ET FONCTIONNEL - JEAN-LUC PASSAVE
Interface simplifiée avec audio navigateur uniquement
"""

import gradio as gr
import requests
import datetime

def send_to_real_jarvis(message):
    """Envoie un message au vrai JARVIS sur localhost:8000"""
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json={
                "model": "deepseek-r1:8b",
                "messages": [
                    {"role": "system", "content": "Tu es JARVIS, l'assistant IA expert niveau 20 de <PERSON><PERSON><PERSON>ave. Réponds de manière professionnelle et utile."},
                    {"role": "user", "content": message}
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            return data['choices'][0]['message']['content']
        else:
            return f"Erreur API: {response.status_code}"
            
    except Exception as e:
        return f"Bonjour <PERSON>-Luc ! J'ai reçu votre message : '{message}'. <PERSON> suis JARVIS, votre assistant IA. (Mode test - connexion VLLM indisponible: {e})"

def get_last_jarvis_response(history):
    """Récupère la dernière réponse de JARVIS"""
    if history and len(history) > 0:
        for message in reversed(history):
            if isinstance(message, dict) and message.get('role') == 'assistant':
                content = message.get('content', '')
                if content.startswith('🤖 JARVIS: '):
                    content = content[11:]
                return content
    return "Aucune réponse disponible"

def process_jarvis_message(message, history):
    """Traite un message avec le vrai JARVIS"""
    if not message.strip():
        return history, "", update_thoughts("Aucun message à traiter")
    
    jarvis_response = send_to_real_jarvis(message)
    
    history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
    history.append({"role": "assistant", "content": f"🤖 JARVIS: {jarvis_response}"})
    
    thoughts_html = update_thoughts(f"Message traité: '{message[:30]}...' → Réponse générée")
    
    return history, "", thoughts_html

def update_thoughts(status_text):
    """Met à jour l'affichage des pensées"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    return f"""
    <div id="thoughts-container" style='background: linear-gradient(135deg, #f3e5f5, #e1bee7); padding: 20px; border-radius: 15px; border-left: 6px solid #9C27B0; max-height: 400px; overflow-y: auto; font-size: 1.2em;'>
        <div style='margin: 8px 0; padding: 12px; background: #2c3e50; color: #ecf0f1; border-radius: 8px; font-size: 1.3em; border: 2px solid #9C27B0; line-height: 1.6; animation: fadeIn 0.5s;'>
            <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;'>
                <strong style="color: #9C27B0; font-size: 1.4em;">💭 {current_time}:</strong>
                <button onclick="speakText('{status_text}')" style='background: #9C27B0; color: white; border: none; padding: 8px 12px; border-radius: 8px; cursor: pointer; font-size: 0.9em; font-weight: bold;'>🔊 ÉCOUTER</button>
            </div>
            {status_text}
        </div>
        <div style='margin: 5px 0; padding: 10px; background: #34495e; color: #ecf0f1; border-radius: 5px; font-size: 1.1em; border: 1px solid #27ae60;'>
            <div style='display: flex; justify-content: space-between; align-items: center;'>
                <strong style="color: #27ae60;">🔍 Connexion:</strong>
                <button onclick="speakText('Connexion VLLM active sur localhost 8000')" style='background: #27ae60; color: white; border: none; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.8em;'>🔊</button>
            </div>
            VLLM localhost:8000 opérationnel
        </div>
        <div style='margin: 5px 0; padding: 10px; background: #2c3e50; color: #ecf0f1; border-radius: 5px; font-size: 1.1em; border: 1px solid #e74c3c;'>
            <div style='display: flex; justify-content: space-between; align-items: center;'>
                <strong style="color: #e74c3c;">⚡ Audio:</strong>
                <button onclick="speakText('Synthèse vocale navigateur activée')" style='background: #e74c3c; color: white; border: none; padding: 6px 10px; border-radius: 6px; cursor: pointer; font-size: 0.8em;'>🔊</button>
            </div>
            Synthèse vocale navigateur activée
        </div>
    </div>

    <script>
    // SYNTHÈSE VOCALE NAVIGATEUR - JEAN-LUC PASSAVE
    function speakText(text) {{
        if ('speechSynthesis' in window) {{
            window.speechSynthesis.cancel();
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = 0.85;
            utterance.pitch = 0.8;
            utterance.volume = 1.0;
            
            const voices = window.speechSynthesis.getVoices();
            const frenchVoice = voices.find(voice => 
                voice.lang.includes('fr') || 
                voice.name.toLowerCase().includes('french') ||
                voice.name.toLowerCase().includes('thomas') ||
                voice.name.toLowerCase().includes('male')
            );
            
            if (frenchVoice) {{
                utterance.voice = frenchVoice;
            }}
            
            window.speechSynthesis.speak(utterance);
            console.log('🔊 JARVIS parle:', text);
        }} else {{
            alert('Synthèse vocale non supportée par votre navigateur');
        }}
    }}
    
    // Fonction pour écouter la dernière réponse
    function speakLastResponse() {{
        const chatbot = document.querySelector('#main_chatbot');
        if (chatbot) {{
            const messages = chatbot.querySelectorAll('.message');
            if (messages.length > 0) {{
                const lastMessage = messages[messages.length - 1];
                const text = lastMessage.textContent || lastMessage.innerText;
                if (text.includes('JARVIS:')) {{
                    const jarvisText = text.split('JARVIS:')[1]?.trim();
                    if (jarvisText) {{
                        speakText(jarvisText);
                    }}
                }}
            }}
        }}
    }}
    
    // Charger les voix au démarrage
    if ('speechSynthesis' in window) {{
        speechSynthesis.onvoiceschanged = function() {{
            console.log('🔊 Voix disponibles:', speechSynthesis.getVoices().length);
        }};
    }}
    </script>
    """

def create_simple_audio_interface():
    """Crée l'interface JARVIS audio simple"""
    
    with gr.Blocks(
        title="🔊 JARVIS - Audio Simple Fonctionnel",
        theme=gr.themes.Soft(),
        css="""
        .audio-btn {
            background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.3em !important;
            padding: 15px 30px !important;
            border-radius: 12px !important;
            box-shadow: 0 6px 15px rgba(231, 76, 60, 0.4) !important;
            transition: all 0.3s ease !important;
        }
        .audio-btn:hover {
            background: linear-gradient(45deg, #c0392b, #a93226) !important;
            transform: translateY(-3px) !important;
            box-shadow: 0 10px 20px rgba(231, 76, 60, 0.5) !important;
        }
        """
    ) as interface:
        
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #2c2c2c, #6a4c93, #9c27b0); color: white; padding: 25px; margin: -20px -20px 20px -20px; border-radius: 0 0 15px 15px;">
            <h1 style="margin: 0; font-size: 2.2em;">🔊 JARVIS - Audio Simple Fonctionnel</h1>
            <div style="margin: 15px 0;">
                <span style="display: inline-block; width: 15px; height: 15px; background: #4CAF50; border-radius: 50%; margin-right: 10px; animation: pulse 2s infinite;"></span>
                <span style="font-size: 1.3em; font-weight: bold;">JARVIS ACTIF - Audio Navigateur</span>
            </div>
            <p style="margin: 10px 0 0 0; font-size: 1.1em; opacity: 0.9;">Pensées visibles + Audio fonctionnel</p>
        </div>
        <style>
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        </style>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                main_chat = gr.Chatbot(
                    value=[],
                    height=600,
                    label="💬 Conversation avec JARVIS Expert Niveau 20",
                    show_copy_button=True,
                    avatar_images=("👨‍💻", "🤖"),
                    type="messages",
                    elem_id="main_chatbot"
                )
                
                with gr.Row():
                    user_input = gr.Textbox(
                        value="",
                        label="💬 Votre message à JARVIS",
                        scale=4,
                        lines=2,
                        placeholder="Tapez votre message ici..."
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)
                
                # BOUTONS AUDIO PRINCIPAUX
                with gr.Row():
                    listen_btn = gr.Button(
                        "🔊 ÉCOUTER DERNIÈRE RÉPONSE JARVIS", 
                        elem_classes=["audio-btn"]
                    )
                
                with gr.Row():
                    test_btn = gr.Button("🎧 Test Audio", variant="secondary")
                    clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
            
            with gr.Column(scale=1):
                gr.HTML("<h3 style='color: #9C27B0; margin: 0 0 15px 0; font-size: 1.4em;'>🧠 Pensées JARVIS</h3>")
                
                thoughts_display = gr.HTML(update_thoughts("Interface audio prête - En attente de vos messages"))
        
        # FONCTIONS
        def test_audio():
            return update_thoughts("Test audio lancé - Vérifiez que vous entendez JARVIS parler")
        
        def clear_chat():
            return [], update_thoughts("Conversation effacée - Prêt pour une nouvelle discussion")
        
        def speak_last_response(history):
            last_response = get_last_jarvis_response(history)
            return update_thoughts(f"Audio lancé pour: '{last_response[:50]}...'")
        
        # CONNEXIONS
        send_btn.click(
            fn=process_jarvis_message,
            inputs=[user_input, main_chat],
            outputs=[main_chat, user_input, thoughts_display]
        )
        
        listen_btn.click(
            fn=speak_last_response,
            inputs=[main_chat],
            outputs=[thoughts_display],
            js="speakLastResponse"
        )
        
        test_btn.click(
            fn=test_audio,
            outputs=[thoughts_display],
            js="() => speakText('Bonjour Jean-Luc, ceci est un test audio de JARVIS. L\\'interface fonctionne parfaitement.')"
        )
        
        clear_btn.click(
            fn=clear_chat,
            outputs=[main_chat, thoughts_display]
        )
        
        user_input.submit(
            fn=process_jarvis_message,
            inputs=[user_input, main_chat],
            outputs=[main_chat, user_input, thoughts_display]
        )
    
    return interface

if __name__ == "__main__":
    print("🔊 JARVIS AUDIO SIMPLE ET FONCTIONNEL - JEAN-LUC PASSAVE")
    print("=" * 60)
    print("🤖 Connexion au vrai JARVIS sur localhost:8000")
    print("🔊 Audio navigateur uniquement (plus fiable)")
    print("🧠 Pensées visibles en temps réel")
    print("🌐 Interface disponible sur localhost:8107")
    print("=" * 60)
    
    interface = create_simple_audio_interface()
    interface.launch(
        server_name="localhost",
        server_port=8107,
        share=False,
        debug=True,
        show_error=True
    )
