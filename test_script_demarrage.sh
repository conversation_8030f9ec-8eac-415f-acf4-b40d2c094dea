#!/bin/bash

# 🧪 TEST SCRIPT DE DÉMARRAGE JARVIS - JEAN-LUC PASSAVE
# Vérification que tout est en place pour le lancement

echo "🧪 TEST SCRIPT DE DÉMARRAGE JARVIS"
echo "=================================="

# Aller dans le bon répertoire
cd "$(dirname "$0")"
echo "📁 Répertoire: $(pwd)"

echo ""
echo "🔍 VÉRIFICATION DES FICHIERS ESSENTIELS"
echo "======================================="

# Vérifier les fichiers principaux
FILES_TO_CHECK=(
    "JARVIS_ELECTRON_LAUNCHER.command"
    "jarvis_electron_nouveau.js"
    "jarvis_interface_communication_principale.py"
    "package.json"
    "node_modules/.bin/electron"
)

for file in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$file" ] || [ -d "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - MANQUANT"
    fi
done

echo ""
echo "🔍 VÉRIFICATION DES PORTS"
echo "========================="

# Vérifier si le port 7864 est libre
if lsof -i :7864 > /dev/null 2>&1; then
    echo "⚠️ Port 7864 déjà utilisé"
    echo "🔧 Processus sur port 7864:"
    lsof -i :7864
else
    echo "✅ Port 7864 libre"
fi

echo ""
echo "🔍 VÉRIFICATION PYTHON"
echo "======================"

# Vérifier Python
if command -v python3 > /dev/null; then
    echo "✅ Python3 disponible: $(python3 --version)"
else
    echo "❌ Python3 non trouvé"
fi

# Vérifier les modules Python essentiels
echo "🔍 Modules Python:"
python3 -c "import gradio; print('✅ Gradio disponible')" 2>/dev/null || echo "❌ Gradio manquant"
python3 -c "import requests; print('✅ Requests disponible')" 2>/dev/null || echo "❌ Requests manquant"

echo ""
echo "🔍 VÉRIFICATION NODE.JS"
echo "======================="

# Vérifier Node.js
if command -v node > /dev/null; then
    echo "✅ Node.js disponible: $(node --version)"
else
    echo "❌ Node.js non trouvé"
fi

# Vérifier npm
if command -v npm > /dev/null; then
    echo "✅ npm disponible: $(npm --version)"
else
    echo "❌ npm non trouvé"
fi

echo ""
echo "🔍 VÉRIFICATION PERMISSIONS"
echo "==========================="

# Vérifier les permissions du script de démarrage
if [ -x "JARVIS_ELECTRON_LAUNCHER.command" ]; then
    echo "✅ Script de démarrage exécutable"
else
    echo "❌ Script de démarrage non exécutable"
    echo "🔧 Correction des permissions..."
    chmod +x JARVIS_ELECTRON_LAUNCHER.command
    echo "✅ Permissions corrigées"
fi

echo ""
echo "📋 RÉSUMÉ DU TEST"
echo "================="

echo "🎯 Interface JARVIS Dashboard: port 7864"
echo "🖥️ Application Electron: jarvis_electron_nouveau.js"
echo "🚀 Script de démarrage: JARVIS_ELECTRON_LAUNCHER.command"
echo ""
echo "✅ Test terminé. Vous pouvez maintenant double-cliquer sur:"
echo "   JARVIS_ELECTRON_LAUNCHER.command"
echo ""
echo "📝 Note: L'interface violette validée sera accessible sur http://localhost:7864"
