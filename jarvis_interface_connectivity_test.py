#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📡 JARVIS INTERFACE CONNECTIVITÉ TEST
====================================
Interface de test pour les boutons Wi-Fi, Bluetooth, AirDrop
"""

import gradio as gr
from jarvis_connectivity_controls import (
    get_connectivity_manager, create_connectivity_buttons_html
)

def create_connectivity_test_interface():
    """Crée l'interface de test pour la connectivité"""
    
    connectivity_manager = get_connectivity_manager()
    
    def toggle_wifi():
        result = connectivity_manager.toggle_wifi()
        return result["message"], get_status_display()
    
    def toggle_bluetooth():
        result = connectivity_manager.toggle_bluetooth()
        return result["message"], get_status_display()
    
    def open_airdrop():
        result = connectivity_manager.open_airdrop()
        return result["message"], get_status_display()
    
    def get_status_display():
        status = connectivity_manager.get_status_summary()
        return f"""
📡 **STATUT CONNECTIVITÉ JARVIS**

📶 **Wi-Fi**: {status['wifi']['icon']} {'Activé' if status['wifi']['status'] else 'Désactivé'}
🔵 **Bluetooth**: {status['bluetooth']['icon']} {'Activé' if status['bluetooth']['status'] else 'Désactivé'}  
📡 **AirDrop**: {status['airdrop']['icon']} {'Disponible' if status['airdrop']['status'] else 'Indisponible'}

💡 **Utilisez ces boutons pour transférer des fichiers vers JARVIS**
        """
    
    def upload_files(files):
        """Gère l'upload de fichiers vers JARVIS"""
        if files is None:
            return "❌ Aucun fichier sélectionné"
        
        file_names = []
        for file in files:
            file_names.append(file.name)
        
        return f"✅ Fichiers reçus par JARVIS: {', '.join(file_names)}"
    
    with gr.Blocks(
        title="📡 JARVIS Connectivité",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
        }
        .gr-button-primary {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            font-size: 16px;
            font-weight: bold;
        }
        .connectivity-buttons {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 15px;
            display: flex;
            gap: 10px;
            backdrop-filter: blur(10px);
        }
        """
    ) as interface:
        
        gr.HTML(f"""
        <div style="text-align: center; padding: 25px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; margin-bottom: 20px;">
            <h1 style="color: white; margin: 0; font-size: 32px;">📡 JARVIS CONNECTIVITÉ</h1>
            <p style="color: white; margin: 15px 0 0 0; font-size: 18px;">Contrôles Wi-Fi, Bluetooth, AirDrop & Transfert de Fichiers</p>
        </div>
        
        {create_connectivity_buttons_html()}
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                gr.HTML("<h3>📡 Contrôles de Connectivité</h3>")
                
                with gr.Row():
                    wifi_btn = gr.Button("📶 Basculer Wi-Fi", variant="primary")
                    bluetooth_btn = gr.Button("🔵 Basculer Bluetooth", variant="primary")
                    airdrop_btn = gr.Button("📡 Ouvrir AirDrop", variant="primary")
                
                gr.HTML("<h3>📁 Transfert de Fichiers vers JARVIS</h3>")
                
                file_upload = gr.File(
                    label="Sélectionner fichiers pour JARVIS",
                    file_count="multiple",
                    file_types=None
                )
                
                upload_btn = gr.Button("📤 Transférer vers JARVIS", variant="primary")
                
            with gr.Column(scale=3):
                gr.HTML("<h3>📊 Statut & Résultats</h3>")
                
                result_output = gr.Textbox(
                    label="Résultat des actions",
                    lines=3,
                    interactive=False
                )
                
                status_output = gr.HTML(
                    value=get_status_display()
                )
        
        # Actions des boutons
        wifi_btn.click(
            toggle_wifi,
            outputs=[result_output, status_output]
        )
        
        bluetooth_btn.click(
            toggle_bluetooth,
            outputs=[result_output, status_output]
        )
        
        airdrop_btn.click(
            open_airdrop,
            outputs=[result_output, status_output]
        )
        
        upload_btn.click(
            upload_files,
            inputs=[file_upload],
            outputs=[result_output]
        )
        
        # Actualisation automatique du statut
        interface.load(lambda: get_status_display(), outputs=status_output)
    
    return interface

if __name__ == "__main__":
    print("📡 LANCEMENT INTERFACE CONNECTIVITÉ JARVIS")
    print("=" * 60)
    
    interface = create_connectivity_test_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7888,
        share=False,
        show_error=True,
        quiet=False
    )
