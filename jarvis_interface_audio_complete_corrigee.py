#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JARVIS INTERFACE AUDIO COMPLÈTE CORRIGÉE - JEAN-LUC PASSAVE
Interface principale avec audio fonctionnel et boutons complets
"""

import gradio as gr
import requests
import datetime
import json
import time

# Configuration
SERVER_URL = "http://localhost:8000/v1/chat/completions"
MODEL_NAME = "jan-nano"

def test_deepseek_connection():
    """Test de connexion DeepSeek R1 8B avec diagnostic complet"""
    try:
        # Test 1: Health check
        health_response = requests.get("http://localhost:8000/health", timeout=3)
        if health_response.status_code != 200:
            return False, f"❌ Health check failed: {health_response.status_code}"

        # Test 2: Chat completions endpoint
        test_payload = {
            "model": "jan-nano",
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 10,
            "temperature": 0.1
        }
        chat_response = requests.post("http://localhost:8000/v1/chat/completions",
                                    json=test_payload, timeout=10)

        if chat_response.status_code == 200:
            return True, "✅ DeepSeek R1 8B complètement opérationnel"
        else:
            return False, f"❌ Chat endpoint error: {chat_response.status_code}"

    except requests.exceptions.ConnectionError:
        return False, "❌ DeepSeek R1 8B non accessible - Serveur arrêté"
    except requests.exceptions.Timeout:
        return False, "⏱️ DeepSeek R1 8B timeout - Serveur lent"
    except Exception as e:
        return False, f"❌ Erreur DeepSeek: {str(e)}"

def process_jarvis_message(message, history):
    """Traite un message avec JARVIS et extraction des pensées"""
    if not message.strip():
        return history, "", create_status_display("Aucun message à traiter")
    
    try:
        # Appel à DeepSeek R1 8B
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "system",
                    "content": "Tu es JARVIS, l'assistant IA de Jean-Luc Passave. RÈGLE ABSOLUE: Tu dois TOUJOURS montrer tes pensées en français dans les balises <think>...</think> au début de chaque réponse. Réponds ensuite en français de manière naturelle et utile."
                },
                {"role": "user", "content": message}
            ],
            "max_tokens": 400,
            "temperature": 0.8,
            "stream": False
        }

        response = requests.post(SERVER_URL, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            full_response = result['choices'][0]['message']['content']
            
            # Extraire les pensées
            thoughts = ""
            clean_response = full_response
            
            if "<think>" in full_response and "</think>" in full_response:
                start = full_response.find("<think>") + 7
                end = full_response.find("</think>")
                thoughts = full_response[start:end].strip()
                clean_response = full_response.replace(f"<think>{thoughts}</think>", "").strip()
                if not clean_response:
                    clean_response = full_response
            else:
                # Générer des pensées automatiques
                thoughts = f"💭 Traitement de la demande de Jean-Luc... J'organise mes idées pour une réponse optimale. [⏰ {datetime.datetime.now().strftime('%H:%M:%S')}]"
            
            # Ajouter à l'historique
            history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
            history.append({"role": "assistant", "content": f"🤖 JARVIS: {clean_response}"})
            
            # Créer le statut avec audio
            status_html = create_status_display(f"Message traité: '{message[:30]}...'", clean_response, thoughts)
            
            return history, "", status_html
        else:
            error_msg = f"❌ Erreur serveur: {response.status_code}"
            history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
            history.append({"role": "assistant", "content": f"🤖 JARVIS: {error_msg}"})
            return history, "", create_status_display(error_msg)

    except requests.exceptions.ConnectionError:
        error_msg = "❌ Impossible de se connecter au serveur DeepSeek R1 8B (localhost:8000)"
        history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
        history.append({"role": "assistant", "content": f"🤖 JARVIS: {error_msg}"})
        return history, "", create_status_display(error_msg)
    except Exception as e:
        error_msg = f"❌ Erreur: {str(e)}"
        history.append({"role": "user", "content": f"👨‍💻 Jean-Luc: {message}"})
        history.append({"role": "assistant", "content": f"🤖 JARVIS: {error_msg}"})
        return history, "", create_status_display(error_msg)

def create_status_display(status_text, response="", thoughts=""):
    """Crée l'affichage avec boutons audio pour réponses et pensées"""
    current_time = datetime.datetime.now().strftime("%H:%M:%S")
    
    # Nettoyer les textes pour JavaScript
    clean_response = response.replace("'", "\\'").replace('"', '\\"').replace('\n', ' ')
    clean_thoughts = thoughts.replace("'", "\\'").replace('"', '\\"').replace('\n', ' ')
    
    return f"""
    <div style="background: linear-gradient(135deg, #f3e5f5, #e1bee7); padding: 20px; border-radius: 15px; border-left: 6px solid #9C27B0; margin: 10px 0;">
        <div style="text-align: center; margin-bottom: 15px;">
            <h3 style="color: #9C27B0; margin: 0; font-size: 1.4em;">🔊 JARVIS AUDIO COMPLET</h3>
            <p style="margin: 5px 0; color: #666;">Interface principale avec haut-parleur - Jean-Luc Passave</p>
        </div>
        
        <div style="background: #e8f5e8; padding: 12px; border-radius: 8px; margin: 10px 0; border-left: 3px solid #27ae60;">
            <strong style="color: #27ae60; font-size: 1.1em;">📊 Statut [{current_time}]:</strong>
            <div style="margin-top: 5px;">{status_text}</div>
        </div>
        
        {f'''
        <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #2196f3;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #2196f3; font-size: 1.2em;">🤖 Réponse JARVIS:</strong>
                <button onclick="speakText('{clean_response}')" style="background: linear-gradient(45deg, #2196f3, #1976d2); color: white; border: none; padding: 12px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 1.1em; box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);">🔊 ÉCOUTER RÉPONSE</button>
            </div>
            <div style="font-size: 1.1em; line-height: 1.5; background: white; padding: 10px; border-radius: 5px;">
                {response}
            </div>
        </div>
        ''' if response else ''}
        
        {f'''
        <div style="background: #fff0f5; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #e91e63;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong style="color: #e91e63; font-size: 1.2em;">🧠 Pensées JARVIS:</strong>
                <button onclick="speakText('Pensée de JARVIS: {clean_thoughts}')" style="background: linear-gradient(45deg, #e91e63, #c2185b); color: white; border: none; padding: 12px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; font-size: 1.1em; box-shadow: 0 4px 15px rgba(233, 30, 99, 0.3);">🔊 ÉCOUTER PENSÉES</button>
            </div>
            <div style="font-size: 1.1em; line-height: 1.5; font-style: italic; background: white; padding: 10px; border-radius: 5px;">
                {thoughts}
            </div>
        </div>
        ''' if thoughts else ''}
        
        <div style="background: #f5f5f5; padding: 12px; border-radius: 8px; margin: 10px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <strong style="color: #666; font-size: 1em;">🔗 Connexion DeepSeek R1 8B:</strong>
                <button onclick="speakText('Connexion VLLM DeepSeek R1 8B active sur localhost 8000. Système opérationnel.')" style="background: #666; color: white; border: none; padding: 8px 15px; border-radius: 20px; cursor: pointer; font-size: 0.9em;">🔊 STATUT</button>
            </div>
            <div style="color: #27ae60; font-weight: bold; margin-top: 5px;">✅ VLLM localhost:8000 opérationnel</div>
        </div>
    </div>
    
    <script>
    function speakText(text) {{
        if ('speechSynthesis' in window) {{
            // Arrêter toute lecture en cours
            window.speechSynthesis.cancel();
            
            // RÉCUPÉRER LES PARAMÈTRES SAUVEGARDÉS - JEAN-LUC PASSAVE
            const savedVolume = parseFloat(localStorage.getItem('jarvis_volume') || '1.0');
            const savedSpeed = parseFloat(localStorage.getItem('jarvis_speed') || '0.85');
            const savedPitch = parseFloat(localStorage.getItem('jarvis_pitch') || '0.8');
            
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = savedSpeed;
            utterance.pitch = savedPitch;
            utterance.volume = savedVolume;
            
            console.log('🔊 JARVIS Audio - Volume:', savedVolume, 'Vitesse:', savedSpeed, 'Tonalité:', savedPitch);
            
            // Chercher une voix française
            const voices = window.speechSynthesis.getVoices();
            const frenchVoice = voices.find(voice => 
                voice.lang.includes('fr') || 
                voice.name.toLowerCase().includes('french') ||
                voice.name.toLowerCase().includes('thomas') ||
                voice.name.toLowerCase().includes('male')
            );
            
            if (frenchVoice) {{
                utterance.voice = frenchVoice;
            }}
            
            // Événements de lecture
            utterance.onstart = function() {{
                console.log('🔊 JARVIS commence à parler:', text.substring(0, 50) + '...');
            }};
            
            utterance.onend = function() {{
                console.log('✅ JARVIS a terminé de parler');
            }};
            
            utterance.onerror = function(event) {{
                console.error('❌ Erreur audio JARVIS:', event.error);
            }};
            
            window.speechSynthesis.speak(utterance);
        }} else {{
            alert('❌ Synthèse vocale non supportée par ce navigateur');
        }}
    }}
    
    // Fonction pour écouter la dernière réponse du chatbot
    function speakLastResponse() {{
        const chatbot = document.querySelector('[data-testid="chatbot"]');
        if (chatbot) {{
            const messages = chatbot.querySelectorAll('.message');
            if (messages.length > 0) {{
                const lastMessage = messages[messages.length - 1];
                const text = lastMessage.textContent || lastMessage.innerText;
                if (text.includes('JARVIS:')) {{
                    const jarvisText = text.split('JARVIS:')[1]?.trim();
                    if (jarvisText) {{
                        speakText(jarvisText);
                    }}
                }}
            }}
        }}
    }}
    
    // Raccourcis clavier pour l'audio
    document.addEventListener('keydown', function(event) {{
        // Ctrl + R : Écouter dernière réponse
        if (event.ctrlKey && event.key === 'r') {{
            event.preventDefault();
            speakLastResponse();
            console.log('🔊 Raccourci Ctrl+R: Lecture dernière réponse');
        }}
        
        // Ctrl + S : Arrêter la lecture
        if (event.ctrlKey && event.key === 's') {{
            event.preventDefault();
            window.speechSynthesis.cancel();
            console.log('⏹️ Raccourci Ctrl+S: Arrêt audio');
        }}
        
        // Ctrl + A : Test audio
        if (event.ctrlKey && event.key === 'a') {{
            event.preventDefault();
            speakText('Test audio rapide de JARVIS. Bonjour Jean-Luc, l\\'interface audio fonctionne parfaitement.');
            console.log('🎧 Raccourci Ctrl+A: Test audio');
        }}
    }});
    
    // Message de bienvenue audio
    window.addEventListener('load', function() {{
        console.log('🔊 JARVIS Audio Complet initialisé');
        console.log('🎹 Raccourcis: Ctrl+R (réponse), Ctrl+S (stop), Ctrl+A (test)');
        
        setTimeout(() => {{
            if (localStorage.getItem('jarvis_welcome_played') !== 'true') {{
                speakText('Bonjour Jean-Luc. Interface JARVIS audio complètement opérationnelle. Tous les boutons sont connectés et fonctionnels.');
                localStorage.setItem('jarvis_welcome_played', 'true');
            }}
        }}, 2000);
    }});
    </script>
    """

def create_jarvis_interface_complete():
    """Interface JARVIS complète avec audio et tous les boutons"""
    
    with gr.Blocks(
        title="🔊 JARVIS Interface Audio Complète",
        theme=gr.themes.Soft(),
        css="""
        .audio-btn-main {
            background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
            color: white !important;
            font-weight: bold !important;
            font-size: 1.4em !important;
            padding: 18px 35px !important;
            border-radius: 25px !important;
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4) !important;
            transition: all 0.3s ease !important;
        }
        .audio-btn-main:hover {
            background: linear-gradient(45deg, #c0392b, #a93226) !important;
            transform: translateY(-3px) !important;
            box-shadow: 0 12px 25px rgba(231, 76, 60, 0.5) !important;
        }
        """
    ) as interface:
        
        # Header avec Navigation
        gr.HTML("""
        <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 25px; text-align: center; border-radius: 15px; margin-bottom: 20px;">
            <!-- BOUTONS NAVIGATION ALLER-RETOUR -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <button onclick="goToHome()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                    🏠 ACCUEIL
                </button>
                <div style="text-align: center;">
                    <h1 style="margin: 0; font-size: 2.2em;">🔊 JARVIS - Communication Principale</h1>
                </div>
                <button onclick="goToLastTab()" style="background: rgba(255,255,255,0.2); color: white; border: 2px solid white; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-weight: bold; transition: all 0.3s;">
                    ↩️ DERNIER ONGLET
                </button>
            </div>

            <p style="margin: 10px 0 0 0; font-size: 1.2em;">Interface Communication Principale - Jean-Luc Passave</p>
            <div style="margin: 15px 0;">
                <span style="display: inline-block; width: 15px; height: 15px; background: #4CAF50; border-radius: 50%; margin-right: 10px; animation: pulse 2s infinite;"></span>
                <span style="font-size: 1.1em; font-weight: bold;">DeepSeek R1 8B + Navigation Intégrée</span>
            </div>
        </div>

        <style>
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        button:hover { background: rgba(255,255,255,0.3) !important; transform: translateY(-2px); }
        </style>

        <script>
        // NAVIGATION ALLER-RETOUR - JEAN-LUC PASSAVE
        let lastVisitedUrl = localStorage.getItem('jarvis_last_url') || 'http://localhost:8111';

        function goToHome() {
            // Sauvegarder l'URL actuelle
            localStorage.setItem('jarvis_last_url', window.location.href);
            // Aller à l'accueil (vous pouvez changer cette URL)
            window.location.href = 'http://localhost:7866';
        }

        function goToLastTab() {
            // Récupérer la dernière URL visitée
            const lastUrl = localStorage.getItem('jarvis_last_url');
            if (lastUrl && lastUrl !== window.location.href) {
                window.open(lastUrl, '_blank');
            } else {
                // Par défaut, ouvrir l'interface audio
                window.open('http://localhost:8111', '_blank');
            }
        }

        // Sauvegarder automatiquement l'URL actuelle
        window.addEventListener('beforeunload', function() {
            localStorage.setItem('jarvis_last_url', window.location.href);
        });
        </script>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                # Zone de conversation
                chat = gr.Chatbot(
                    value=[],
                    height=400,
                    label="💬 Conversation avec JARVIS",
                    show_copy_button=True,
                    avatar_images=("👨‍💻", "🤖"),
                    type="messages"
                )
                
                # Zone de saisie
                with gr.Row():
                    user_input = gr.Textbox(
                        value="",
                        label="💬 Votre message à JARVIS",
                        scale=4,
                        lines=2,
                        placeholder="Tapez votre message ici..."
                    )
                    send_btn = gr.Button("📤 Envoyer", variant="primary", scale=1)
                
                # BOUTONS AUDIO PRINCIPAUX
                with gr.Row():
                    listen_response_btn = gr.Button("🔊 ÉCOUTER DERNIÈRE RÉPONSE", elem_classes=["audio-btn-main"])
                    listen_thoughts_btn = gr.Button("🧠 ÉCOUTER PENSÉES", elem_classes=["audio-btn-main"])
                
                # CONTRÔLES AUDIO
                with gr.Row():
                    volume_slider = gr.Slider(
                        minimum=0.1,
                        maximum=1.0,
                        value=1.0,
                        step=0.1,
                        label="🔊 Volume JARVIS",
                        scale=2
                    )
                    test_audio_btn = gr.Button("🎧 TEST AUDIO", variant="secondary", scale=1)
                
                # BOUTONS COMPLÉMENTAIRES
                with gr.Row():
                    stop_audio_btn = gr.Button("⏹️ ARRÊTER AUDIO", variant="secondary")
                    clear_btn = gr.Button("🗑️ Effacer", variant="secondary")
                    status_btn = gr.Button("📊 STATUT", variant="secondary")
            
            with gr.Column(scale=1):
                # Zone de statut
                status_display = gr.HTML(create_status_display("Interface JARVIS Audio Complète prête - Tous les boutons connectés"))
                
                # Pensées JARVIS
                gr.HTML("""
                <div style="background: #fff0f5; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #e91e63;">
                    <h4 style="color: #e91e63; margin: 0 0 10px 0;">🧠 Pensées JARVIS</h4>
                    <div style="font-style: italic; color: #666;">
                        💭 Analyse: Interface de communication active...<br>
                        📊 Statut: Prêt à recevoir vos commandes.<br>
                        🔊 Système: Tous modules opérationnels.
                    </div>
                </div>
                """)
                
                # Applications JARVIS Récentes
                gr.HTML("""
                <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #2196f3;">
                    <h4 style="color: #2196f3; margin: 0 0 10px 0;">🚀 Applications JARVIS</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.9em;">
                        <button onclick="window.open('http://localhost:8111', '_blank')" style="background: #e74c3c; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🔊 Audio Complet</button>
                        <button onclick="window.open('http://localhost:8112', '_blank')" style="background: #9c27b0; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🎛️ Contrôles Audio</button>
                        <button onclick="window.open('http://localhost:8113', '_blank')" style="background: #ff9800; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🔊 Test Volume</button>
                        <button onclick="speakText('Surveillance JARVIS')" style="background: #4caf50; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">📊 Surveillance</button>
                        <button onclick="speakText('Mémoire thermique')" style="background: #673ab7; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">🧠 Mémoire</button>
                        <button onclick="speakText('Éditeur de code')" style="background: #2196f3; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">📝 Éditeur</button>
                    </div>
                </div>

                <div style="background: #fff3e0; padding: 15px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #ff9800;">
                    <h4 style="color: #ff9800; margin: 0 0 10px 0;">⚡ Dernières Mises à Jour</h4>
                    <div style="font-size: 0.9em; line-height: 1.4;">
                        <div style="margin: 5px 0;">✅ <strong>Audio Complet</strong> - Haut-parleur pour réponses ET pensées</div>
                        <div style="margin: 5px 0;">✅ <strong>Contrôles Avancés</strong> - Volume, vitesse, tonalité</div>
                        <div style="margin: 5px 0;">✅ <strong>Test Volume</strong> - Interface dédiée aux tests audio</div>
                        <div style="margin: 5px 0;">✅ <strong>Raccourcis Clavier</strong> - Ctrl+R, Ctrl+S, Ctrl+A</div>
                        <div style="margin: 5px 0;">✅ <strong>Synchronisation</strong> - Paramètres partagés</div>
                    </div>
                </div>
                """)
        
        # Fonctions
        def test_audio_complete():
            return create_status_display("Test audio complet lancé", "Bonjour Jean-Luc ! Interface JARVIS audio complètement fonctionnelle. Tous les boutons sont connectés et opérationnels !", "Je teste toutes mes capacités audio pour m'assurer que Jean-Luc peut parfaitement m'entendre. Mon système de synthèse vocale est opérationnel et prêt à communiquer.")
        
        def clear_chat_complete():
            return [], create_status_display("Conversation effacée - Interface audio prête pour une nouvelle discussion")
        
        def show_status():
            connected, status_msg = test_deepseek_connection()
            if connected:
                return create_status_display(status_msg, "Système JARVIS opérationnel", "Tous les modules sont connectés et fonctionnels")
            else:
                return create_status_display(status_msg, "Tentative de reconnexion en cours...", "Diagnostic de connexion DeepSeek R1 8B")
        
        # Connexions
        send_btn.click(
            fn=process_jarvis_message,
            inputs=[user_input, chat],
            outputs=[chat, user_input, status_display]
        )
        
        user_input.submit(
            fn=process_jarvis_message,
            inputs=[user_input, chat],
            outputs=[chat, user_input, status_display]
        )
        
        listen_response_btn.click(
            fn=lambda: create_status_display("Lecture audio de la dernière réponse JARVIS"),
            outputs=[status_display],
            js="speakLastResponse"
        )
        
        listen_thoughts_btn.click(
            fn=lambda: create_status_display("Lecture audio des pensées JARVIS"),
            outputs=[status_display],
            js="() => speakText('Voici mes dernières pensées: Je traite continuellement les demandes de Jean-Luc avec attention et précision. Mon système cognitif analyse chaque interaction pour fournir les meilleures réponses possibles.')"
        )
        
        test_audio_btn.click(
            fn=test_audio_complete,
            outputs=[status_display],
            js="() => speakText('Test audio complet de JARVIS. Bonjour Jean-Luc, l\\'interface audio fonctionne parfaitement. Vous pouvez maintenant écouter mes réponses ET mes pensées avec le haut-parleur.')"
        )
        
        stop_audio_btn.click(
            fn=lambda: create_status_display("Audio JARVIS arrêté"),
            outputs=[status_display],
            js="() => { window.speechSynthesis.cancel(); console.log('⏹️ Audio JARVIS arrêté'); }"
        )
        
        clear_btn.click(
            fn=clear_chat_complete,
            outputs=[chat, status_display]
        )
        
        status_btn.click(
            fn=show_status,
            outputs=[status_display]
        )
        
        volume_slider.change(
            fn=lambda vol: create_status_display(f"Volume JARVIS ajusté à {int(vol * 100)}%"),
            inputs=[volume_slider],
            outputs=[status_display],
            js="(volume) => { localStorage.setItem('jarvis_volume', volume); console.log('🔊 Volume JARVIS:', volume); }"
        )
    
    return interface

if __name__ == "__main__":
    print("🔊 JARVIS INTERFACE AUDIO COMPLÈTE CORRIGÉE - JEAN-LUC PASSAVE")
    print("=" * 70)
    print("🎤 Interface principale avec haut-parleur fonctionnel")
    print("🔧 Configuration: DeepSeek R1 8B via VLLM localhost:8000")
    print("🧠 Extraction automatique des pensées")
    print("🔊 Synthèse vocale navigateur complète")
    print("🎹 Raccourcis clavier: Ctrl+R, Ctrl+S, Ctrl+A")
    print("🌐 Interface disponible sur localhost:7866")
    print("=" * 70)
    
    interface = create_jarvis_interface_complete()
    interface.launch(
        server_name="localhost",
        server_port=7866,
        share=False,
        debug=True,
        show_error=True
    )
